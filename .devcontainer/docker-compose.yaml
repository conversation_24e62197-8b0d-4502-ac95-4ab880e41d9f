version: '3.8'

services:
  restful-api:
    build:
      context: ..
      dockerfile: .docker/Dockerfile
    volumes:
      - ../src:/app/src
    ports:
      - "888:888"
    depends_on:
      - mysql
      - redis
      - dynamodb
    environment:
      MYSQL_HOST: mysql
      MYSQL_PORT: 3306
      MYSQL_USER: root
      MYSQL_PASSWORD: PSword!123
      MYSQL_DB: golf_teamdb
      MYSQL_CONN: root:PSword!123@tcp(mysql:3306)/golf_teamdb
      MYSQL_MNCDB_CONN: root:PSword!123@tcp(mysql:3306)/mncdb
      SECRET_KEY: SecretYouShouldHide
      ACCESS_TOKEN_TTL: 3600
      REFRESH_TOKEN_TTL: 604800
      REDIS_DB: 0
      REDIS_POOL_SIZE: 1
      REDIS_HOST: redis
      REDIS_PASSWORD:
      REDIS_PORT: 6379
      AUDIT_ON: on
      AUDIT_SYNC_ON: on
      DYNAMODB_AUDIT_TABLE_NAME: operate_audit
      ACCESS_CONTROL_ALLOW_DOMAIN: "*"

  mysql:
    image: mysql:8.0
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: PSword!123
      MYSQL_DATABASE: golf_teamdb
    ports:
      - "3309:3306"
    volumes:
      - db-data:/var/lib/mysql
      - ./initdb.sql:/docker-entrypoint-initdb.d/initdb.sql
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_general_ci
    healthcheck:
      test: [ "CMD", "mysqladmin", "ping", "-h", "localhost", "-pPSword!123" ]
      interval: 10s
      timeout: 5s
      retries: 5
  redis:
    image: redis:latest
    ports:
      - "6379:6379"
  dynamodb:
    image: amazon/dynamodb-local:latest
    ports:
      - "8000:8000"
    volumes:
      - dynamodb-data:/home/<USER>

volumes:
  db-data:
  dynamodb-data:
