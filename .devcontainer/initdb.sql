CREATE DATABASE IF NOT EXISTS golf_teamdb CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
ALTER DATABASE golf_teamdb CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci;

-- dependon mncdb 

CREATE DATABASE IF NOT EXISTS `mncdb` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

CREATE TABLE  IF NOT EXISTS  `mncdb`.`holiday` (
  `holiday_id` int(10) unsigned NOT NULL COMMENT '祝日ID',
  `holiday_date` date NOT NULL COMMENT '祝日',
  `holiday_name` varchar(45) NOT NULL COMMENT '祝日名',
  `created_user_id` int(10) unsigned NOT NULL COMMENT '作成ユーザID',
  `created_at` timestamp NULL DEFAULT NULL COMMENT 'データ作成日時',
  `updated_user_id` int(10) unsigned DEFAULT NULL COMMENT 'データ更新ユーザID',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT 'データ更新日時',
  `deleted_user_id` int(11) DEFAULT NULL COMMENT '削除ユーザID',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '削除日時',
  PRIMARY KEY (`holiday_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='祝日マスタ';

CREATE TABLE  IF NOT EXISTS `mncdb`.`office` (
  `office_id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '拠点ID',
  `office_key` varchar(45) NOT NULL COMMENT '拠点KEY名',
  `office_name` varchar(256) NOT NULL COMMENT '拠点名',
  `office_name_kana` varchar(256) NOT NULL,
  `group_id` int(10) unsigned DEFAULT NULL COMMENT 'グループID',
  `group_label_id` int(10) unsigned DEFAULT NULL COMMENT 'グループラベルID',
  `zip_code` varchar(45) NOT NULL COMMENT '郵便番号',
  `prefectures_id` int(10) unsigned NOT NULL COMMENT '都道府県ID',
  `address_1` varchar(256) NOT NULL COMMENT '住所1',
  `address_2` varchar(256) DEFAULT NULL COMMENT '住所2',
  `office_type_id` int(10) unsigned NOT NULL COMMENT '拠点タイプID',
  `need_cm_revision_update` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'CMリビジョン更新が必要か?(0:不要,1:必要)',
  `contract_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '契約状態（0:未契約、1:契約済み、2:契約停止中、3:未契約(連携済)）',
  `product_type_id` int(10) unsigned NOT NULL DEFAULT '1' COMMENT '製品タイプ',
  `product_subtype_id` int(10) unsigned DEFAULT NULL COMMENT '製品サブタイプ	',
  `office_unique_id` int(13) unsigned DEFAULT NULL COMMENT 'オフィスユニークID',
  `password_valid_days` int(10) unsigned DEFAULT '0' COMMENT 'パスワード有効日数',
  `password_on_off` tinyint(1) unsigned DEFAULT '0' COMMENT 'パスワード期間有効無効(0:無効,1:有効)',
  `sort_id` int(10) unsigned DEFAULT '0' COMMENT 'ゴルフ場並び順',
  `evaluate_start_date` date DEFAULT NULL COMMENT '評価開始日',
  `evaluate_end_date` date DEFAULT NULL COMMENT '評価終了日',
  `created_user_id` int(10) unsigned NOT NULL COMMENT '作成ユーザID',
  `created_at` timestamp NULL DEFAULT NULL COMMENT 'データ作成日時',
  `updated_user_id` int(10) unsigned DEFAULT NULL COMMENT 'データ更新ユーザID',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT 'データ更新日時',
  `deleted_user_id` int(11) DEFAULT NULL COMMENT '削除ユーザID',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '削除日時',
  PRIMARY KEY (`office_id`),
  KEY `fk_office_prefectures_idx` (`prefectures_id`),
  KEY `fk_office_office_type1_idx` (`office_type_id`),
  KEY `fk_office_product_type_idx` (`product_type_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='拠点マスタ';



CREATE TABLE  IF NOT EXISTS  `mncdb`.`product_type` (
  `product_type_id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '製品タイプID',
  `product_type_name` varchar(256) NOT NULL COMMENT '製品タイプ名',
  `restiction_tags` text COMMENT '機能制限タグ',
  `created_user_id` int(10) unsigned NOT NULL COMMENT '作成ユーザID',
  `created_at` timestamp NULL DEFAULT NULL COMMENT 'データ作成日時',
  `updated_user_id` int(10) unsigned DEFAULT NULL COMMENT 'データ更新ユーザID',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT 'データ更新日時',
  `deleted_user_id` int(10) DEFAULT NULL COMMENT '削除ユーザID',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '削除日時',
  PRIMARY KEY (`product_type_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='製品タイプ';


-- 配置功能子项表
CREATE TABLE  IF NOT EXISTS  `mncdb`.`product_subtype` (
  `product_type_id` int(10) unsigned NOT NULL COMMENT '製品タイプID',
  `product_subtype_id` int(10) unsigned NOT NULL COMMENT '製品サブタイプID',
  `product_subtype_name` varchar(256) NOT NULL COMMENT '製品サブタイプ名',
  `restiction_tags` text COMMENT '機能制限タグ',
  `created_user_id` int(10) unsigned NOT NULL COMMENT '作成ユーザID',
  `created_at` timestamp NULL DEFAULT NULL COMMENT 'データ作成日時',
  `updated_user_id` int(10) unsigned DEFAULT NULL COMMENT 'データ更新ユーザID',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT 'データ更新日時',
  `deleted_user_id` int(10) DEFAULT NULL COMMENT '削除ユーザID',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '削除日時',
  PRIMARY KEY (`product_type_id`,`product_subtype_id`),
  CONSTRAINT `fk_product_type` FOREIGN KEY (`product_type_id`) REFERENCES `product_type` (`product_type_id`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='製品サブタイプ';


CREATE TABLE IF NOT EXISTS mncdb.online_compe_indices (
  id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  office_key VARCHAR(64) NOT NULL,
  compe_id INT NOT NULL,
  compe_name VARCHAR(512) NOT NULL,
  compe_type INT NOT NULL,
  aggregation_types VARCHAR(512) NOT NULL,
  time_zone VARCHAR(32) NOT NULL,
  started_at timestamp NOT NULL,
  ended_at timestamp NOT NULL,
  created_at timestamp NOT NULL,
  INDEX idx_online_compe_indices_office_key (office_key),
  INDEX idx_online_compe_indices_compe_id (compe_id),
  INDEX idx_online_compe_indices_compe_type (compe_type),
  INDEX idx_online_compe_indices_started_at (started_at),
  INDEX idx_online_compe_indices_ended_at (ended_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `mncdb`.`office`(`office_id`, `office_key`, `office_name`, `office_name_kana`, `group_id`, `group_label_id`, `zip_code`, `prefectures_id`, `address_1`, `address_2`, `office_type_id`, `need_cm_revision_update`, `contract_status`, `product_type_id`, `product_subtype_id`, `office_unique_id`, `password_valid_days`, `password_on_off`, `sort_id`, `evaluate_start_date`, `evaluate_end_date`, `created_user_id`, `created_at`, `updated_user_id`, `updated_at`, `deleted_user_id`, `deleted_at`) VALUES (1, 'mi-system', 'system', 'システム', 1, NULL, '953-0012', 15, '新潟市', NULL, 1, 0, 0, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `mncdb`.`product_type`(`product_type_id`, `product_type_name`, `restiction_tags`, `created_user_id`, `created_at`, `updated_user_id`, `updated_at`, `deleted_user_id`, `deleted_at`) VALUES (1, 'Ai', 'HOME,CART_CONTROL,REPORT,DEVICE_MANAGEMENT,CM_MANAGEMENT,USER_MANAGEMENT,GREEN_SETTING,INVADED,SCORE_MANAGEMENT,MANUAL,ONE_TIME,LOG', 0, NULL, 1, '2019-12-13 20:24:01', NULL, NULL);


