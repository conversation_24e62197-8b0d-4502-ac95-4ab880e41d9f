package client

import (
	"context"
	"log"
	"mi-restful-api/enum"
	"os"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
)

func GetDynamodb() *dynamodb.Client {
	deployMode := os.Getenv("DEPLOY_MODE")
	if deployMode == enum.DeployModeLocal {
		return getLocaldb()
	}
	// load default config
	//cfg, err := config.LoadDefaultConfig(context.Background(), config.WithRegion("us-east-2"))
	cfg, err := config.LoadDefaultConfig(context.Background(), config.WithRegion(os.Getenv("AWS_REGION")))
	if err != nil {
		log.Fatalf("unable to load SDK config, %v", err)
	}

	// create DynamoDB client
	return dynamodb.NewFromConfig(cfg)
}
func getLocaldb() *dynamodb.Client {
	cfg, err := config.LoadDefaultConfig(context.TODO(),
		config.WithRegion("us-west-2"), // set local region
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider("fakeMyKeyId", "fakeSecretAccessKey", "")),
		config.WithEndpointResolver(aws.EndpointResolverFunc(
			func(service, region string) (aws.Endpoint, error) {
				if service == dynamodb.ServiceID {
					return aws.Endpoint{
						PartitionID: "aws",
						URL:         "http://dynamodb:8000", // use local DynamoDB Local addr
						//URL:           "http://localhost:8000", // use local DynamoDB Local addr
						SigningRegion: "us-west-2",
					}, nil
				}
				return aws.Endpoint{}, &aws.EndpointNotFoundError{}
			})),
	)

	if err != nil {
		log.Fatalf("unable to load SDK config, %v", err)
	}

	// create DynamoDB client
	return dynamodb.NewFromConfig(cfg)
}

type customEndpointResolver struct {
	endpoint string
}

func (r *customEndpointResolver) ResolveEndpoint(service, region string) (aws.Endpoint, error) {
	if service == dynamodb.ServiceID {
		return aws.Endpoint{
			URL: r.endpoint,
		}, nil
	}
	return aws.Endpoint{}, nil
}

func getLocalDynamoDBClient() *dynamodb.Client {
	// Create static credentials provider
	creds := credentials.NewStaticCredentialsProvider("fakeAccessKeyID", "fakeSecretAccessKey", "")

	// Create a custom endpoint resolver
	endpointResolver := &customEndpointResolver{
		endpoint: "http://***************:8000",
	}

	// Load AWS config with static credentials and custom endpoint
	cfg, err := config.LoadDefaultConfig(context.TODO(),
		config.WithRegion("us-west-2"),
		config.WithCredentialsProvider(creds),
		config.WithEndpointResolver(endpointResolver),
	)
	if err != nil {
		log.Fatalf("unable to load SDK config, %v", err)
	}

	// Create a DynamoDB client
	svc := dynamodb.NewFromConfig(cfg)

	return svc
}
