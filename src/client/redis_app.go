package client

import (
	"mi-restful-api/configs"
	"mi-restful-api/enum"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
)

var AppRedisInstance *redis.Client
var AppRedisClusterInstance *redis.ClusterClient
var AppRedisOnce sync.Once

func GetAppRedisClient() redis.UniversalClient {
	config := configs.GetAppRedisConfig()
	AppRedisOnce.Do(func() {
		if config.Mode == enum.RedisServerModeCluster {
			AppRedisClusterInstance = redis.NewClusterClient(&redis.ClusterOptions{
				Addrs: []string{
					config.Host + ":" + config.Port,
				},
				Password:     config.Password,
				PoolSize:     config.PoolSize,
				MinIdleConns: 2,
				PoolTimeout:  10 * time.Second,
			})
		} else {
			AppRedisInstance = redis.NewClient(&redis.Options{
				Addr:         config.Host + ":" + config.Port,
				Password:     config.Password,
				DB:           config.DB,
				PoolSize:     config.PoolSize,
				MinIdleConns: 2,
				PoolTimeout:  10 * time.Second,
			})
		}

	})

	if config.Mode == enum.RedisServerModeCluster {
		return AppRedisClusterInstance
	} else {
		return AppRedisInstance
	}
}
