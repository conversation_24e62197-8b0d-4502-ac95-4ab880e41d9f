package client

import (
	"mi-restful-api/configs"
	"mi-restful-api/enum"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
)

var redisInstance *redis.Client
var redisOnce sync.Once
var redisClusterInstance *redis.ClusterClient

func GetRedisClient() redis.UniversalClient {
	config := configs.GetRedisConfig()
	redisOnce.Do(func() {
		if config.Mode == enum.RedisServerModeCluster {
			redisClusterInstance = redis.NewClusterClient(&redis.ClusterOptions{
				Addrs: []string{
					config.Host + ":" + config.Port,
				},
				Password:     config.Password,
				PoolSize:     config.PoolSize,
				MinIdleConns: 2,
				PoolTimeout:  10 * time.Second,
			})
		} else {
			redisInstance = redis.NewClient(&redis.Options{
				Addr:         config.Host + ":" + config.Port,
				Password:     config.Password,
				DB:           config.DB,
				PoolSize:     config.PoolSize,
				MinIdleConns: 2,
				PoolTimeout:  10 * time.Second,
			})
		}
	})

	if config.Mode == enum.RedisServerModeCluster {
		return redisClusterInstance
	} else {
		return redisInstance
	}
}
