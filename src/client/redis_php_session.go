package client

import (
	"mi-restful-api/configs"
	"mi-restful-api/enum"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
)

var PHPSessionRedisInstance *redis.Client
var PHPSessionRedisOnce sync.Once
var PHPSessionClusterInstance *redis.ClusterClient

func GetPHPSessionRedisClient() redis.UniversalClient {
	config := configs.GetPHPSessionRedisConfig()
	PHPSessionRedisOnce.Do(func() {
		if config.Mode == enum.RedisServerModeCluster {
			PHPSessionClusterInstance = redis.NewClusterClient(&redis.ClusterOptions{
				Addrs: []string{
					config.Host + ":" + config.Port,
				},
				Password:     config.Password,
				PoolSize:     config.PoolSize,
				MinIdleConns: 2,
				PoolTimeout:  10 * time.Second,
			})
		} else {
			PHPSessionRedisInstance = redis.NewClient(&redis.Options{
				Addr:         config.Host + ":" + config.Port,
				Password:     config.Password,
				DB:           config.DB,
				PoolSize:     config.PoolSize,
				MinIdleConns: 2,
				PoolTimeout:  10 * time.Second,
			})
		}
	})
	if config.Mode == enum.RedisServerModeCluster {
		return PHPSessionClusterInstance
	} else {
		return PHPSessionRedisInstance
	}
}
