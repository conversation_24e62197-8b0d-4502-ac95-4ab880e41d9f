package client

import (
	"fmt"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"os"
	"sync"
)

var dbConn *gorm.DB
var onceMysql sync.Once
var dbConnMu sync.Mutex

func GetDbClient() (*gorm.DB, error) {
	var err error
	dsn := fmt.Sprintf(
		"%s?charset=utf8mb4&parseTime=True&loc=Local&timeout=3s",
		os.Getenv("MYSQL_CONN"),
	)
	onceMysql.Do(func() {
		dbConn, err = ConnDb(dsn)
	})

	db, err := dbConn.DB()
	if err != nil {
		return nil, err
	}
	err = db.Ping()
	if err != nil {
		dbConnMu.Lock()

		db, err = dbConn.DB()
		if err != nil {
			return nil, err
		}
		if err = db.<PERSON>(); err != nil {
			dbConn, err = ConnDb(dsn)
		}
		dbConnMu.Unlock()
	}

	return dbConn, err
}

func ConnDb(dsn string) (*gorm.DB, error) {
	return gorm.Open(mysql.Open(dsn), &gorm.Config{
		//Logger: logger.Default.LogMode(logger.Silent),
		Logger: logger.Default.LogMode(logger.Info),
	})
}

var mncDBConn *gorm.DB
var mncDBOnceMysql sync.Once
var mncDBConnMu sync.Mutex

func GetMncDBClient() (*gorm.DB, error) {
	var err error
	dsn := fmt.Sprintf(
		"%s?charset=utf8mb4&parseTime=True&loc=Local&timeout=3s",
		os.Getenv("MYSQL_MNCDB_CONN"),
	)
	mncDBOnceMysql.Do(func() {
		mncDBConn, err = ConnDb(dsn)
	})

	db, err := mncDBConn.DB()
	if err != nil {
		return nil, err
	}
	err = db.Ping()
	if err != nil {
		mncDBConnMu.Lock()

		db, err = mncDBConn.DB()
		if err != nil {
			return nil, err
		}
		if err = db.Ping(); err != nil {
			mncDBConn, err = ConnDb(dsn)
		}
		mncDBConnMu.Unlock()
	}

	return mncDBConn, err
}
