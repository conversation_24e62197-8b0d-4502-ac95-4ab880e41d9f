package configs

import (
	"os"
	"strconv"
	"strings"
	"sync"
)

type Redis struct {
	Host     string
	Password string
	Port     string
	DB       int
	PoolSize int
	Mode     string
}

var RedisConfigInstance *Redis
var RedisConfigOnce sync.Once

func GetRedisConfig() *Redis {
	RedisConfigOnce.Do(func() {
		db, err := strconv.ParseInt(os.Getenv("REDIS_DB"), 10, 8)
		if err != nil {
			panic("redis db is invalid")
		}
		poolSize, err := strconv.ParseInt(os.Getenv("REDIS_POOL_SIZE"), 10, 8)
		if err != nil {
			poolSize = 0
		}
		RedisConfigInstance = &Redis{
			Host:     os.Getenv("REDIS_HOST"),
			Password: strings.TrimSpace(os.Getenv("REDIS_PASSWORD")),
			Port:     os.Getenv("REDIS_PORT"),
			Mode:     os.Getenv("REDIS_MODE"),
			DB:       int(db),
			PoolSize: int(poolSize),
		}
	})
	return RedisConfigInstance
}

var AppRedisConfigInstance *Redis
var AppRedisConfigOnce sync.Once

func GetAppRedisConfig() *Redis {
	AppRedisConfigOnce.Do(func() {
		db, err := strconv.ParseInt(os.Getenv("APP_REDIS_DB"), 10, 8)
		if err != nil {
			panic("redis db is invalid")
		}
		poolSize, err := strconv.ParseInt(os.Getenv("APP_REDIS_POOL_SIZE"), 10, 8)
		if err != nil {
			poolSize = 0
		}
		AppRedisConfigInstance = &Redis{
			Host:     os.Getenv("APP_REDIS_HOST"),
			Password: strings.TrimSpace(os.Getenv("APP_REDIS_PASSWORD")),
			Port:     os.Getenv("APP_REDIS_PORT"),
			Mode:     os.Getenv("APP_REDIS_MODE"),
			DB:       int(db),
			PoolSize: int(poolSize),
		}
	})
	return AppRedisConfigInstance
}

var PHPSessionRedisConfigInstance *Redis
var PHPSessionRedisConfigOnce sync.Once

func GetPHPSessionRedisConfig() *Redis {
	PHPSessionRedisConfigOnce.Do(func() {
		db, err := strconv.ParseInt(os.Getenv("PHP_SESSION_REDIS_DB"), 10, 8)
		if err != nil {
			panic("redis db is invalid")
		}
		poolSize, err := strconv.ParseInt(os.Getenv("PHP_SESSION_REDIS_POOL_SIZE"), 10, 8)
		if err != nil {
			poolSize = 0
		}
		PHPSessionRedisConfigInstance = &Redis{
			Host:     os.Getenv("PHP_SESSION_REDIS_HOST"),
			Password: strings.TrimSpace(os.Getenv("PHP_SESSION_REDIS_PASSWORD")),
			Port:     os.Getenv("PHP_SESSION_REDIS_PORT"),
			Mode:     os.Getenv("PHP_SESSION_REDIS_MODE"),
			DB:       int(db),
			PoolSize: int(poolSize),
		}
	})
	return PHPSessionRedisConfigInstance
}
