package configs

import (
	"os"
	"sync"
)

type OperateAudit struct {
	DynamoAuditTableName string
	AuditON              string
	AuditSync            string
}

var OperateAuditInstance *OperateAudit
var OperateAuditConfigOnce sync.Once

func GetOperateAuditConfig() *OperateAudit {
	OperateAuditConfigOnce.Do(func() {
		OperateAuditInstance = &OperateAudit{
			AuditON:              os.Getenv("AUDIT_ON"),
			AuditSync:            os.Getenv("AUDIT_SYNC_ON"),
			DynamoAuditTableName: os.Getenv("DYNAMODB_AUDIT_TABLE_NAME"),
		}
	})
	return OperateAuditInstance
}
