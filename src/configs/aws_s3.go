package configs

import (
	"log"
	"os"
)

type AwsS3Config struct {
	S3Bucket            string
	S3PromotionalPrefix string
}

func GetAwsS3Config() *AwsS3Config {
	s3Bucket := os.Getenv("APP_S3_BUCKET")
	if s3Bucket == "" {
		log.Fatal("APP_S3_BUCKET environment variable not set")
	}
	s3PromotionalPrefix := os.Getenv("APP_S3_PROMOTIONAL_PREFIX")
	if s3PromotionalPrefix == "" {
		log.Fatal("APP_S3_PROMOTIONAL_PREFIX environment variable not set")
	}
	return &AwsS3Config{
		S3Bucket:            s3Bucket,
		S3PromotionalPrefix: s3PromotionalPrefix,
	}
}
