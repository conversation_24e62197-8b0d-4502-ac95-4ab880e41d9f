package configs

import (
	"log"
	"os"
)

type DynamodbConfig struct {
	CompeTableName   string
	MashiaiTableName string
}

func GetDynamodbCompeConfig() *DynamodbConfig {
	tableName := os.Getenv("DYNAMODB_COMPE_TABLE_NAME")
	if tableName == "" {
		log.Fatal("DYNAMODB_TABLE_NAME environment variable not set")
	}
	mashiaiTableName := os.Getenv("DYNAMODB_MASHIAI_TABLE_NAME")
	if mashiaiTableName == "" {
		log.Fatal("DYNAMODB_MASHIAI_TABLE_NAME environment variable not set")
	}
	return &DynamodbConfig{
		CompeTableName:   tableName,
		MashiaiTableName: mashiaiTableName,
	}
}
