package configs

import (
	"log"
	"os"
)

type APIConfig struct {
	GolferSearch          string
	GolferSearchToken     string
	GetGolferData         string
	GetGolferDataToken    string
	CourseRateSearch      string
	CourseRateSearchToken string
}

func GetAPIConfig() *APIConfig {
	GolferSearch := os.Getenv("GOLFER_SEARCH")
	if GolferSearch == "" {
		log.Fatal("GOLFER_SEARCH environment variable not set")
	}
	GolferSearchToken := os.Getenv("GOLFER_SEARCH_TOKEN")
	if GolferSearchToken == "" {
		log.Fatal("GOLFER_SEARCH_TOKEN environment variable not set")
	}
	GetGolferData := os.Getenv("GET_GOLFER_DATA")
	if GetGolferData == "" {
		log.Fatal("GET_GOLFER_DATA environment variable not set")
	}
	GetGolferDataToken := os.Getenv("GET_GOLFER_DATA_TOKEN")
	if GetGolferDataToken == "" {
		log.Fatal("GET_GOLFER_DATA_TOKEN environment variable not set")
	}
	CourseRateSearch := os.Getenv("COURSE_RATE_SEARCH")
	if CourseRateSearch == "" {
		log.Fatal("COURSE_RATE_SEARCH environment variable not set")
	}
	CourseRateSearchToken := os.Getenv("COURSE_RATE_SEARCH_TOKEN")
	if CourseRateSearchToken == "" {
		log.Fatal("COURSE_RATE_SEARCH_TOKEN environment variable not set")
	}
	return &APIConfig{
		GolferSearch:          GolferSearch,
		GetGolferData:         GetGolferData,
		GolferSearchToken:     GolferSearchToken,
		GetGolferDataToken:    GetGolferDataToken,
		CourseRateSearch:      CourseRateSearch,
		CourseRateSearchToken: CourseRateSearchToken,
	}
}
