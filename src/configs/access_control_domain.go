package configs

import (
	"os"
	"sync"
)

type AccessControlDomains struct {
	Domains string
}

var AccessControlDomainsInstance *AccessControlDomains
var AccessControlDomainsConfigOnce sync.Once

func GetAccessControlDomainsConfig() *AccessControlDomains {
	AccessControlDomainsConfigOnce.Do(func() {
		AccessControlDomainsInstance = &AccessControlDomains{
			Domains: os.Getenv("ACCESS_CONTROL_ALLOW_DOMAIN"),
		}
	})
	return AccessControlDomainsInstance
}
