package configs

import (
	"log"
	"os"
)

type ShareConfig struct {
	ShareUrl       string
	EmailSource    string
	EmailAwsRegion string
}

func GetShareConfig() *ShareConfig {
	ShareUrl := os.Getenv("SHARED_URL")
	if ShareUrl == "" {
		log.Fatal("SHARED_URL environment variable not set")
	}
	EmailSource := os.Getenv("EMAIL_SOURCE")
	if EmailSource == "" {
		log.Fatal("EMAIL_SOURCE environment variable not set")
	}
	EmailAwsRegion := os.Getenv("EMAIL_AWS_REGION")
	if EmailAwsRegion == "" {
		log.Fatal("EMAIL_AWS_REGION environment variable not set")
	}
	return &ShareConfig{
		ShareUrl:       ShareUrl,
		EmailSource:    EmailSource,
		EmailAwsRegion: EmailAwsRegion,
	}
}
