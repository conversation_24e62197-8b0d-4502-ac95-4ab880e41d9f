package repository

import (
	"encoding/json"
	"fmt"
	"log/slog"
	"mi-restful-api/configs"
	"mi-restful-api/utils/dynamo"
	"time"

	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"github.com/google/uuid"
)

// OperateAudit operate audit ,table store delete and update
type OperateAudit struct {
	DynamoAuditTable string
	AuditOn          string
	AuditSyncOn      string
}

// NewOperateAudit after auth ,if open operate audit
func NewOperateAudit() *OperateAudit {
	auditConfig := configs.GetOperateAuditConfig()

	return &OperateAudit{
		DynamoAuditTable: auditConfig.DynamoAuditTableName,
		AuditOn:          auditConfig.AuditON,
		AuditSyncOn:      auditConfig.AuditSync,
	}
}

func (svc *OperateAudit) SaveOperate(sqlTableName, officeId string, detail any, loginInfo string) {
	detailBytes, err2 := json.Marshal(detail)
	if err2 != nil {
		slog.Info(err2.Error())
		return
	}

	item := map[string]types.AttributeValue{
		"office_id":  &types.AttributeValueMemberS{Value: officeId},
		"sort_key":   &types.AttributeValueMemberS{Value: svc.CreateSortKey(sqlTableName)},
		"created_at": &types.AttributeValueMemberS{Value: time.Now().Format(time.RFC3339)},
		"detail":     &types.AttributeValueMemberS{Value: string(detailBytes)},
		"login_info": &types.AttributeValueMemberS{Value: loginInfo},
	}

	execute, err := dynamo.PutItem(svc.DynamoAuditTable, item)
	if err != nil {
		slog.Error("put item err: ", "", err.Error())
		return
	}

	slog.Info("execute:", "", execute)
}

func (svc *OperateAudit) CreateSortKey(sqlTableName string) string {
	return fmt.Sprintf("audit_%s_%s_%s", sqlTableName, time.Now().Format("20060102"), uuid.NewString())
}
