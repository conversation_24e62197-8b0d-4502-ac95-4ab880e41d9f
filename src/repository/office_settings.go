package repository

import (
	"mi-restful-api/enum"
	"mi-restful-api/exception"
	"mi-restful-api/model"
	"mi-restful-api/request"

	"gorm.io/gorm"
)

type OfficeSettings struct {
	ConnMySQL *gorm.DB
}

func (svc *OfficeSettings) StoreOfficeSettingsData(req request.OfficeSettingsCreateReq, officeId string) error {
	// create or update
	tx := svc.ConnMySQL.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		}
	}()
	
	var updateData model.OfficeSettings
	tx.Model(&model.OfficeSettings{}).Where("office_id = ?", officeId).First(&updateData)

	if updateData.ID == 0 { // create new data
		updateData.OfficeID = officeId
	}
	updateData.EnableSelfScorePrint = req.EnableSelfScorePrint
	updateData.EnableQuestionnaire = req.EnableQuestionnaire
	updateData.EnableStartGuide = req.EnableStartGuide
	updateData.CardReaderType = req.CardReaderType

	var result *gorm.DB
	if updateData.ID == 0 {
		result = tx.Model(&model.OfficeSettings{}).Create(&updateData)
	} else {
		result = tx.Model(&model.OfficeSettings{}).Where("office_id = ? and id=?", officeId, updateData.ID).Updates(&updateData)
	}
	if result.Error != nil {
		tx.Rollback()
		return exception.NewError(enum.DBSQLEXECErrorCode, result.Error.Error())
	}
	
	// commit transaction
	if err := tx.Commit().Error; err != nil {
		return exception.NewError(enum.DBSQLEXECErrorCode, err.Error())
	}

	return nil
}

func (svc *OfficeSettings) IndexOfficeSettingsData(officeId string) (model.OfficeSettings, error) {
	builder := svc.ConnMySQL.Model(model.OfficeSettings{}).
		Where("office_id= ?", officeId)
	var data model.OfficeSettings
	err := builder.Find(&data).Error
	return data, err
}