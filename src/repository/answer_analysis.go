package repository

import (
	"mi-restful-api/model"
	"mi-restful-api/request"
	"time"

	"gorm.io/gorm"
)

type AnswerAnalysis struct {
	ConnMySQL *gorm.DB
}

func (svc *AnswerAnalysis) AnswerStatistical(officeId string, req request.StatisticalIndexReq) ([]model.StatisticalQuery, error) {
	var result []model.StatisticalQuery
	build := svc.ConnMySQL.Model(&model.Answer{}).Where("office_id= ? and created_at >= ? and created_at <= ?", officeId, tidyStartMonth(req.StartDate), tidyEndMonth(req.EndDate))
	if req.CaddyId != "" {
		build.Where("caddy_id=?", req.CaddyId)
	}
	if req.Weekday == 1 || req.Weekday == 2 {
		build.Where("weekday =?", req.Weekday)
	}
	err := build.Select("DATE_FORMAT(created_at, '%Y-%m') AS month, question_id, comment_id, count(*) as count").
		Group("DATE_FORMAT(created_at, '%Y-%m'), question_id, comment_id").
		Find(&result).Error

	return result, err
}

func tidyStartMonth(endMonth string) string {
	parse, err := time.Parse("2006-01", endMonth)
	if err != nil {
		dateSuffix := "-01 00:00:00"
		return endMonth + dateSuffix
	}

	return parse.Format(time.RFC3339)
}

func tidyEndMonth(endMonth string) string {
	parse, err := time.Parse("2006-01", endMonth)
	if err != nil {
		dateSuffix := "-31 23:59:59"
		return endMonth + dateSuffix
	}

	parse = parse.AddDate(0, 1, 0)
	parse = parse.Add(-1 * time.Second)
	return parse.Format(time.RFC3339)
}

func (svc *AnswerAnalysis) AnswerByCaddy(officeId string, req request.CaddyAnalysisIndexReq) ([]model.CaddyAnalysisQuery, error) {
	var result []model.CaddyAnalysisQuery
	build := svc.ConnMySQL.Model(&model.Answer{}).Where("office_id= ? and created_at >= ? and created_at <= ?", officeId, tidyStartMonth(req.StartDate), tidyEndMonth(req.EndDate))
	if req.CaddyId != "" {
		build.Where("caddy_id=?", req.CaddyId)
	}
	if req.Weekday == 1 || req.Weekday == 2 {
		build.Where("weekday =?", req.Weekday)
	}
	err := build.Select("DATE_FORMAT(created_at, '%Y-%m') AS month, question_id, comment_id, caddy_id, count(*) as count").
		Group("DATE_FORMAT(created_at, '%Y-%m'),caddy_id, question_id, comment_id").
		Find(&result).Error

	return result, err
}
