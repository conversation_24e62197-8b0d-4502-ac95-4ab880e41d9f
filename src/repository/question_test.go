package repository

import (
	"mi-restful-api/model"
	"mi-restful-api/request"
	"mi-restful-api/testutils/mocks"
	"regexp"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/joho/godotenv"
	log "github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func init() {
	// init env
	err := godotenv.Load("../../.env")
	if err != nil {
		log.Fatal("Error loading .env file")
	}
}

func TestFindQuestion(t *testing.T) {
	db, sqlMock, err := mocks.GetMockDB()
	if err != nil {
		t.Fatalf("Failed to open mock sql db, err: %v", err)
	}
	defer db.Close()

	// 使用 GORM 连接 sqlmock 数据库
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	assert.NoError(t, err)

	// 创建一个 Question 服务实例，并注入 mock 的数据库连接
	svc := &Question{ConnMySQL: gormDB}

	// 设置测试参数
	officeId := "111"

	// 模拟数据库返回的行
	mockRows := sqlMock.NewRows([]string{"id", "office_id", "type", "index", "content"}).
		AddRow(1, "111", int8(1), int8(1), "Question 1").
		AddRow(2, "111", int8(1), int8(2), "Question 2")

	expectedSQL := regexp.QuoteMeta("SELECT * FROM `question` WHERE office_id =? AND `question`.`deleted_at` IS NULL ORDER BY `type`,`index`")
	sqlMock.ExpectQuery(expectedSQL).
		WithArgs(officeId).
		WillReturnRows(mockRows)

	// 业务逻辑
	result, err := svc.FindQuestion(officeId)

	// 预期结果
	assert.NoError(t, err)
	assert.Len(t, result, 2)
	assert.Equal(t, result[0].Content, "Question 1")
	assert.Equal(t, result[1].Content, "Question 2")

	// check
	err = sqlMock.ExpectationsWereMet()
	assert.NoError(t, err)
}

func TestStoreQuestion(t *testing.T) {
	db, sqlMock, err := mocks.GetMockDB()
	if err != nil {
		t.Fatalf("Failed to open mock sql db, err: %v", err)
	}
	defer db.Close()

	// 使用 GORM 连接 sqlMock 数据库
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	assert.NoError(t, err)

	// 创建一个 Question 服务实例，并注入 mock 的数据库连接
	svc := &Question{ConnMySQL: gormDB}

	// 设置测试参数
	officeId := "111"

	// test data
	var data model.Question
	data.CreatedAt = time.Now()
	data.Content = "this is a test question content"
	data.Require = 1
	data.OfficeID = officeId
	data.Type = 1
	data.Index = 1

	// 模拟数据库行为，期望的插入操作
	sqlMock.ExpectBegin()
	expectedSQL := regexp.QuoteMeta("SELECT IFNULL(max(`index`), 0) `index` FROM `question` WHERE (office_id = ? and `type`=? and deleted_at is null) AND `question`.`deleted_at` IS NULL ORDER BY `question`.`id` LIMIT ?")
	sqlMock.ExpectQuery(expectedSQL).
		WithArgs("111", data.Type, 1).
		WillReturnRows(sqlMock.NewRows([]string{"index"}).AddRow(0))
	//
	expectedSQL = "INSERT INTO `question` (`index`,`type`,`content`,`require`,`created_at`,`deleted_at`,`office_id`) VALUES (?,?,?,?,?,?,?)"
	expectedSQL = regexp.QuoteMeta(expectedSQL)
	sqlMock.ExpectExec(expectedSQL).
		WithArgs(data.Index, data.Type, data.Content, data.Require, data.CreatedAt, data.DeletedAt, data.OfficeID).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMock.ExpectCommit()
	// 调用创建数据方法

	question, err := svc.CreateQuestion(data, officeId)
	// 验证错误及 返回结果
	assert.NoError(t, err)
	assert.Equal(t, 1, question)
	// 验证所有预期的操作是否都已完成
	err = sqlMock.ExpectationsWereMet()
	assert.NoError(t, err)
}

func TestUpdateQuestion(t *testing.T) {
	db, sqlMock, err := mocks.GetMockDB()
	if err != nil {
		t.Fatalf("Failed to open mock sql db, err: %v", err)
	}
	defer db.Close()

	// 使用 GORM 连接 sqlMock 数据库
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	assert.NoError(t, err)

	// 创建一个 Question 服务实例，并注入 mock 的数据库连接
	svc := &Question{ConnMySQL: gormDB}

	// 设置测试参数
	officeId := "111"
	questionId := 1
	var req request.PutQuestionParams
	req.Require = 1
	req.Content = "update content"

	mockRows := sqlMock.NewRows([]string{"id", "office_id", "type", "index", "content", "created_at"}).
		AddRow(1, "111", int8(1), int8(1), "Question 1", time.Now())

	// 模拟数据库行为，期望的插入操作
	sqlMock.ExpectBegin()
	expectedSQL := regexp.QuoteMeta("SELECT * FROM `question` WHERE office_id =? AND id =? AND `question`.`deleted_at` IS NULL ORDER BY `question`.`id` LIMIT ?")
	sqlMock.ExpectQuery(expectedSQL).
		WithArgs("111", questionId, 1).
		WillReturnRows(mockRows)
	//
	expectedSQL = "UPDATE `question` SET `id`=?,`index`=?,`type`=?,`content`=?,`require`=?,`created_at`=?,`office_id`=? WHERE (id = ? and office_id = ?) AND `question`.`deleted_at` IS NULL"
	expectedSQL = regexp.QuoteMeta(expectedSQL)
	sqlMock.ExpectExec(expectedSQL).
		WithArgs(int(questionId), int8(1), int8(1), req.Content, req.Require, sqlmock.AnyArg(), officeId, questionId, officeId).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMock.ExpectCommit()
	// 调用创建数据方法

	_, err = svc.UpdateQuestion(officeId, questionId, req)
	// 验证错误及 返回结果
	assert.NoError(t, err)
	// 验证所有预期的操作是否都已完成
	err = sqlMock.ExpectationsWereMet()
	assert.NoError(t, err)
}

func TestDeleteQuestion(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("Failed to open sqlmock database: %v", err)
	}
	defer db.Close()

	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("Failed to open gorm database: %v", err)
	}

	// 设置时间
	mockRows := mock.NewRows([]string{"id", "office_id", "type", "index", "content"}).
		AddRow(25, "111", int8(1), int8(1), "Question 1")

	// 模拟 SELECT 语句
	mock.ExpectBegin()
	querySQL := regexp.QuoteMeta("SELECT * FROM `question` WHERE (office_id = ? and id= ?) AND `question`.`deleted_at` IS NULL ORDER BY `question`.`id` LIMIT ?")
	mock.ExpectQuery(querySQL).
		WithArgs("111", "25", 1).
		WillReturnRows(mockRows)

	// 模拟 UPDATE 删除操作
	updateSQL := regexp.QuoteMeta("UPDATE `question` SET `deleted_at`=? WHERE (office_id = ? AND id = ?) AND `question`.`deleted_at` IS NULL")
	mock.ExpectExec(updateSQL).
		WithArgs(sqlmock.AnyArg(), "111", "25").
		WillReturnResult(sqlmock.NewResult(1, 1))

	// 模拟 UPDATE 调整 index 操作
	updateSQL = "UPDATE `question` SET `index`=`index` - ? WHERE (office_id = ? and `type`=? and deleted_at is null) AND `index` > ? AND `question`.`deleted_at` IS NULL"
	updateSQL = regexp.QuoteMeta(updateSQL)
	mock.ExpectExec(updateSQL).
		WithArgs(1, "111", int8(1), int8(1)).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()
	// 调用待测试的函数
	handle := Question{
		ConnMySQL: gormDB,
	}
	err = handle.DeleteQuestion("25", "111")
	if err != nil {
		t.Errorf("Failed to execute DeleteQuestionAndUpdateIndex: %v", err)
	}

	// 验证所有的期望是否都满足
	if err = mock.ExpectationsWereMet(); err != nil {
		t.Errorf("There were unfulfilled expectations: %v", err)
	}
}

//func TestBaseQuestion(t *testing.T) {
//	dbClient, err := client.GetDbClient()
//	assert.NoError(t, err)
//
//	// 设置测试参数
//	officeId := "111"
//	questionId := 1
//	var req request.PutQuestionParams
//	req.Require = 1
//	req.Content = "update content"
//
//	handler := repository.Question{
//		ConnMySQL: dbClient,
//	}
//
//	question, err := handler.UpdateQuestion(officeId, questionId, req)
//	fmt.Println(question, err)
//}
