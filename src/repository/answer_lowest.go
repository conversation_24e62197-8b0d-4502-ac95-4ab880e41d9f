package repository

import (
	"mi-restful-api/model"
	"mi-restful-api/request"
	"time"

	"gorm.io/gorm"
)

type AnswerLowest struct {
	ConnMySQL *gorm.DB
}

func (svc *AnswerLowest) AnswerLowest(officeId string, req request.AnswerLowestReq) ([]model.AnswerLowestQuery, error) {
	var result []model.AnswerLowestQuery

	builder := svc.ConnMySQL.Model(&model.Answer{}).
		Select("answer.question_id, ANY_VALUE(answer.comment_id) comment_id, MIN(evaluation.score) as min_score").
		Joins("LEFT JOIN evaluation ON answer.comment_id = evaluation.id").
		Joins("LEFT JOIN question ON answer.question_id = question.id").
		Group("answer.question_id").
		Where("answer.office_id= ? and answer.created_at >= ? and answer.created_at <= ? ", officeId, req.StartDate, req.EndDate.Add(time.Duration(86399)*time.Second)).
		Where("question.deleted_at is null")

	if req.CaddyId != 0 {
		builder.Where("answer.caddy_id=?", req.CaddyId)
	}

	if req.Weekday == 1 {
		builder.Where("weekday = 1")
	} else if req.Weekday == 2 {
		builder.Where("weekday = 2")
	}
	err := builder.Scan(&result).Error
	return result, err
}
