package repository

import (
	"context"
	"encoding/json"
	"errors"
	"log/slog"
	"mi-restful-api/client"
	"mi-restful-api/model"
)

type AppAuth struct {
}

func NewAppAuth() *AppAuth {
	return &AppAuth{}
}

func (svc *AppAuth) AppAuthCheck(authToken, deviceID string) (*model.AppAuthInfo, error) {
	if deviceID != "" {
		return svc.AuthIncludeDeviceID(authToken, deviceID)
	} else {
		return svc.AuthExcludeDeviceID(authToken)
	}
}

func (svc *AppAuth) AuthIncludeDeviceID(authToken, deviceID string) (*model.AppAuthInfo, error) {
	authKey := svc.redisKeyOfAppAuth(authToken, deviceID)
	exist, err := RedisCheckKeyExist(authKey)
	if err != nil {
		return nil, err
	}

	if !exist {
		return nil, errors.New("auth key not exist or expired")
	}

	content, err := RedisGetByteByKey(authKey)
	if err != nil {
		return nil, err
	}

	var appAuthInfo model.AppAuthInfo
	err = json.Unmarshal(content, &appAuthInfo)
	if err != nil {
		return nil, err
	}

	return &appAuthInfo, nil
}

func (svc *AppAuth) AuthExcludeDeviceID(authToken string) (*model.AppAuthInfo, error) {
	authKey := svc.redisKeyOfAppAuth(authToken, "")
	firstKey, err := GetPrefixKeys(authKey)
	if err != nil {
		return nil, err
	}

	content, err := RedisGetByteByKey(firstKey)
	if err != nil {
		return nil, err
	}

	var appAuthInfo model.AppAuthInfo
	err = json.Unmarshal(content, &appAuthInfo)
	if err != nil {
		return nil, err
	}

	return &appAuthInfo, nil
}

func (svc *AppAuth) redisKeyOfAppAuth(authToken, deviceID string) string {
	return "auth_device_key/" + authToken + "/" + deviceID
}

func GetPrefixKeys(key string) (string, error) {
	rdb := client.GetAppRedisClient()
	result, err := rdb.Keys(context.Background(), key+"*").Result()
	if err != nil {
		return "", err
	}

	// judgment
	if len(result) > 0 {
		return result[0], nil
	} else {
		return "", errors.New("keys * not found: prefix:" + key)
	}
}

func RedisCheckKeyExist(key string) (bool, error) {
	slog.Debug("RedisCheckKeyExist", "key", key)
	rdb := client.GetAppRedisClient()
	if key == "" {
		return false, errors.New("key is empty")
	}

	result, err := rdb.Exists(context.Background(), key).Result()
	if err != nil {
		return false, err
	}
	slog.Debug("RedisCheckKeyExist", "key", key, "result", result)

	// judgment
	if result > 0 {
		return true, nil
	} else {
		return false, nil
	}
}

func RedisGetByteByKey(key string) ([]byte, error) {
	slog.Debug("RedisGetByteByKey", "key", key)
	rdb := client.GetAppRedisClient()
	if key == "" {
		return []byte{}, errors.New("key is empty")
	}

	result, err := rdb.Get(context.Background(), key).Bytes()

	slog.Debug("RedisGetByteByKey", "key", key, "result", string(result), "err", err)

	return result, err

}
