package repository

import (
	"mi-restful-api/request"
	"mi-restful-api/testutils/mocks"
	"regexp"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func InitMySQLConn(t *testing.T) (sqlmock.Sqlmock, *gorm.DB) {
	db, mock, err := mocks.GetMockDB()
	if err != nil {
		t.Fatalf("Failed to open sqlmock database: %v", err)
	}

	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("Failed to open gorm database: %v", err)
	}

	return mock, gormDB
}

func TestFindAllEvaluation(t *testing.T) {
	mock, db := InitMySQLConn(t)

	// 设置测试参数
	officeId := "111"

	// 模拟数据库返回的行
	mockRows := mock.NewRows([]string{"id", "office_id", "stage", "score", "content"}).
		AddRow(1, "111", 1, 1, "select 1").
		AddRow(3, "111", 2, 2, "select 2")

	expectedSQL := regexp.QuoteMeta("SELECT * FROM `evaluation` WHERE office_id =? AND `evaluation`.`deleted_at` IS NULL")
	mock.ExpectQuery(expectedSQL).
		WithArgs(officeId).
		WillReturnRows(mockRows)

	// 业务逻辑
	svc := &EvaluationRepos{ConnMySQL: db}
	result, err := svc.FindAllEvaluation(officeId)

	// 预期结果
	assert.NoError(t, err)
	assert.Len(t, result, 2)
	assert.Equal(t, result[0].Content, "select 1")
	assert.Equal(t, result[1].Content, "select 2")

	// check
	err = mock.ExpectationsWereMet()
	assert.NoError(t, err)
}

func TestUpdateEvaluation(t *testing.T) {
	mock, db := InitMySQLConn(t)

	officeId := "111"
	var req = []request.EvaluationPutReq{
		{
			Stage:   1,
			Score:   1,
			Content: "this is select 1",
		},
	}
	rows := sqlmock.NewRows([]string{"id", "stage", "score", "content", "created_at", "office_id"}).
		AddRow(6, 1, 1, "this is select 1", time.Now(), officeId)

	mock.ExpectBegin()
	querySQL := "SELECT * FROM `evaluation` WHERE (office_id = ? and stage = ?) AND `evaluation`.`deleted_at` IS NULL ORDER BY `evaluation`.`id` LIMIT ?"
	querySQL = regexp.QuoteMeta(querySQL)
	mock.ExpectQuery(querySQL).WithArgs(officeId, req[0].Stage, 1).
		WillReturnRows(rows)

	createSQL := "UPDATE `evaluation` SET `id`=?,`stage`=?,`score`=?,`content`=?,`created_at`=?,`office_id`=? WHERE (office_id = ? and id=?) AND `evaluation`.`deleted_at` IS NULL"
	createSQL = regexp.QuoteMeta(createSQL)
	mock.ExpectExec(createSQL).
		WithArgs(6, req[0].Stage, req[0].Score, req[0].Content, sqlmock.AnyArg(), officeId, officeId, 6).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()

	svc := &EvaluationRepos{ConnMySQL: db}
	result, err := svc.UpdateEvaluation(req, officeId)
	assert.NoError(t, err)
	assert.Equal(t, 1, len(result))
	// check
	err = mock.ExpectationsWereMet()
	assert.NoError(t, err)

}

func TestUpdateCreateEvaluation(t *testing.T) {
	mock, db := InitMySQLConn(t)

	officeId := "111"
	var req = []request.EvaluationPutReq{
		{
			Stage:   1,
			Score:   1,
			Content: "this is select 1",
		},
	}
	rows := sqlmock.NewRows([]string{"id", "stage", "score", "content", "created_at", "office_id"})

	mock.ExpectBegin()
	querySQL := "SELECT * FROM `evaluation` WHERE (office_id = ? and stage = ?) AND `evaluation`.`deleted_at` IS NULL ORDER BY `evaluation`.`id` LIMIT ?"
	querySQL = regexp.QuoteMeta(querySQL)
	mock.ExpectQuery(querySQL).WithArgs(officeId, req[0].Stage, 1).
		WillReturnRows(rows)

	createSQL := "INSERT INTO `evaluation` (`stage`,`score`,`content`,`created_at`,`deleted_at`,`office_id`) VALUES (?,?,?,?,?,?)"
	createSQL = regexp.QuoteMeta(createSQL)
	mock.ExpectExec(createSQL).
		WithArgs(req[0].Stage, req[0].Score, req[0].Content, sqlmock.AnyArg(), nil, officeId).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()

	svc := &EvaluationRepos{ConnMySQL: db}
	result, err := svc.UpdateEvaluation(req, officeId)
	assert.NoError(t, err)
	assert.Equal(t, 1, len(result))
	// check
	err = mock.ExpectationsWereMet()
	assert.NoError(t, err)

}

func TestCreateEvaluation(t *testing.T) {
	mock, db := InitMySQLConn(t)

	officeId := "111"
	var req = []request.EvaluationPutReq{
		{
			Stage:   1,
			Score:   1,
			Content: "this is select 1",
		},
	}

	mock.ExpectBegin()
	createSQL := "INSERT INTO `evaluation` (`stage`,`score`,`content`,`created_at`,`deleted_at`,`office_id`) VALUES (?,?,?,?,?,?)"
	createSQL = regexp.QuoteMeta(createSQL)
	mock.ExpectExec(createSQL).
		WithArgs(req[0].Stage, req[0].Score, req[0].Content, sqlmock.AnyArg(), nil, officeId).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()

	svc := &EvaluationRepos{ConnMySQL: db}
	result, err := svc.CreateEvaluation(req, officeId)
	assert.NoError(t, err)
	assert.Equal(t, 1, len(result))
	// check
	err = mock.ExpectationsWereMet()
	assert.NoError(t, err)

}

//func TestBaseEvaluation(t *testing.T) {
//	dbClient, err := client.GetDbClient()
//	assert.NoError(t, err)
//
//	// 设置测试参数
//	officeId := "111"
//	var req = []request.EvaluationPutReq{
//		{
//			Stage:   7,
//			Score:   1,
//			Content: "this is select 1",
//		},
//	}
//
//	handler := repository.EvaluationRepos{
//		ConnMySQL: dbClient,
//	}
//
//	question, err := handler.UpdateEvaluation(req, officeId)
//	fmt.Println(question, err)
//}
