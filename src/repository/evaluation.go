package repository

import (
	"mi-restful-api/enum"
	"mi-restful-api/exception"
	"mi-restful-api/model"
	"mi-restful-api/request"
	"time"

	"gorm.io/gorm"
)

type EvaluationRepos struct {
	ConnMySQL *gorm.DB
}

func (svc *EvaluationRepos) CreateEvaluation(reqs []request.EvaluationPutReq, officeId string) ([]model.Evaluation, error) {
	var data []model.Evaluation
	curTime := time.Now()
	for _, req := range reqs {
		var dataTemp model.Evaluation
		dataTemp.Stage = req.Stage
		dataTemp.Score = req.Score
		dataTemp.CreatedAt = curTime
		dataTemp.OfficeID = officeId
		dataTemp.Content = req.Content

		data = append(data, dataTemp)
	}

	err := svc.ConnMySQL.Model(model.Evaluation{}).
		Create(&data).Error
	if err != nil {
		return nil, exception.NewError(enum.DBSQLEXECErrorCode, err.Error())
	}

	return data, nil
}

func (svc *EvaluationRepos) UpdateEvaluation(req []request.EvaluationPutReq, officeId string) ([]model.Evaluation, error) {
	// create or update
	tx := svc.ConnMySQL.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		}
	}()
	var data []model.Evaluation
	createTime := time.Now()
	for _, reqTemp := range req {
		var updateData model.Evaluation
		tx.Model(&model.Evaluation{}).Where("office_id = ? and stage = ?", officeId, reqTemp.Stage).First(&updateData)
		if updateData.ID == 0 { // update exist data
			updateData.Stage = reqTemp.Stage
			updateData.OfficeID = officeId
			updateData.CreatedAt = createTime
		}
		updateData.Score = reqTemp.Score
		updateData.Content = reqTemp.Content

		var result *gorm.DB
		if updateData.ID == 0 {
			result = tx.Model(&model.Evaluation{}).Create(&updateData)
		} else {
			result = tx.Model(&model.Evaluation{}).Where("office_id = ? and id=?", officeId, updateData.ID).Updates(&updateData)
		}
		if result.Error != nil {
			tx.Rollback()
			return nil, exception.NewError(enum.DBSQLEXECErrorCode, result.Error.Error())
		}

		data = append(data, updateData)
	}
	// commit transaction
	if err := tx.Commit().Error; err != nil {
		return nil, exception.NewError(enum.DBSQLEXECErrorCode, err.Error())
	}

	return data, nil
}

func (svc *EvaluationRepos) FindEvaluation(officeId string, commentId int) (*model.Evaluation, error) {

	var data model.Evaluation
	err := svc.ConnMySQL.Model(model.Evaluation{}).
		Where("office_id =?", officeId).
		Where("id =?", commentId).
		First(&data).Error
	if err != nil {
		return nil, exception.NewError(enum.DBSQLEXECErrorCode, err.Error())
	}
	return &data, err
}

func (svc *EvaluationRepos) FindAllEvaluation(officeId string) ([]model.Evaluation, error) {

	var data []model.Evaluation
	err := svc.ConnMySQL.Model(model.Evaluation{}).
		Where("office_id =?", officeId).
		Find(&data).Error
	if err != nil {
		return nil, exception.NewError(enum.DBSQLEXECErrorCode, err.Error())
	}

	return data, err
}
