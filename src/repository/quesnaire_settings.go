package repository

import (
	"mi-restful-api/enum"
	"mi-restful-api/exception"
	"mi-restful-api/model"
	"mi-restful-api/request"

	"gorm.io/gorm"
)

type QuesnaireSettings struct {
	ConnMySQL *gorm.DB
}

func (svc *QuesnaireSettings) StoreQuesnaireSettingsData(req request.QuesnaireSettingsCreateReq, officeId string) error {
	// create or update
	tx := svc.ConnMySQL.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		}
	}()
	
	var updateData model.QuesnaireSettings
	tx.Model(&model.QuesnaireSettings{}).Where("office_id = ?", officeId).First(&updateData)

	if updateData.ID == 0 { // create new data
		updateData.OfficeID = officeId
	}
	updateData.CaddyNameType = req.CaddyNameType

	var result *gorm.DB
	if updateData.ID == 0 {
		result = tx.Model(&model.QuesnaireSettings{}).Create(&updateData)
	} else {
		result = tx.Model(&model.QuesnaireSettings{}).Where("office_id = ? and id=?", officeId, updateData.ID).Updates(&updateData)
	}
	if result.Error != nil {
		tx.Rollback()
		return exception.NewError(enum.DBSQLEXECErrorCode, result.Error.Error())
	}
	
	// commit transaction
	if err := tx.Commit().Error; err != nil {
		return exception.NewError(enum.DBSQLEXECErrorCode, err.Error())
	}

	return nil
}
