package repository

import (
	"fmt"
	"mi-restful-api/model"
	"strconv"
	"testing"
	"time"
)

func TestPlayHistory(t *testing.T) {
	officeId := 1000219
	cartNo := 51
	data, err := GetPlayHistoryFromRDS(officeId, cartNo, "2024-12-05")
	if err != nil {
		fmt.Println(err.Error())
	}

	fmt.Println(data)
}

func TestCartInfoFromScore(t *testing.T) {
	start := time.Now()
	//return
	cartNo := 109
	officeKey := "testipc1"
	scorePrimaryKey := model.MarshaliDynamoDb{
		PartitionKey: officeKey,
		//SortKey:      "score_" + time.Now().Format("20060102"),
		SortKey: "score_20241206",
	}
	items, err := GetCardInfoFromDynamoDB(scorePrimaryKey, strconv.Itoa(cartNo))
	if err != nil {
		fmt.Println("err ocu:", err.<PERSON>rror())
	} else {
		fmt.Println("over", items)
	}
	fmt.Println("exec time: ", time.Now().Sub(start).String())
}

func TestPlayHistoryOpInfo(t *testing.T) {
	officeId := "1000219"
	cartNo := 109
	officeKey := "testipc1"
	start := time.Now()
	//return
	result, err := GetCartInfoNewVersion("", officeKey, officeId, cartNo)
	fmt.Println("exec time: ", time.Now().Sub(start).String())
	if err != nil {
		fmt.Println("err ocu:", err.Error())
	} else {
		fmt.Println(result)
	}
}
