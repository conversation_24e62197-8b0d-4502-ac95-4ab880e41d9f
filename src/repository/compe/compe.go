package repocompe

import (
	"context"
	"errors"
	"fmt"
	"log/slog"
	"mi-restful-api/client"
	"mi-restful-api/configs"
	"mi-restful-api/model/compe"
	"mi-restful-api/model/player"
	req "mi-restful-api/request/compe"
	"mi-restful-api/utils/common"
	"mi-restful-api/utils/dynamo"
	"strconv"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"gorm.io/gorm"
)

func CreateOnlineCompe(onlineCompe compe.OnlineCompe) (bool, error) {
	item, err := attributevalue.MarshalMap(onlineCompe)
	if err != nil {
		slog.Error("operation create compe error", "err", err, "params", item)
		return false, err
	}

	compeTableConfig := configs.GetDynamodbCompeConfig()
	execute, err := dynamo.PutItem(compeTableConfig.CompeTableName, item)
	if err != nil {
		slog.Error("operation create compe error", "err", err, "params", execute)
		return false, err
	}

	return true, nil
}

func UpdateOnlineCompe(onlineCompe compe.OnlineCompe) (bool, error) {
	item, err := attributevalue.MarshalMap(onlineCompe.Details)
	if err != nil {
		slog.Error("operation update online compe error", "err", err, "params", item)
		return false, err
	}

	input := &dynamodb.UpdateItemInput{
		TableName: aws.String(configs.GetDynamodbCompeConfig().CompeTableName),
		Key: map[string]types.AttributeValue{
			"partition_key": &types.AttributeValueMemberS{Value: onlineCompe.PartitionKey},
			"sort_key":      &types.AttributeValueMemberS{Value: onlineCompe.SortKey},
		},
		UpdateExpression: aws.String("SET details = :details, expiration_time = :expiration_time, updated_at = :updated_at"),
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":details":         &types.AttributeValueMemberM{Value: item},
			":expiration_time": &types.AttributeValueMemberN{Value: fmt.Sprintf("%d", onlineCompe.ExpirationTime)},
			":updated_at":      &types.AttributeValueMemberS{Value: onlineCompe.UpdatedAt.Format(time.RFC3339)},
		},
		ReturnValues: types.ReturnValueAllNew,
	}

	execute, err := dynamo.UpdateItem(input)
	if err != nil {
		slog.Error("operation update online compe error", "err", err, "params", execute)
		return false, err
	}

	return true, nil
}

func DeleteOnlineCompeByNo(no int) (bool, error) {
	// TODO: delete from db
	return true, nil
}

func OnlineCompeByNo(no int) (compe.OnlineCompe, error) {
	slog.Info("operation online compe by no", "no", no)
	pk := "#online-compe-basic"
	sk := "online-compe_" + strconv.Itoa(no)
	input := &dynamodb.QueryInput{
		TableName:              aws.String(configs.GetDynamodbCompeConfig().CompeTableName),
		KeyConditionExpression: aws.String("partition_key = :partition_key AND sort_key = :sort_key"),
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":partition_key": &types.AttributeValueMemberS{Value: pk},
			":sort_key":      &types.AttributeValueMemberS{Value: sk},
		},
		Limit: aws.Int32(1),
	}

	resp, err := dynamo.Query(input)
	if err != nil {
		slog.Error("operation online compe by no", "err", err, "params", input)
		return compe.OnlineCompe{}, err
	}

	if len(resp.Items) == 0 {
		slog.Error("operation online compe by no", "err", "no data", "params", input)
		return compe.OnlineCompe{}, errors.New("no data")
	}

	var onlineCompe compe.OnlineCompe
	err = attributevalue.UnmarshalMap(resp.Items[0], &onlineCompe)
	if err != nil {
		slog.Error("operation online compe by no", "err", err)
		return compe.OnlineCompe{}, nil
	}

	return onlineCompe, nil
}

func OnlineCompePrivateByNo(officeKey string, sk string) (compe.PrivateSetting, error) {
	input := &dynamodb.QueryInput{
		TableName:              aws.String(configs.GetDynamodbCompeConfig().CompeTableName),
		KeyConditionExpression: aws.String("partition_key = :partition_key AND sort_key = :sort_key"),
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":partition_key": &types.AttributeValueMemberS{Value: officeKey},
			":sort_key":      &types.AttributeValueMemberS{Value: sk},
		},
		Limit: aws.Int32(1),
	}

	resp, err := dynamo.Query(input)
	if err != nil {
		slog.Error("operation online compe by no", "err", err, "params", input)
		return compe.PrivateSetting{}, err
	}

	if len(resp.Items) == 0 {
		slog.Error("operation online compe by no", "err", "no data", "params", input)
		return compe.PrivateSetting{}, errors.New("no data")
	}

	var officeCompe compe.OfficeCompe
	err = attributevalue.UnmarshalMap(resp.Items[0], &officeCompe)
	if err != nil {
		slog.Error("operation online compe by no", "err", err)
		return compe.PrivateSetting{}, nil
	}

	return compe.PrivateSetting{
		CourseSetting: officeCompe.Details.CourseSetting,
		HiddenHole:    officeCompe.Details.HiddenHole,
	}, nil
}

func OnlineCompeLastNo() (int, error) {
	slog.Info("operation online compe last no")
	db, err := client.GetMncDBClient()
	if err != nil {
		slog.Error("OnlineCompeLastNo get mysql db client error:", "err", err)
		return 0, fmt.Errorf("%s %v", "get db client error:", err)
	}

	var onlineCompeIndices []compe.OnlineCompeIndex
	err = db.Model(&compe.OnlineCompeIndex{}).
		Order("compe_id desc").
		Limit(1).
		Find(&onlineCompeIndices).Error
	if err != nil {
		slog.Error("OnlineCompeLastNo get online compe indices error:", "err", err)
		return 0, fmt.Errorf("%s %v", "get online compe indices error:", err)
	}

	if len(onlineCompeIndices) == 0 {
		slog.Info("ListOfficeToOnlineCompeByPlayDate no online compe indices found")
		return 0, nil
	}

	return onlineCompeIndices[0].CompeID, nil
}

func UpdateDefaultOnlineCompeSetting(defaultSetting compe.OnlineCompeDefaultSetting) (bool, error) {
	item, err := attributevalue.MarshalMap(defaultSetting.Details)
	if err != nil {
		slog.Error("update default online compe setting error", "err", err, "params", item)
		return false, err
	}

	input := &dynamodb.UpdateItemInput{
		TableName: aws.String(configs.GetDynamodbCompeConfig().CompeTableName),
		Key: map[string]types.AttributeValue{
			"partition_key": &types.AttributeValueMemberS{Value: defaultSetting.PartitionKey},
			"sort_key":      &types.AttributeValueMemberS{Value: defaultSetting.SortKey},
		},
		UpdateExpression: aws.String("SET details = :details, expiration_time = :expiration_time, updated_at = :updated_at"),
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":details":         &types.AttributeValueMemberM{Value: item},
			":expiration_time": &types.AttributeValueMemberN{Value: fmt.Sprintf("%d", defaultSetting.ExpirationTime)},
			":updated_at":      &types.AttributeValueMemberS{Value: defaultSetting.UpdatedAt.Format(time.RFC3339)},
		},
		ReturnValues: types.ReturnValueAllNew,
	}

	execute, err := dynamo.UpdateItem(input)
	if err != nil {
		slog.Error("update default online compe setting error", "err", err, "params", execute)
		return false, err
	}
	return true, nil
}

func OnlineCompeDefaultSetting(officeKey string) (*compe.OnlineCompeDefaultSetting, error) {
	pk := "#online-compe-default"
	sk := "setting_" + officeKey
	input := &dynamodb.QueryInput{
		TableName:              aws.String(configs.GetDynamodbCompeConfig().CompeTableName),
		KeyConditionExpression: aws.String("partition_key = :partition_key AND sort_key = :sort_key"),
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":partition_key": &types.AttributeValueMemberS{Value: pk},
			":sort_key":      &types.AttributeValueMemberS{Value: sk},
		},
		Limit: aws.Int32(1),
	}

	resp, err := dynamo.Query(input)
	if err != nil {
		slog.Error("operation online compe default", "err", err, "params", input)
		return nil, err
	}

	if len(resp.Items) == 0 {
		slog.Error("operation online compe default", "err", "no data", "params", input)
		return nil, errors.New("no data")
	}

	var defaultSetting compe.OnlineCompeDefaultSetting
	err = attributevalue.UnmarshalMap(resp.Items[0], &defaultSetting)
	if err != nil {
		slog.Error("operation online default", "err", err)
		return nil, nil
	}

	return &defaultSetting, nil
}

func UpsertOfficeToCompe(officeCompe compe.OfficeCompe, newTo time.Time) (bool, error) {
	item, err := attributevalue.MarshalMap(officeCompe.Details)
	if err != nil {
		slog.Error("operation upsert compe error", "err", err, "params", item)
		return false, err
	}

	input := &dynamodb.UpdateItemInput{
		TableName: aws.String(configs.GetDynamodbCompeConfig().CompeTableName),
		Key: map[string]types.AttributeValue{
			"partition_key": &types.AttributeValueMemberS{Value: officeCompe.OfficeKey},
			"sort_key":      &types.AttributeValueMemberS{Value: officeCompe.SortKey},
		},
		UpdateExpression: aws.String("SET details = :details, expiration_time = :expiration_time, updated_at = :updated_at"),
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":details":         &types.AttributeValueMemberM{Value: item},
			":expiration_time": &types.AttributeValueMemberN{Value: fmt.Sprintf("%d", officeCompe.ExpirationTime)},
			":updated_at":      &types.AttributeValueMemberS{Value: officeCompe.UpdatedAt.Format(time.RFC3339)},
		},
		ReturnValues: types.ReturnValueAllNew,
	}

	result, err := dynamo.UpdateItem(input)
	if err != nil {
		slog.Error("operation upsert compe error", "err", err, "result", result)
		return false, err
	}

	//TODO Check here
	expirationTime := int(time.Until(newTo).Seconds())

	if expirationTime < 0 {
		slog.Warn("operation upsert compe activated error", "err", "expirationTime is negative")
		return true, nil
	}

	// Get the online compe to access its details
	onlineCompe, err := OnlineCompeByNo(officeCompe.Details.CompeNo)
	if err != nil {
		slog.Error("operation upsert compe error: failed to get online compe", "err", err, "compe_no", officeCompe.Details.CompeNo)
		return false, err
	}

	// Prepare aggregation types string
	aggregationTypes := []string{}
	if onlineCompe.Details.CompeTypeSetting.Handy != nil {
		aggregationTypes = append(aggregationTypes, "handy")
	}
	if onlineCompe.Details.CompeTypeSetting.Peoria != nil {
		if onlineCompe.Details.CompeTypeSetting.Peoria.AggregationMethod.Type == nil {
			slog.Error("operation upsert compe error: aggregation method type is nil", "compe_no", officeCompe.Details.CompeNo)
			return false, errors.New("aggregation method type is nil")
		}
		aggregationTypes = append(aggregationTypes, strconv.Itoa(*onlineCompe.Details.CompeTypeSetting.Peoria.AggregationMethod.Type))
	}

	// Connect to MySQL database
	db, err := client.GetMncDBClient()
	if err != nil {
		slog.Error("operation upsert compe error: failed to get MySQL client", "err", err)
		return false, err
	}

	// Create or update OnlineCompeIndex in MySQL
	// First, check if the record exists
	var existingIndex compe.OnlineCompeIndex
	dbResult := db.Where("compe_id = ? AND office_key = ?", officeCompe.Details.CompeNo, officeCompe.OfficeKey).First(&existingIndex)

	// Prepare the data for insert or update
	aggregationTypesValue := strings.Join(aggregationTypes, ",")

	if dbResult.Error != nil {
		// If record doesn't exist, create it
		if errors.Is(dbResult.Error, gorm.ErrRecordNotFound) {
			// Insert a new record using SQL directly since the struct doesn't have OfficeKey field
			sql := `INSERT INTO online_compe_indices
				(compe_id, compe_name, compe_type, aggregation_types, office_key, time_zone, started_at, ended_at, created_at)
				VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`

			createResult := db.Exec(sql,
				officeCompe.Details.CompeNo,
				officeCompe.Details.CompeName,
				*onlineCompe.Details.CompeTypeSetting.Type,
				aggregationTypesValue,
				officeCompe.OfficeKey,
				"Asia/Tokyo",
				onlineCompe.Details.Basic.Duration.From,
				onlineCompe.Details.Basic.Duration.To,
				time.Now())

			if createResult.Error != nil {
				slog.Error("operation upsert compe error: failed to create OnlineCompeIndex", "err", createResult.Error)
				return false, createResult.Error
			}
		} else {
			// Some other error occurred
			slog.Error("operation upsert compe error: failed to query OnlineCompeIndex", "err", dbResult.Error)
			return false, dbResult.Error
		}
	} else {
		// Record exists, update it using SQL directly
		sql := `UPDATE online_compe_indices
			SET compe_name = ?, compe_type = ?, aggregation_types = ?, time_zone = ?, started_at = ?, ended_at = ?
			WHERE compe_id = ? AND office_key = ?`

		updateResult := db.Exec(sql,
			officeCompe.Details.CompeName,
			*onlineCompe.Details.CompeTypeSetting.Type,
			aggregationTypesValue,
			"Asia/Tokyo",
			onlineCompe.Details.Basic.Duration.From,
			onlineCompe.Details.Basic.Duration.To,
			officeCompe.Details.CompeNo,
			officeCompe.OfficeKey)

		if updateResult.Error != nil {
			slog.Error("operation upsert compe error: failed to update OnlineCompeIndex", "err", updateResult.Error)
			return false, updateResult.Error
		}
	}

	return true, nil
}

func ListOfficeToOnlineCompeByPlayDate(playDateStr string, officeKey string, offset int, limit int) ([]compe.OnlineCompe, []compe.OfficeCompe, error) {
	slog.Info("operation list office online compe by play date", "playDateStr", playDateStr, "officeKey", officeKey, "offset", offset, "limit", limit)

	t, err := time.Parse("20060102", playDateStr)
	if err != nil {
		slog.Error("ListOfficeToOnlineCompeByPlayDate format time str error:", "err", err)
		return []compe.OnlineCompe{}, []compe.OfficeCompe{}, fmt.Errorf("%s %v", "format time str error:", err)
	}
	// to YYYY-MM-DD
	formattedPlayDate := t.Format("2006-01-02")
	db, err := client.GetMncDBClient()
	if err != nil {
		slog.Error("ListOfficeToOnlineCompeByPlayDate get mysql db client error:", "err", err)
		return []compe.OnlineCompe{}, []compe.OfficeCompe{}, fmt.Errorf("%s %v", "get db client error:", err)
	}

	var onlineCompeIndices []compe.OnlineCompeIndex
	err = db.Model(&compe.OnlineCompeIndex{}).
		Where("office_key = ? AND ? BETWEEN DATE(started_at) AND DATE(ended_at)",
			officeKey,
			formattedPlayDate).
		Offset(offset).
		Limit(limit).
		Find(&onlineCompeIndices).Error
	if err != nil {
		slog.Error("ListOfficeToOnlineCompeByPlayDate get online compe indices error:", "err", err)
		return []compe.OnlineCompe{}, []compe.OfficeCompe{}, fmt.Errorf("%s %v", "get online compe indices error:", err)
	}

	if len(onlineCompeIndices) == 0 {
		slog.Info("ListOfficeToOnlineCompeByPlayDate no online compe indices found")
		return []compe.OnlineCompe{}, []compe.OfficeCompe{}, nil
	}

	// Prepare to fetch the actual compe data
	var onlineCompes []compe.OnlineCompe
	var officeCompes []compe.OfficeCompe

	for _, index := range onlineCompeIndices {
		// Fetch online compe
		onlineCompe, err := OnlineCompeByNo(index.CompeID)
		if err != nil {
			slog.Error("ListOfficeToOnlineCompeByPlayDate get online compe error:", "err", err, "compe_id", index.CompeID)
			continue
		}
		onlineCompes = append(onlineCompes, onlineCompe)

		// Fetch office compe
		pk := officeKey
		sk := "online-compe_" + strconv.Itoa(index.CompeID)
		input := &dynamodb.QueryInput{
			TableName:              aws.String(configs.GetDynamodbCompeConfig().CompeTableName),
			KeyConditionExpression: aws.String("partition_key = :partition_key AND sort_key = :sort_key"),
			ExpressionAttributeValues: map[string]types.AttributeValue{
				":partition_key": &types.AttributeValueMemberS{Value: pk},
				":sort_key":      &types.AttributeValueMemberS{Value: sk},
			},
			Limit: aws.Int32(1),
		}

		resp, err := dynamo.Query(input)
		if err != nil {
			slog.Error("ListOfficeToOnlineCompeByPlayDate get office compe error:", "err", err, "params", input)
			continue
		}

		if len(resp.Items) == 0 {
			slog.Error("ListOfficeToOnlineCompeByPlayDate no office compe found:", "compe_id", index.CompeID)
			continue
		}

		var officeCompe compe.OfficeCompe
		err = attributevalue.UnmarshalMap(resp.Items[0], &officeCompe)
		if err != nil {
			slog.Error("ListOfficeToOnlineCompeByPlayDate unmarshal office compe error:", "err", err)
			continue
		}

		officeCompes = append(officeCompes, officeCompe)
	}

	return onlineCompes, officeCompes, nil
}

func GetOldCompeByNo(dateStr string, officeKey string, compeNo int) (*compe.OldCompe, error) {
	slog.Info("operation get old compe by no", "officeKey", officeKey, "compeNo", compeNo)

	pk := officeKey
	sk := "compe_" + officeKey + "_" + strconv.Itoa(compeNo)

	officeOldCompeInput := map[string]types.AttributeValue{
		"partition_key": &types.AttributeValueMemberS{Value: pk},
		"sort_key":      &types.AttributeValueMemberS{Value: sk},
	}

	resp, err := dynamo.GetItem(configs.GetDynamodbCompeConfig().MashiaiTableName, officeOldCompeInput)
	if err != nil {
		slog.Error("operation get old compe by no", "err", err, "params", officeOldCompeInput)
		return nil, err
	}

	var oldCompe compe.OldCompe
	err = attributevalue.UnmarshalMap(resp.Item, &oldCompe)
	if err != nil {
		slog.Error("operation get old compe by no", "err", err)
		return nil, err
	}

	return &oldCompe, nil

}

func ListOfficeToOldCompe(dateStr string, officeKey string, offset int, limit int, asc bool) ([]compe.OldCompe, error) {

	slog.Info("operation list office old compe", "dateStr", dateStr, "officeKey", officeKey, "offset", offset, "limit", limit)

	officeOldCompeInput := &dynamodb.QueryInput{
		TableName:              aws.String(configs.GetDynamodbCompeConfig().MashiaiTableName),
		KeyConditionExpression: aws.String("partition_key = :office_key AND begins_with(sort_key, :prefix)"),
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":office_key": &types.AttributeValueMemberS{Value: officeKey},
			":prefix":     &types.AttributeValueMemberS{Value: "compe_" + dateStr + "_"},
		},
		Limit:            aws.Int32(int32(limit)),
		ScanIndexForward: aws.Bool(asc),
	}

	result, err := dynamo.Query(officeOldCompeInput)
	if err != nil {
		slog.Error("operation list office old compe", "err", err, "params", officeOldCompeInput)
		return nil, err
	}
	if result.Items == nil {
		slog.Warn("operation list office old compe", "err", "no items", "params", officeOldCompeInput)
		return []compe.OldCompe{}, nil
	}

	oldCompes := []compe.OldCompe{}
	for _, item := range result.Items {
		var oldCompe compe.OldCompe
		err = attributevalue.UnmarshalMap(item, &oldCompe)
		if err != nil {
			slog.Error("operation list office old compe", "err", err, "item", item)
			continue
		}
		oldCompes = append(oldCompes, oldCompe)
	}
	return oldCompes, nil
}

func UpdateOfficeToCompeShareKey(shareKey string, officeKey string, req req.LeaderboardRankingShareKeyCreateReq) error {
	pk := officeKey
	sk := "online-compe_" + strconv.Itoa(req.CompeNo)
	input := &dynamodb.UpdateItemInput{
		TableName: aws.String(configs.GetDynamodbCompeConfig().CompeTableName),
		Key: map[string]types.AttributeValue{
			"partition_key": &types.AttributeValueMemberS{Value: pk},
			"sort_key":      &types.AttributeValueMemberS{Value: sk},
		},
		ConditionExpression: aws.String("attribute_exists(sort_key)"),
		UpdateExpression:    aws.String("SET details.shared_key = :shared_key"),
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":shared_key": &types.AttributeValueMemberS{Value: shareKey},
		},
	}

	execute, err := dynamo.UpdateItem(input)
	if err != nil {
		slog.Error("operation upsert compe error", "err", err, "params", execute)
		return err
	}

	return nil
}

func UpsertCompeToPlayer(compePayer compe.CompePlayer) (bool, error) {
	slog.Info("UpsertCompeToPlayer", "compePayer", compePayer)
	item, err := attributevalue.MarshalMap(compePayer)
	if err != nil {
		slog.Error("operation upsert compe to player error", "err", err, "params", item)
		return false, err
	}
	compeTableConfig := configs.GetDynamodbCompeConfig()
	execute, err := dynamo.PutItem(compeTableConfig.CompeTableName, item)
	if err != nil {
		slog.Error("operation upsert compe to player error", "err", err, "params", execute)
		return false, err
	}

	return true, nil
}

func UpsertPlayerToCompe(playerCompe player.PlayerCompe) (bool, error) {
	slog.Info("UpsertPlayerToCompe", "playerCompe", playerCompe)
	item, err := attributevalue.MarshalMap(playerCompe)
	if err != nil {
		slog.Error("operation upsert player to compe error", "err", err, "params", item)
		return false, err
	}
	compeTableConfig := configs.GetDynamodbCompeConfig()
	//delete cache []player.PlayerCompe if key exist
	redis := client.GetAppRedisClient()
	cacheKey := fmt.Sprintf("player_online-compe_%s_%d_%s", playerCompe.PartitionKey, playerCompe.Details.PlayerNo, playerCompe.Details.PlayDate)
	_, err = redis.Del(context.Background(), cacheKey).Result()
	if err != nil {
		slog.Warn("Failed to delete player compes from cache", "err", err)
	}
	execute, err := dynamo.PutItem(compeTableConfig.CompeTableName, item)
	if err != nil {
		slog.Error("operation upsert player to compe error", "err", err, "params", execute)
		return false, err
	}
	return true, nil
}

func LeaderboardRankingByCompeNo(compeNo string, aggregationType string, officeKey string, dateStr string) (*compe.Ranking, error) {
	slog.Info("operation leaderboard ranking by compe id", "compeNo", compeNo, "aggregationType", aggregationType, "officeKey", officeKey, "dateStr", dateStr)
	pk := "#online-compe-ranking"
	skPrefix := "online-compe_" + compeNo +
		"_" + officeKey +
		"_" + dateStr +
		"_" + aggregationType
	input := &dynamodb.QueryInput{
		TableName:              aws.String(configs.GetDynamodbCompeConfig().CompeTableName),
		KeyConditionExpression: aws.String("partition_key = :partition_key AND begins_with(sort_key, :sortKeyPrefix)"),
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":partition_key": &types.AttributeValueMemberS{Value: pk},
			":sortKeyPrefix": &types.AttributeValueMemberS{Value: skPrefix},
		},
		Limit:            aws.Int32(1),
		ScanIndexForward: aws.Bool(false),
	}

	resp, err := dynamo.Query(input)
	if err != nil {
		slog.Error("operation leaderboard ranking by compe id", "err", err, "params", input)
		return nil, err
	}

	if len(resp.Items) == 0 {
		slog.Warn("operation leaderboard ranking by compe id", "err", "no data", "params", input)
		return nil, nil
	}

	var ranking compe.Ranking
	err = attributevalue.UnmarshalMap(resp.Items[0], &ranking)
	if err != nil {
		slog.Error("operation leaderboard ranking by compe id", "err", err)
		return nil, err
	}

	return &ranking, nil
}

func LeaderboardRankingLastestDateByCompeNo(compeNo string, officeKey string) (string, error) {
	slog.Info("operation leaderboard ranking by compe id", "compeNo", compeNo, "officeKey", officeKey)
	pk := "#online-compe-ranking"
	skPrefix := "online-compe_" + compeNo +
		"_" + officeKey +
		"_"
	input := &dynamodb.QueryInput{
		TableName:              aws.String(configs.GetDynamodbCompeConfig().CompeTableName),
		KeyConditionExpression: aws.String("partition_key = :partition_key AND begins_with(sort_key, :sortKeyPrefix)"),
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":partition_key": &types.AttributeValueMemberS{Value: pk},
			":sortKeyPrefix": &types.AttributeValueMemberS{Value: skPrefix},
		},
		Limit:            aws.Int32(1),
		ScanIndexForward: aws.Bool(false),
	}

	resp, err := dynamo.Query(input)
	if err != nil {
		slog.Error("operation leaderboard ranking by compe id", "err", err, "params", input)
		return "", err
	}

	if len(resp.Items) == 0 {
		slog.Warn("operation leaderboard ranking by compe id", "err", "no data", "params", input)
		return "", nil
	}
	item := resp.Items[0]
	sortKey := item["sort_key"].(*types.AttributeValueMemberS).Value
	parts := strings.Split(sortKey, "_")
	if len(parts) < 4 {
		slog.Error("operation leaderboard ranking by compe id", "err", "invalid sort key", "sortKey", sortKey, "params", input)
		err = fmt.Errorf("invalid sort key: %s", sortKey)
		return "", err
	}
	dateStr := strings.Split(sortKey, "_")[3]
	return dateStr, nil
}

func LeaderboardRankingTypeByCompeNoAndDate(compeNo string, officeKey string, dateStr string) ([]string, error) {
	slog.Info("operation leaderboard ranking type by compe id", "compeNo", compeNo, "officeKey", officeKey, "dateStr", dateStr)
	pk := "#online-compe-ranking"
	skPrefix := "online-compe_" + compeNo +
		"_" + officeKey +
		"_" + dateStr +
		"_"

	// only return sortlkey
	input := &dynamodb.QueryInput{
		TableName:              aws.String(configs.GetDynamodbCompeConfig().CompeTableName),
		KeyConditionExpression: aws.String("partition_key = :partition_key AND begins_with(sort_key, :sortKeyPrefix)"),
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":partition_key": &types.AttributeValueMemberS{Value: pk},
			":sortKeyPrefix": &types.AttributeValueMemberS{Value: skPrefix},
		},
		ProjectionExpression: aws.String("sort_key"),
		ScanIndexForward:     aws.Bool(false),
	}

	resp, err := dynamo.Query(input)
	if err != nil {
		slog.Error("operation leaderboard ranking by compe id", "err", err, "params", input)
		return []string{}, err
	}

	if len(resp.Items) == 0 {
		slog.Error("operation leaderboard ranking by compe id", "err", "no data", "params", input)
		return []string{}, nil
	}

	// return  last item in sort_key  split  by "_"
	var aggregationTypes []string
	for _, item := range resp.Items {
		sortKey := item["sort_key"].(*types.AttributeValueMemberS).Value
		parts := strings.Split(sortKey, "_")
		if len(parts) > 0 {
			aggregationType := parts[len(parts)-1]
			aggregationTypes = append(aggregationTypes, aggregationType)
		}
	}

	return aggregationTypes, nil

}

func LeaderboardRankingShareKeyCreate(req req.LeaderboardRankingShareKeyCreateReq, officeId string, officeKey string, expiry time.Duration) (string, error) {
	redis := client.GetRedisClient()
	maxRetries := 3
	for retry := 0; retry < maxRetries; retry++ {
		var shareKey string
		if req.ShareKey != nil {
			shareKey = *req.ShareKey
		} else {
			shareKey = common.GenerateShareKey(req.CompeNo)
		}
		value := fmt.Sprintf("%d_%s_%s", req.CompeNo, officeId, officeKey)
		// delete redis records with shareKey key
		keys, err := redis.Keys(context.Background(), strconv.Itoa(req.CompeNo)+":shareKey:*").Result()
		if err != nil {
			slog.Error("Redis operation failed", "error", err, "retry", retry)
			continue
		}
		for _, key := range keys {
			redis.Del(context.Background(), key)
		}

		success, err := redis.SetNX(context.Background(), strconv.Itoa(req.CompeNo)+":shareKey:"+shareKey, value, expiry).Result()
		if err != nil {
			slog.Error("Redis operation failed", "error", err, "retry", retry)
			continue
		}

		if success {
			slog.Info("Share key created successfully", "shareKey", shareKey)
			err := UpdateOfficeToCompeShareKey(shareKey, officeKey, req)
			if err != nil {
				redis.Del(context.Background(), strconv.Itoa(req.CompeNo)+":shareKey:"+shareKey)
				slog.Error("update dynamodb failed", "error", err, "retry", retry)
				return "", err
			}
			return shareKey, nil
		}
	}
	return "", errors.New("create share key error, please try again")
}
