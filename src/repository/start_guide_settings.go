package repository

import (
	"mi-restful-api/enum"
	"mi-restful-api/exception"
	"mi-restful-api/model"
	"mi-restful-api/request"

	"gorm.io/gorm"
)

type StartGuideSettings struct {
	ConnMySQL *gorm.DB
}

func (svc *StartGuideSettings) StoreStartGuideSettingsData(req request.StartGuideSettingsCreateReq, officeId string) error {
	// create or update
	tx := svc.ConnMySQL.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		}
	}()
	
	var updateData model.StartGuideSettings
	tx.Model(&model.StartGuideSettings{}).Where("office_id = ?", officeId).First(&updateData)

	if updateData.ID == 0 { // create new data
		updateData.OfficeID = officeId
	}
	updateData.EnableStartTime = req.EnableStartTime
	updateData.MainTextAlways = req.MainTextAlways
	updateData.SubTextAlways = req.SubTextAlways
	updateData.EnableAutoStart = req.EnableAutoStart
	updateData.AutoStartType = req.AutoStartType
	updateData.StartTimeSchedule = req.StartTimeSchedule
	updateData.StartNumber = req.StartNumber
	updateData.SubTextAuto = req.SubTextAuto
	
	var result *gorm.DB
	if updateData.ID == 0 {
		result = tx.Model(&model.StartGuideSettings{}).Create(&updateData)
	} else {
		result = tx.Model(&model.StartGuideSettings{}).Where("office_id = ? and id=?", officeId, updateData.ID).Updates(&updateData)
	}
	if result.Error != nil {
		tx.Rollback()
		return exception.NewError(enum.DBSQLEXECErrorCode, result.Error.Error())
	}
	
	// commit transaction
	if err := tx.Commit().Error; err != nil {
		return exception.NewError(enum.DBSQLEXECErrorCode, err.Error())
	}

	return nil
}

func (svc *StartGuideSettings) IndexStartGuideSettingsData(officeId string) (model.StartGuideSettings, error) {
	builder := svc.ConnMySQL.Model(model.StartGuideSettings{}).
		Where("office_id= ?", officeId)
	var data model.StartGuideSettings
	err := builder.Find(&data).Error
	return data, err
}