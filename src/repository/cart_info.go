package repository

import (
	"errors"
	"fmt"
	"log/slog"
	"mi-restful-api/client"
	"mi-restful-api/enum"
	"mi-restful-api/exception"
	"mi-restful-api/logging"
	"mi-restful-api/model"
	"mi-restful-api/response"
	"mi-restful-api/utils/common"
	"mi-restful-api/utils/dynamo"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
)

var marshalIDynamodbName string = "marshal-i"

func GetCardInfoFromDynamoDB(RequestMarshalIDynamoDb model.MarshaliDynamoDb, cartNo string) (map[string]response.CartInfoData, error) {
	items, err := GetMultiItem(RequestMarshalIDynamoDb)
	if err != nil {
		return nil, err
	} else if len(items) == 0 { // data not found
		return nil, nil
	}

	sort.Slice(items, func(i, j int) bool {
		firstDate, _ := time.Parse(time.RFC3339, items[i].UpdatedAt)
		secondDate, _ := time.Parse(time.RFC3339, items[j].UpdatedAt)
		return firstDate.Before(secondDate)
	})

	var data = map[string]response.CartInfoData{}
	dataFilter := make(map[string]map[string]model.PlayerScore)
	for _, item := range items {
		scoreDetail := model.ScoreDetail{}
		if err = common.ConvertStruct(item.Details, &scoreDetail); err != nil {
			return nil, err
		} else {
			if item.CartNo != cartNo { // not target cartNo jump
				continue
			}

			headerInfo := model.ScoreHeader{}
			if err = common.ConvertStruct(item.Header, &headerInfo); err != nil {
				slog.Error("")
			}

			tempStartTime, err := time.Parse(time.RFC3339, item.UpdatedAt)
			startTime := ""
			if err != nil {
				slog.Warn("score-log_ updated_at parse time error value :" + item.UpdatedAt)
			} else {
				startTime = tempStartTime.Format("15:04")
			}
			startCourse := scoreDetail.ScheduledStartCourseIndex
			if len(startCourse) > 0 && len(scoreDetail.Courses) > 0 {
				courseIndex, err := strconv.Atoi(startCourse)
				if err != nil {
					startCourse = ""
				} else if len(scoreDetail.Courses) > courseIndex {
					startCourse = scoreDetail.Courses[courseIndex].CourseName
				}
			} else if len(startCourse) <= 0 && len(scoreDetail.Courses) > 0 {
				startCourse = scoreDetail.Courses[0].CourseName
			} else {
				startCourse = ""
			}

			playerKeys, existScore := GetPlayerInfo(scoreDetail.PlayerScore)
			firstKey := fmt.Sprintf("%s_%s_%s_%s_%s", item.CartNo, scoreDetail.ScheduledStartCourseIndex, scoreDetail.ScheduledStartTime, scoreDetail.CaddieNo, playerKeys)
			if _, ok := dataFilter[firstKey]; ok {
				// repeat data filter , skip old data
				if data[firstKey].UpdatedAt >= scoreDetail.UpdatedAt {
					continue
				}

				var tempData response.CartInfoData
				tempData.CartNo = item.CartNo
				tempData.PlayedDate = headerInfo.PlayDate
				tempData.StartTime = startTime
				tempData.CaddyID = scoreDetail.CaddieNo
				tempData.CaddyName = scoreDetail.CaddieName
				tempData.StartCourse = startCourse
				tempData.UpdatedAt = scoreDetail.UpdatedAt

				for _, player := range scoreDetail.PlayerScore {
					var cartPlayer response.CartPlayer
					cartPlayer.Id = player.PlayerNo
					cartPlayer.Name = player.PlayerName
					cartPlayer.ClubChecked = false

					tempData.CartPlayer = append(tempData.CartPlayer, cartPlayer)
				}

				tempData.Serial = fmt.Sprintf("%s_%s", headerInfo.PlayDate, getPlayersSortedIdString(tempData.CartPlayer))
				tempData.ExistScore = existScore
				tempData.SortKey = item.SortKey

				// obtain timeDuration for found operationInfo
				dealStartTimeAndCourseByScoreLog(RequestMarshalIDynamoDb.PartitionKey, item.SortKey, &tempData)
				snapshotData, err := getOperationInfoByTimeDuration(RequestMarshalIDynamoDb.PartitionKey, &tempData)
				if err != nil {
					data[firstKey] = tempData
					continue
				} else {
					snapshotStartTime, err := time.Parse(time.RFC3339, snapshotData.StartTime)
					if err != nil {
						slog.Warn("time parse error", "error", err, "time_str", snapshotData.StartTime)
					} else {
						tempData.StartTime = snapshotStartTime.Format("15:04")
					}
					// deal caddy_name
					if tempData.CaddyName == "" {
						tempData.CaddyName = snapshotData.CaddieName
						tempData.CaddyID = snapshotData.CaddieNo
					}
					data[firstKey] = tempData
				}
			} else {
				var tempData response.CartInfoData
				tempData.CartNo = item.CartNo
				tempData.PlayedDate = headerInfo.PlayDate
				tempData.StartTime = startTime
				tempData.CaddyID = scoreDetail.CaddieNo
				tempData.CaddyName = scoreDetail.CaddieName
				tempData.StartCourse = startCourse
				tempData.UpdatedAt = scoreDetail.UpdatedAt

				for _, player := range scoreDetail.PlayerScore {
					if _, ok := dataFilter[firstKey][player.PlayerNo]; ok {
						continue
					} else {
						var cartPlayer response.CartPlayer
						cartPlayer.Id = player.PlayerNo
						cartPlayer.Name = player.PlayerName
						cartPlayer.ClubChecked = false

						if _, exists := dataFilter[firstKey]; !exists {
							dataFilter[firstKey] = make(map[string]model.PlayerScore)
						}
						dataFilter[firstKey][player.PlayerNo] = player
						tempData.CartPlayer = append(tempData.CartPlayer, cartPlayer)
					}
				}

				tempData.Serial = fmt.Sprintf("%s_%s", headerInfo.PlayDate, getPlayersSortedIdString(tempData.CartPlayer))
				tempData.ExistScore = existScore
				tempData.SortKey = item.SortKey

				// obtain timeDuration for found operationInfo
				dealStartTimeAndCourseByScoreLog(RequestMarshalIDynamoDb.PartitionKey, item.SortKey, &tempData)
				snapshotData, err := getOperationInfoByTimeDuration(RequestMarshalIDynamoDb.PartitionKey, &tempData)
				if err != nil {
					data[firstKey] = tempData
					continue
				} else {
					snapshotStartTime, err := time.Parse(time.RFC3339, snapshotData.StartTime)
					if err != nil {
						slog.Warn("time parse error", "error", err, "time_str", snapshotData.StartTime)
					} else {
						tempData.StartTime = snapshotStartTime.Format("15:04")
					}

					// deal caddy_name
					if tempData.CaddyName == "" {
						tempData.CaddyName = snapshotData.CaddieName
						tempData.CaddyID = snapshotData.CaddieNo
					}
					data[firstKey] = tempData
				}

			}

		}
	}

	return data, nil
}

func getOperationInfoByTimeDuration(partitionKey string, tempData *response.CartInfoData) (*model.SnapshotData, error) {
	sortKey := fmt.Sprintf("operation-info_%s_%s", strings.Replace(tempData.PlayedDate, "-", "", 3), strings.Replace(tempData.TimeDuration, ":", "", 3))
	operationInfo, err := getOperationInfo(partitionKey, sortKey, tempData.CartNo)
	buildTime := ""
	if err != nil {
		buildTime = fmt.Sprintf("%s %s:00", tempData.PlayedDate, tempData.TimeDuration)
		var standardTime time.Time
		standardTime, err = time.Parse(time.DateTime, buildTime)
		if err != nil {
			slog.Error("operation info format time err", "error", err, "params", buildTime)
			return nil, err
		}
		timeDurations := make([]string, 0)
		hour := standardTime.Hour()
		for hour > 6 {
			standardTime = standardTime.Add(-10 * time.Minute)

			timeDurations = append(timeDurations, standardTime.Format("15:04"))
			hour = standardTime.Hour()
		}
		operationInfo, err = getNearestOperationInfo(partitionKey, tempData.PlayedDate, tempData.CartNo, timeDurations, "score_")
		if err != nil {
			return nil, err
		}
	}

	slog.Debug("debug :operationInfo :", "operationInfo", operationInfo)
	if operationInfo == nil {
		errMsg := "cant found operationInfo by detect from " + buildTime + "init sort key is :" + sortKey
		slog.Warn(errMsg)
		return nil, errors.New(errMsg)
	}

	return operationInfo, nil
}

func getNearestOperationInfo(partitionKey, dateTime, cartNo string, timeDurations []string, from string) (*model.SnapshotData, error) {
	for _, hourMinute := range timeDurations {
		sortKey := fmt.Sprintf("operation-info_%s_%s", strings.Replace(dateTime, "-", "", 3), strings.Replace(hourMinute, ":", "", 3))
		sortKey = sortKey[:len(sortKey)-1] // Take ten minutes each time.

		operationInfo, err := getOperationInfo(partitionKey, sortKey, cartNo)
		logging.LogFormat(enum.LogDebug, map[string]any{
			"category":      enum.LogCategoryApp,
			"message":       "cart info v2 nearest data of operation-info ,cart_info_v2_debug",
			"module":        "CartInfoV2Index",
			"from":          from,
			"key":           sortKey,
			"partition_key": partitionKey,
			"data":          operationInfo,
			"err":           err,
		})
		if err != nil {
			return nil, err
		} else if err == nil && operationInfo != nil && len(operationInfo.Players) > 0 {
			return operationInfo, nil
		} else {
			continue
		}
	}
	return nil, nil
}

func getOperationInfo(partitionKey, sortKey, CartNo string) (*model.SnapshotData, error) {
	input := &dynamodb.QueryInput{
		TableName:              aws.String(marshalIDynamodbName),
		KeyConditionExpression: aws.String("partition_key = :office_key and begins_with(sort_key, :prefix)"),
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":office_key": &types.AttributeValueMemberS{Value: partitionKey},
			":prefix":     &types.AttributeValueMemberS{Value: sortKey},
		},
		ScanIndexForward: aws.Bool(false), // asc sorted
	}

	resp, err := dynamo.Query(input)
	if err != nil {
		slog.Error("operation info get error", "err", err, "params", input)
		return nil, err
	}
	if resp.Count < 1 {
		slog.Info("operation info get error", "info", "response data count lt one", "params", input)
	}

	itemList := []*model.MarshaliDynamoDb{}
	if err = attributevalue.UnmarshalListOfMaps(resp.Items, &itemList); err != nil {
		slog.Error("operation info get error", "error", err, "params", input, "data", itemList)
		return nil, err
	} else {

		if len(itemList) < 1 { // at least one
			slog.Error("operation info check query data length lt 1", "error", err, "params", input, "data", itemList)
			return nil, errors.New("operation info check query data length lt 1")
		}
		for _, itemDetail := range itemList {
			operationInfoDetail := model.OperationInfoDetail{}
			if err = common.ConvertStruct(itemDetail.Details, &operationInfoDetail); err != nil {
				slog.Error("unmarshal operation info details error", "error", err, "data", itemDetail.Details)
				return nil, err
			} else {
				for _, snapshotData := range operationInfoDetail.SnapshotData {
					if snapshotData.CartNo == CartNo {
						return &snapshotData, nil
					}
				}

				slog.Info("operation info not found cart_no", "info", "not found snapshot data cart_no:"+CartNo, "search_sort_key_prefix", sortKey, "return_data_sort_key", itemDetail.SortKey, "data", itemDetail.Details)
				return nil, errors.New("not found snapshot data cart_no:" + CartNo)
			}
		}

		return nil, errors.New(fmt.Sprintf("not found snapshot data sort_key: %s , cart_no:%s ,items length: %d", sortKey, CartNo, len(itemList)))
	}

}

func dealStartTimeAndCourseByScoreLog(partitionKey, sortKey string, tempData *response.CartInfoData) {
	scoreLogPrefix := strings.Replace(sortKey, "score_", "score-log_", 1)
	scoreLogKey := model.MarshaliDynamoDb{
		PartitionKey: partitionKey,
		SortKey:      scoreLogPrefix,
	}
	// init var
	updateAt, err := time.Parse(time.RFC3339, tempData.UpdatedAt)
	if err == nil {
		tempData.TimeDuration = updateAt.Format("15:04")
	}
	scoreLogLast, err := GetLastScoreHistory(scoreLogKey)
	if err != nil {
		slog.Error("getLastScore error", "params", scoreLogKey, "error", err)
	} else {
		splitSortKey := strings.Split(scoreLogLast.SortKey, "_")
		if len(splitSortKey) >= 4 {
			timeStr := splitSortKey[3]
			tempData.TimeDuration = fmt.Sprintf("%s:%s", timeStr[0:2], timeStr[2:4])
		}
	}
}

func GetPlayerInfo(players []model.PlayerScore) (string, bool) {
	existScore := false
	playIds := []string{}
	for _, ps := range players {
		if ps.CourseScore != nil && len(ps.CourseScore) > 0 {
			existScore = true
		}
		playIds = append(playIds, ps.PlayerNo)
	}
	sort.Strings(playIds)
	return strings.Join(playIds, "-"), existScore
}

func GetMultiItem(RequestMarshaliDynamoDb model.MarshaliDynamoDb) ([]*model.MarshaliDynamoDb, error) {
	var LastEvaluatedKey map[string]types.AttributeValue
	result := []*model.MarshaliDynamoDb{}

	for {
		output, err := getScanData(LastEvaluatedKey, &RequestMarshaliDynamoDb, 100)
		if err != nil {
			return nil, err
		}

		itemList := []*model.MarshaliDynamoDb{}
		if err := attributevalue.UnmarshalListOfMaps(output.Items, &itemList); err != nil {
			return nil, err
		}
		if output.Count > 0 {
			result = append(result, itemList...)
		}

		if len(output.LastEvaluatedKey) == 0 {
			break
		} else {
			LastEvaluatedKey = output.LastEvaluatedKey
		}
	}

	return result, nil
}

func getScanData(evaluated map[string]types.AttributeValue, Request *model.MarshaliDynamoDb, limit int32) (*dynamodb.QueryOutput, error) {
	input := &dynamodb.QueryInput{
		TableName:              aws.String(marshalIDynamodbName),
		KeyConditionExpression: aws.String("partition_key = :office_key and begins_with(sort_key, :prefix)"),
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":office_key": &types.AttributeValueMemberS{Value: Request.PartitionKey},
			":prefix":     &types.AttributeValueMemberS{Value: Request.SortKey},
		},
		ScanIndexForward:  aws.Bool(false), // asc sorted
		Limit:             aws.Int32(limit),
		ExclusiveStartKey: evaluated,
	}

	return dynamo.Query(input)
}

func GetLastScoreHistory(RequestMarshaliDynamoDb model.MarshaliDynamoDb) (*model.MarshaliDynamoDb, error) {
	var LastEvaluatedKey map[string]types.AttributeValue
	var result model.MarshaliDynamoDb

	for {
		output, err := getScanDataScoreLog(LastEvaluatedKey, &RequestMarshaliDynamoDb)
		if err != nil {
			return nil, err
		}

		itemList := []*model.MarshaliDynamoDb{}
		if err := attributevalue.UnmarshalListOfMaps(output.Items, &itemList); err != nil {
			return nil, err
		}
		if output.Count > 0 {
			result = *itemList[0]
		}

		if len(output.LastEvaluatedKey) == 0 {
			break
		} else {
			LastEvaluatedKey = output.LastEvaluatedKey
		}
	}

	return &result, nil
}

func getScanDataScoreLog(evaluated map[string]types.AttributeValue, Request *model.MarshaliDynamoDb) (*dynamodb.QueryOutput, error) {
	input := &dynamodb.QueryInput{
		TableName:              aws.String(marshalIDynamodbName),
		KeyConditionExpression: aws.String("partition_key = :office_key and begins_with(sort_key, :prefix)"),
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":office_key": &types.AttributeValueMemberS{Value: Request.PartitionKey},
			":prefix":     &types.AttributeValueMemberS{Value: Request.SortKey},
		},
		ScanIndexForward:  aws.Bool(false), // asc sorted
		ExclusiveStartKey: evaluated,
	}

	return dynamo.Query(input)
}

func GetCartInfoOfRDS(officeId string, cartNo int) ([]model.PlayerQuery, error) {
	db, err := client.GetDbClient()
	if err != nil {
		return nil, exception.NewError(enum.DBConnErrorCode, err.Error())
	}

	var data []model.PlayerQuery
	subQuery := db.Model(&model.Player{}).
		Select("max(id) id").
		Group("office_id, player_id").
		Where("office_id=?", officeId)
	err = db.Model(&model.Player{}).
		Select("q.cart_no,q.caddy_id,q.played_date,q.start_time,q.start_course,q.serial,player.id,player.player_id,player.player_name,player.questionnaire_id").
		Joins("left join questionnaire as q on player.questionnaire_id=q.id ").
		Where("player.id in (?)", subQuery).
		Where("player.office_id=? and q.cart_no=? and q.created_at> ?", officeId, cartNo, getCurrentDate()).
		Find(&data).Error
	if err != nil {
		return nil, exception.NewError(enum.DBSQLEXECErrorCode, err.Error())
	}

	return data, nil
}

func getCurrentDate() string {
	return time.Now().Format("2006-01-02") + " 00:00:00"
}

func GetPlayHistoryFromRDS(officeId int, cartNo int, playDate string) ([]model.PlayHistory, error) {
	db, err := client.GetMncDBClient()
	if err != nil {
		return nil, exception.NewError(enum.DBConnErrorCode, err.Error())
	}

	var data []model.PlayHistory
	/*subQuery := db.Model(&model.PlayHistory{}).
		Select("start_time, cart_no, MAX(created_at) AS max_created_at").
		Where("office_id=? and play_history_date=? and cart_no=?", officeId, playDate, cartNo)

	err = db.Table("play_history AS ph").
		Joins("JOIN (?) AS sq ON ph.start_time = sq.start_time AND ph.cart_no = sq.cart_no AND ph.created_at = sq.max_created_at", subQuery).
		Where("ph.office_id=? and ph.play_history_date=? and ph.cart_no=?", officeId, playDate, cartNo).
		Select("ph.*").
		Find(&data).Error*/
	err = db.Model(&model.PlayHistory{}).Where("office_id=? and play_history_date=? and cart_no=?", officeId, playDate, cartNo).
		Find(&data).Error

	if err != nil {
		return nil, err
	}
	return data, nil
}

func getOperationInfoByTimeDurationV2(partitionKey string, tempData *model.PlayHistory) (*model.SnapshotData, error) {
	dateStr := tempData.PlayHistoryDate.Format("20060102")
	cartNoStr := strconv.Itoa(tempData.CartNo)
	startCheckTime := time.Now()
	if !tempData.ArrivalTime.IsZero() {
		startCheckTime = tempData.ArrivalTime
	}

	circleStartTime := startCheckTime
	timeDurations := make([]string, 0)
	for circleStartTime.Sub(tempData.StartTime) > 0 {
		timeDurations = append(timeDurations, circleStartTime.Format("15:04"))
		circleStartTime = circleStartTime.Add(-10 * time.Minute)
	}

	logging.LogFormat(enum.LogDebug, map[string]any{
		"category":      enum.LogCategoryApp,
		"message":       "cart info v2 play_history line operation-info by time duration ,cart_info_v2_debug",
		"module":        "CartInfoV2Index",
		"duration":      timeDurations,
		"partition_key": partitionKey,
		"date":          dateStr,
		"cart_no":       cartNoStr,
	})

	operationInfo, err := getNearestOperationInfo(partitionKey, dateStr, cartNoStr, timeDurations, "history_")
	if err != nil {
		logging.LogFormat(enum.LogDebug, map[string]any{
			"category":      enum.LogCategoryApp,
			"message":       "cart info v2 play_history line getNearestOperationInfo err ,cart_info_v2_debug",
			"module":        "CartInfoV2Index",
			"duration":      timeDurations,
			"partition_key": partitionKey,
			"date":          dateStr,
			"cart_no":       cartNoStr,
			"err":           err,
		})
		return nil, err
	}
	slog.Debug("debug :operationInfo :", "operationInfo", operationInfo)
	if operationInfo == nil {
		errMsg := "cant found operationInfo by detect from " + startCheckTime.String() + " office_key is  :" + partitionKey
		slog.Warn(errMsg)
		logging.LogFormat(enum.LogDebug, map[string]any{
			"category":      enum.LogCategoryApp,
			"message":       "cart info v2 play_history line get operation data nil ,cart_info_v2_debug",
			"module":        "CartInfoV2Index",
			"duration":      timeDurations,
			"partition_key": partitionKey,
			"date":          dateStr,
			"cart_no":       cartNoStr,
		})
		return nil, errors.New(errMsg)
	}

	return operationInfo, nil
}

func GetCartInfoNewVersion(requestId, officeKey, officeId string, cartNo int) (map[string]response.CartInfoData, error) {
	// obtain play_history data
	//playDate := "2024-12-06"
	playDate := time.Now().Format("2006-01-02")
	officeIdInt, err := strconv.Atoi(officeId)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryAuth,
			"message":  "cart info v2 play_history office_id parse int error office_id:" + officeId,
			"module":   "CartInfoV2Index",
			"request":  requestId,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
				},
			},
		})
		return nil, err
	}
	playHistories, err := GetPlayHistoryFromRDS(officeIdInt, cartNo, playDate)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryAuth,
			"message":  "cart info v2 play_history error office_id:" + officeId,
			"module":   "CartInfoV2Index",
			"request":  requestId,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
				},
			},
		})
		return nil, err
	}

	logging.LogFormat(enum.LogDebug, map[string]any{
		"category":     enum.LogCategoryApp,
		"message":      "cart info v2 play_history data from rds,cart_info_v2_debug",
		"module":       "CartInfoV2Index",
		"request":      requestId,
		"history_err":  err,
		"history_data": playHistories,
		"cart_no":      cartNo,
	})

	data := make(map[string]response.CartInfoData, 0)
	var concurrent sync.WaitGroup
	cartNoStr := strconv.Itoa(cartNo)
	for _, playHistory := range playHistories {
		if playHistory.StartTime.IsZero() {
			logging.LogFormat(enum.LogWarn, map[string]any{
				"category": enum.LogCategoryAuth,
				"message":  "cart info v2 play_history start time is empty skip this row",
				"module":   "CartInfoV2Index",
				"request":  requestId,
				"cart_no":  cartNo,
				"data":     playHistory,
				"param": map[string]any{
					"req": map[string]any{
						"method": "get",
					},
				},
			})

			continue
		}

		concurrent.Add(1)
		go func(playHistory model.PlayHistory) {
			defer concurrent.Done()

			var snapshot *model.SnapshotData
			var tempData response.CartInfoData
			snapshot, err = getOperationInfoByTimeDurationV2(officeKey, &playHistory)
			if err != nil || snapshot == nil {
				logging.LogFormat(enum.LogWarn, map[string]any{
					"category": enum.LogCategoryAuth,
					"message":  "cart info v2 operation-info get info is empty",
					"module":   "CartInfoV2Index",
					"request":  requestId,
					"cart_no":  cartNo,
					"data":     playHistory,
					"param": map[string]any{
						"req": map[string]any{
							"method": "get",
						},
					},
				})
				return
			}

			snapshotStartTime, err := time.Parse(time.RFC3339, snapshot.StartTime)
			if err != nil {
				slog.Warn("snapshot.StartTime time parse error", "error", err, "time_str", snapshot.StartTime)
			} else {
				tempData.StartTime = snapshotStartTime.Format("15:04")
			}

			tempData.CartNo = cartNoStr
			tempData.PlayedDate = playDate
			tempData.CaddyID = snapshot.CaddieNo
			tempData.CaddyName = snapshot.CaddieName
			tempData.UpdatedAt = snapshot.ArrivalTime
			tempData.StartCourse = "" //snapshot.CourseIndex
			tempData.CartPlayer = tidySnapshotPlayer(snapshot.Players)
			// course name
			courseSetting, err2 := getCourseNameByCourseSetting(officeIdInt, snapshot.CourseIndex)
			if err2 == nil {
				tempData.StartCourse = courseSetting.CourseName
			}

			keyName := fmt.Sprintf("%s_%d_%s_%s", playDate, cartNo, snapshot.CaddieNo, snapshot.StartTime)
			data[keyName] = tempData
		}(playHistory)

	}
	// wait all goroutine over
	concurrent.Wait()

	// filter same date user repeat
	filterPlayerHistoryRepeatData(data)

	scorePrimaryKey := model.MarshaliDynamoDb{
		PartitionKey: officeKey,
		SortKey:      "score_" + time.Now().Format("20060102"),
		//SortKey: "score_20241120",
	}
	scoreData, err := getScoreUnderlineData(scorePrimaryKey, cartNoStr)
	if err != nil {
		logging.LogFormat(enum.LogWarn, map[string]any{
			"category": enum.LogCategoryAuth,
			"message":  "cart info v2 score_ get info err",
			"module":   "CartInfoV2Index",
			"request":  requestId,
			"error":    err.Error(),
		})
	} else { // merge score and print key
		tidyScoreInfo(data, scoreData)
	}

	logging.LogFormat(enum.LogDebug, map[string]any{
		"category": enum.LogCategoryApp,
		"message":  "cart info v2 play_history line get data end and return operation-info data,cart_info_v2_debug",
		"module":   "CartInfoV2Index",
		"cart_no":  cartNo,
		"request":  requestId,
		"data":     data,
	})
	return data, err
}

// filter by userinfo and date
func filterPlayerHistoryRepeatData(data map[string]response.CartInfoData) {
	filterKeys := map[string]string{}
	for key, playHistory := range data {
		playerIdString := getPlayersSortedIdString(playHistory.CartPlayer)
		filterKey := fmt.Sprintf("%s_%s", playHistory.PlayedDate, playerIdString)

		playHistory.Serial = filterKey
		data[key] = playHistory // fill serial value

		if existKey, ok := filterKeys[filterKey]; ok {
			if data[existKey].StartTime < playHistory.StartTime {
				delete(data, existKey)      // delete old key against data
				filterKeys[filterKey] = key // refresh key
			} else {
				delete(data, key) // delete key against data
			}
		} else {
			filterKeys[filterKey] = key //save or refresh key
		}
	}
}

func getCourseNameByCourseSetting(officeId, courseIndex int) (*model.CourseSettingDetail, error) {
	input := &dynamodb.QueryInput{
		TableName:              aws.String(marshalIDynamodbName),
		KeyConditionExpression: aws.String("partition_key = :office_key and sort_key = :sort_key"),
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":office_key": &types.AttributeValueMemberS{Value: "#course-setting"},
			":sort_key":   &types.AttributeValueMemberS{Value: fmt.Sprintf("%d_%d", officeId, courseIndex)},
		},
		ScanIndexForward: aws.Bool(false), // 升序排序
	}
	output, err := dynamo.Query(input)
	if err != nil {
		return nil, err
	}
	itemList := []*model.MarshaliDynamoDb{}
	if err := attributevalue.UnmarshalListOfMaps(output.Items, &itemList); err != nil {
		return nil, err
	}
	courseDetail := model.CourseSettingDetail{}
	if output.Count > 0 {
		if err = common.ConvertStruct(itemList[0].Details, &courseDetail); err != nil {
			return nil, err
		} else {
			fmt.Println(courseDetail.CourseName, courseDetail.CourseIndex, courseDetail.OfficeId)
			return &courseDetail, nil
		}
	} else {
		return &courseDetail, nil
	}
}

func tidySnapshotPlayer(snapshotPlayers []model.SnapPlayer) []response.CartPlayer {
	var tempPlayers []response.CartPlayer
	for _, player := range snapshotPlayers {
		var cartPlayer response.CartPlayer
		cartPlayer.Id = player.PlayerNo
		cartPlayer.Name = player.PlayerName
		cartPlayer.ClubChecked = false

		tempPlayers = append(tempPlayers, cartPlayer)
	}
	// asc by name sorted
	sort.Slice(tempPlayers, func(i, j int) bool {
		return tempPlayers[i].Name < tempPlayers[j].Name
	})
	return tempPlayers
}

func getScoreUnderlineData(RequestMarshalIDynamoDb model.MarshaliDynamoDb, cartNo string) (map[string]response.CartInfoData, error) {
	items, err := GetMultiItem(RequestMarshalIDynamoDb)
	if err != nil {
		return nil, err
	} else if len(items) == 0 { // data not found
		return nil, nil
	}

	sort.Slice(items, func(i, j int) bool {
		firstDate, _ := time.Parse(time.RFC3339, items[i].UpdatedAt)
		secondDate, _ := time.Parse(time.RFC3339, items[j].UpdatedAt)
		return firstDate.Before(secondDate)
	})

	var data = map[string]response.CartInfoData{}
	dataFilter := make(map[string]map[string]model.PlayerScore)
	for _, item := range items {
		scoreDetail := model.ScoreDetail{}
		if err = common.ConvertStruct(item.Details, &scoreDetail); err != nil {
			return nil, err
		} else {
			if item.CartNo != cartNo { // not target cartNo jump
				continue
			}

			headerInfo := model.ScoreHeader{}
			if err = common.ConvertStruct(item.Header, &headerInfo); err != nil {
				slog.Error("")
			}

			tempStartTime, err := time.Parse(time.RFC3339, item.UpdatedAt)
			startTime := ""
			if err != nil {
				slog.Warn("score-log_ updated_at parse time error value :" + item.UpdatedAt)
			} else {
				startTime = tempStartTime.Format("15:04")
			}
			startCourse := scoreDetail.ScheduledStartCourseIndex
			if len(startCourse) > 0 && len(scoreDetail.Courses) > 0 {
				courseIndex, err := strconv.Atoi(startCourse)
				if err != nil {
					startCourse = ""
				} else if len(scoreDetail.Courses) > courseIndex {
					startCourse = scoreDetail.Courses[courseIndex].CourseName
				}
			} else if len(startCourse) < 0 && len(scoreDetail.Courses) > 0 {
				//startCourse = scoreDetail.Courses[0].CourseIndex
				startCourse = scoreDetail.Courses[0].CourseName
			} else {
				startCourse = ""
			}

			playerKeys, existScore := GetPlayerInfo(scoreDetail.PlayerScore)
			firstKey := fmt.Sprintf("%s_%s_%s_%s_%s", item.CartNo, scoreDetail.ScheduledStartCourseIndex, scoreDetail.ScheduledStartTime, scoreDetail.CaddieNo, playerKeys)
			if _, ok := dataFilter[firstKey]; ok {
				// repeat data filter , skip old data
				if data[firstKey].UpdatedAt >= scoreDetail.UpdatedAt {
					continue
				}

				var tempData response.CartInfoData
				tempData.CartNo = item.CartNo
				tempData.PlayedDate = headerInfo.PlayDate
				tempData.StartTime = startTime
				tempData.CaddyID = scoreDetail.CaddieNo
				tempData.CaddyName = scoreDetail.CaddieName
				tempData.StartCourse = startCourse
				tempData.UpdatedAt = scoreDetail.UpdatedAt

				for _, player := range scoreDetail.PlayerScore {
					var cartPlayer response.CartPlayer
					cartPlayer.Id = player.PlayerNo
					cartPlayer.Name = player.PlayerName
					cartPlayer.ClubChecked = false

					tempData.CartPlayer = append(tempData.CartPlayer, cartPlayer)
				}

				tempData.ExistScore = existScore
				tempData.SortKey = item.SortKey

				data[firstKey] = tempData
			} else {
				var tempData response.CartInfoData
				tempData.CartNo = item.CartNo
				tempData.PlayedDate = headerInfo.PlayDate
				tempData.StartTime = startTime
				tempData.CaddyID = scoreDetail.CaddieNo
				tempData.CaddyName = scoreDetail.CaddieName
				tempData.StartCourse = startCourse
				tempData.UpdatedAt = scoreDetail.UpdatedAt

				for _, player := range scoreDetail.PlayerScore {
					if _, ok := dataFilter[firstKey][player.PlayerNo]; ok {
						continue
					} else {
						var cartPlayer response.CartPlayer
						cartPlayer.Id = player.PlayerNo
						cartPlayer.Name = player.PlayerName
						cartPlayer.ClubChecked = false

						if _, exists := dataFilter[firstKey]; !exists {
							dataFilter[firstKey] = make(map[string]model.PlayerScore)
						}
						dataFilter[firstKey][player.PlayerNo] = player
						tempData.CartPlayer = append(tempData.CartPlayer, cartPlayer)
					}
				}

				tempData.ExistScore = existScore
				tempData.SortKey = item.SortKey

				data[firstKey] = tempData

			}

		}
	}

	return data, nil
}

func tidyScoreInfo(operationInfo, scoreInfo map[string]response.CartInfoData) {
	if len(operationInfo) < 1 || len(scoreInfo) < 1 {
		return
	}

	for key, data := range operationInfo {
		for _, scoreData := range scoreInfo {
			if data.CartNo == scoreData.CartNo && ComparePlayerInfo(data.CartPlayer, scoreData.CartPlayer) {
				data.SortKey = scoreData.SortKey
				data.ExistScore = scoreData.ExistScore

				operationInfo[key] = data
			}
		}
	}
}

func ComparePlayerInfo(players, players2 []response.CartPlayer) bool {
	return getPlayersSortedIdString(players) == getPlayersSortedIdString(players2)
}

func getPlayersSortedIdString(players []response.CartPlayer) string {
	var playerIds []string
	for _, ps := range players {
		playerIds = append(playerIds, strings.TrimSpace(strings.ToLower(ps.Id)))
	}
	sort.Strings(playerIds)

	return strings.Join(playerIds, "-")
}
