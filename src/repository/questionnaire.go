package repository

import (
	"fmt"
	"mi-restful-api/client"
	"mi-restful-api/enum"
	"mi-restful-api/exception"
	"mi-restful-api/model"
	"mi-restful-api/request"
	"time"

	"gorm.io/gorm"
)

type CommentToDb struct {
	ConnMySQL *gorm.DB
}

func (svc *CommentToDb) StoreAnswerData(oriData request.QuestionnaireCreateReq, officeId string) (int, error) {
	// query data
	serial := oriData.Serial

	weekday, err := GroupWeekday(time.Now())
	if err != nil {
		return 0, err
	}

	var questionnaire *model.Questionnaire
	questionnaire = svc.questionnaireDataFormat(&oriData, weekday)
	questionnaire.Serial = serial
	questionnaire.OfficeID = officeId

	player := svc.playerDataFormat(&oriData)
	player.OfficeID = officeId

	surveys := svc.surveyDataFormat(&oriData, weekday)

	caddyInfo := svc.caddyDataFormat(&oriData)
	caddyInfo.OfficeID = officeId

	err = svc.ConnMySQL.Transaction(func(tx *gorm.DB) error {
		tx.Model(&model.Questionnaire{}).Where("office_id=? and serial=?", officeId, serial).First(&questionnaire)
		if questionnaire.ID == 0 {
			// insert table Evaluation
			if err := tx.Create(&questionnaire).Error; err != nil {
				return exception.NewError(enum.DBSQLEXECErrorCode, err.Error())
			}
		}

		// get Evaluation ID attach Player table
		player.QuestionnaireId = questionnaire.ID
		if err := tx.Create(&player).Error; err != nil {
			return exception.NewError(enum.DBSQLEXECErrorCode, err.Error())
		}

		// insert custom feedback
		if len(oriData.Feedback) > 0 || len(oriData.FeedbackCaddy) > 0 || len(oriData.FeedbackGolf) > 0 {
			var feedback model.PlayerFeedback
			feedback.OfficeID = officeId
			feedback.PlayerID = player.ID
			feedback.Content = oriData.Feedback
			feedback.ContentCaddy = oriData.FeedbackCaddy
			feedback.ContentGolf = oriData.FeedbackGolf
			if err := tx.Create(&feedback).Error; err != nil {
				return exception.NewError(enum.DBSQLEXECErrorCode, err.Error())
			}
		}

		// use return Player ID insert Answer table
		if len(surveys) > 0 { // save question comment， adapter allow comment is empty
			for idx, _ := range surveys {
				surveys[idx].OfficeID = officeId
				surveys[idx].PlayerId = player.ID
			}

			if err := tx.Create(&surveys).Error; err != nil {
				return exception.NewError(enum.DBSQLEXECErrorCode, err.Error())
			}
		}

		// no error auto commit
		return nil
	})

	if err != nil {
		return 0, err
	}
	// if caddie_no is empty ,set caddie_no string 0 that for web display foreign object.
	if caddyInfo.CaddyId == "" {
		caddyInfo.CaddyId = "0"
	}
	// find or create
	if len(caddyInfo.CaddyId) > 0 {
		svc.ConnMySQL.Model(&model.Caddy{}).Where("office_id=? and caddy_id=?", officeId, caddyInfo.CaddyId).
			First(&caddyInfo)
		if caddyInfo.ID == 0 {
			svc.ConnMySQL.Model(&model.Caddy{}).Create(&caddyInfo)
		}
	}

	return questionnaire.ID, nil

}

func (svc *CommentToDb) questionnaireDataFormat(req *request.QuestionnaireCreateReq, weekday int) *model.Questionnaire {
	nowTime := time.Now()
	// prepare insert data
	var data model.Questionnaire
	data.CartNo = req.CartNo
	data.CaddyID = req.CaddyId
	data.CreatedAt = nowTime
	data.PlayedDate = req.PlayedDate
	data.StartTime = req.StartTime
	data.StartCourse = req.StartCourse
	data.Weekday = weekday

	return &data
}

func GroupWeekday(addTime time.Time) (int, error) {
	weekday := addTime.Weekday()
	switch weekday {
	case 0:
		return 2, nil
	case 1:
		fallthrough
	case 2:
		fallthrough
	case 3:
		fallthrough
	case 4:
		fallthrough
	case 5:
		return HolidayCheckWeekday(addTime)
	case 6:
		return 2, nil
	}
	return 0, exception.NewError(enum.TimeWeekdayError, fmt.Sprintf("weekday type not match ,%s", addTime.Format(time.RFC3339)))
}

func HolidayCheckWeekday(addTime time.Time) (int, error) {
	date := addTime.Format("2006-01-02")
	db, err := client.GetMncDBClient()
	if err != nil {
		return 0, exception.NewError(enum.DBConnErrorCode, err.Error())
	}

	var count int64
	err = db.Model(&model.Holiday{}).Where("holiday_date = ?", date).Count(&count).Error
	if err != nil {
		return 0, exception.NewError(enum.DBSQLEXECErrorCode, err.Error())
	}
	if count > 0 {
		return 2, nil
	} else {
		return 1, nil
	}
}

func (svc *CommentToDb) playerDataFormat(req *request.QuestionnaireCreateReq) *model.Player {
	// prepare insert data
	var data model.Player
	data.PlayerName = req.PlayerName
	data.PlayerId = req.PlayerId

	return &data
}

func (svc *CommentToDb) surveyDataFormat(req *request.QuestionnaireCreateReq, weekday int) []model.Answer {
	var answers []model.Answer
	createAt := time.Now()
	for _, survey := range req.Surveys {
		var temp model.Answer
		temp.CreatedAt = createAt
		temp.QuestionId = survey.Id
		temp.CommentId = survey.AnswerId
		temp.CaddyId = req.CaddyId
		temp.Weekday = weekday

		answers = append(answers, temp)
	}
	return answers
}

func (svc *CommentToDb) caddyDataFormat(req *request.QuestionnaireCreateReq) model.Caddy {
	var caddy model.Caddy
	caddy.CaddyId = req.CaddyId
	caddy.CaddyName = req.CaddyName
	caddy.CreatedAt = time.Now()
	return caddy
}
