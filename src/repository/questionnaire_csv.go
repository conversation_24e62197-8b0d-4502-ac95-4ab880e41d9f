package repository

import (
	"bytes"
	"encoding/csv"
	"fmt"
	"mi-restful-api/client"
	"mi-restful-api/enum"
	"mi-restful-api/exception"
	"mi-restful-api/model"
	"strconv"
	"time"
)

type DateToCsv struct {
}

func (svc *DateToCsv) ExportQuestionnaireCsv(data []model.Questionnaire, officeId string) (*bytes.Buffer, error) {
	// use bytes.B<PERSON><PERSON> create CSV data
	return svc.tidyCsvData(data, officeId)
}

func (svc *DateToCsv) questions(officeId string) []model.Question {
	db, err := client.GetDbClient()
	if err != nil {
		return nil
	}
	var questions []model.Question
	db.Model(&model.Question{}).Where("office_id=? ", officeId).
		Order("`type` asc,`index` asc").Find(&questions)

	return questions
}

func (svc *DateToCsv) evaluation(officeId string) []model.Evaluation {
	db, err := client.GetDbClient()
	if err != nil {
		return nil
	}
	var evaluations []model.Evaluation
	db.Model(&model.Evaluation{}).Where("office_id=? ", officeId).
		Find(&evaluations)

	return evaluations
}

func (svc *DateToCsv) caddies(officeId string) []model.Caddy {
	db, err := client.GetDbClient()
	if err != nil {
		return nil
	}
	var caddies []model.Caddy
	db.Model(&model.Caddy{}).Where("office_id=? ", officeId).
		Find(&caddies)

	return caddies
}

func (svc *DateToCsv) tidyCsvData(data []model.Questionnaire, officeId string) (*bytes.Buffer, error) {
	var buf bytes.Buffer
	// UTF-8 BOM bytes for excel detected
	bom := []byte{0xEF, 0xBB, 0xBF}
	buf.Write(bom)

	writer := csv.NewWriter(&buf)

	questions := svc.questions(officeId)
	evaluations := svc.evaluation(officeId)
	caddies := svc.caddies(officeId)

	var questionMap = map[int]model.Question{}
	var evaluationMap = map[int]model.Evaluation{}
	var caddyMap = map[string]model.Caddy{}
	var questionSlice []int
	for _, question := range questions {
		questionMap[question.ID] = question
		questionSlice = append(questionSlice, question.ID)
	}
	for _, evaluation := range evaluations {
		evaluationMap[evaluation.ID] = evaluation
	}
	for _, caddy := range caddies {
		caddyMap[caddy.CaddyId] = caddy
	}

	// writer CSV header
	headerBase := []string{"カート番号", "プレー日", "スタート", "名前", "キャディ", "自由入力"}
	for _, questionId := range questionSlice {
		if question, ok := questionMap[questionId]; ok {
			if question.Type == 1 {
				headerBase = append(headerBase, fmt.Sprintf("キャディ\n 設問 %d", question.Index))
			} else {
				headerBase = append(headerBase, fmt.Sprintf("ゴルフ場\n 設問 %d", question.Index))
			}
		}
	}
	headerBase = append(headerBase, "回答日時")
	if err := writer.Write(headerBase); err != nil {
		return nil, exception.NewError(enum.QuestionnaireExportCsvWriteHeaderError, err.Error())
	}

	for _, questionnaire := range data {
		for _, player := range questionnaire.Players {
			// player answers
			var answers = map[int]model.Answer{}
			for _, answer := range player.Answers {
				answers[answer.QuestionId] = answer
			}

			caddyName := ""
			if caddy, ok := caddyMap[questionnaire.CaddyID]; ok {
				caddyName = caddy.CaddyName
			}

			tempData := []string{questionnaire.CartNo, questionnaire.PlayedDate, questionnaire.StartCourse + "\n" + questionnaire.StartTime, player.PlayerName, caddyName, player.PlayerFeedback.Content, player.PlayerFeedback.ContentCaddy, player.PlayerFeedback.ContentGolf}
			for _, questionId := range questionSlice {
				var score = ""
				if answer, ok := answers[questionId]; ok {
					if evaluation, evOk := evaluationMap[answer.CommentId]; evOk {
						score = strconv.Itoa(evaluation.Score)
					}
				}
				tempData = append(tempData, score)
			}

			tempData = append(tempData, player.CreatedAt.Format(time.DateTime))
			if err := writer.Write(tempData); err != nil {
				return nil, exception.NewError(enum.QuestionnaireExportCsvWriteDataError, err.Error())
			}
		}
	}

	writer.Flush()

	return &buf, nil
}
