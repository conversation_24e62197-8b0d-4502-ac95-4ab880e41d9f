package repository

import (
	"fmt"
	"mi-restful-api/enum"
	"mi-restful-api/exception"
	"mi-restful-api/model"
	"mi-restful-api/request"
	"net/http"

	"gorm.io/gorm"
)

type Question struct {
	ConnMySQL *gorm.DB
}

func (svc *Question) FindQuestion(officeId string) ([]model.Question, error) {
	var data []model.Question
	err := svc.ConnMySQL.Model(model.Question{}).
		Where("office_id =?", officeId).
		Order("`type`,`index`").
		Find(&data).Error
	return data, err
}

func (svc *Question) GetQuestion(officeId string, questionId int) (*model.Question, error) {

	var data model.Question
	err := svc.ConnMySQL.Model(model.Question{}).
		Where("office_id =?", officeId).
		Where("id =?", questionId).
		First(&data).Error
	if err != nil {
		err = exception.NewError(enum.DBSQLEXECErrorCode, err.Error())
	}

	return &data, err
}

func (svc *Question) UpdateQuestion(officeId string, questionId int, req request.PutQuestionParams) (*model.Question, error) {
	var data model.Question
	err := svc.ConnMySQL.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(model.Question{}).
			Where("office_id =?", officeId).
			Where("id =?", questionId).
			First(&data).Error
		if err != nil {
			err = exception.NewError(enum.DBSQLEXECErrorCode, err.Error())
		}

		data.Content = req.Content
		data.Require = req.Require

		err = tx.Model(model.Question{}).
			Where("id = ? and office_id = ?", questionId, officeId).
			Updates(&data).Error
		if err != nil {
			err = exception.NewError(enum.DBSQLEXECErrorCode, err.Error())
		}
		return nil
	})
	if err != nil {
		err = exception.NewError(enum.DBSQLEXECErrorCode, err.Error())
	}

	return &data, err
}

func (svc *Question) CreateQuestion(data model.Question, officeId string) (int, error) {

	err := svc.ConnMySQL.Transaction(func(tx *gorm.DB) error {
		var index int
		err := tx.Model(model.Question{}).
			Where("office_id = ? and `type`=? and deleted_at is null", officeId, data.Type).
			Select("IFNULL(max(`index`), 0) `index`").
			First(&index).Error
		if err != nil {
			return err
		}

		//index equal max index + 1
		index = index + 1
		data.Index = int8(index)
		err = tx.Model(model.Question{}).
			Create(&data).Error
		return err
	})
	if err != nil {
		return 0, exception.NewError(enum.DBSQLEXECErrorCode, err.Error())
	}

	return data.ID, nil
}

func (svc *Question) DeleteQuestion(questionId, officeId string) error {

	err := svc.ConnMySQL.Transaction(func(tx *gorm.DB) error {
		// get index by id
		var data model.Question
		err := tx.Model(model.Question{}).
			Where("office_id = ? and id= ?", officeId, questionId).
			First(&data).Error
		if err != nil {
			return exception.NewError(enum.DBSQLEXECErrorCode, err.Error())
		}

		if data.ID == 0 {
			return exception.NewError(http.StatusNotFound, gorm.ErrRecordNotFound.Error())
		}

		// delete question by id
		err = tx.Where("office_id = ? AND id = ?", officeId, questionId).Delete(&model.Question{}).Error
		if err != nil {
			return exception.NewError(enum.DBSQLEXECErrorCode, err.Error())
		}

		// update index
		err = tx.Model(&model.Question{}).
			Where("office_id = ? and `type`=?  and deleted_at is null", officeId, data.Type).
			Where("`index` > ?", data.Index).
			Update("`index`", gorm.Expr("`index` - ?", 1)).Error
		if err != nil {
			return exception.NewError(enum.DBSQLEXECErrorCode, err.Error())
		}

		return nil
	})

	return err
}

func (svc *Question) UpdateQuestionIndex(req *request.UpdateIndexParams, officeId string) error {
	err := svc.ConnMySQL.Transaction(func(tx *gorm.DB) error {
		// get question info by id
		type TempInfo struct {
			Index int  `gorm:"column:index"`
			Type  int8 `gorm:"column:type"`
		}
		var curInfo TempInfo
		err := tx.Model(model.Question{}).
			Where("id = ? and deleted_at is null", req.Id).
			Select("IFNULL(`index`, 0) `index`,`type`").
			First(&curInfo).Error
		if err != nil {
			return err
		}
		if curInfo.Index == int(req.Index) { // not need change index
			return nil
		}

		var indexMax int
		err = tx.Model(model.Question{}).
			Where("office_id = ? and `type`=? and deleted_at is null", officeId, curInfo.Type).
			Select("IFNULL(max(`index`), 0) `index`").
			First(&indexMax).Error
		if err != nil {
			return exception.NewError(enum.DBSQLEXECErrorCode, err.Error())
		}
		if int8(indexMax) < req.Index || req.Index < 1 {
			return fmt.Errorf("index need 1 ~ %d", indexMax)
		}

		if curInfo.Index > int(req.Index) { // move to before
			// update index
			err = tx.Model(&model.Question{}).
				Where("office_id = ? and `type`=?  and deleted_at is null", officeId, curInfo.Type).
				Where("`index` >= ? and `index` < ?", req.Index, curInfo.Index).
				Update("`index`", gorm.Expr("`index` + ?", 1)).Error
			if err != nil {
				return err
			}
		} else {
			// update index
			err = tx.Model(&model.Question{}).
				Where("office_id = ? and `type`=?  and deleted_at is null", officeId, curInfo.Type).
				Where("`index` > ? and `index` <= ?", curInfo.Index, req.Index).
				Update("`index`", gorm.Expr("`index` - ?", 1)).Error
			if err != nil {
				return err
			}
		}

		err = tx.Model(&model.Question{}).
			Where("office_id = ? and deleted_at is null", officeId).
			Where("id = ?", req.Id).
			Update("`index`", req.Index).Error
		if err != nil {
			return err
		}

		return nil
	})

	return err
}
