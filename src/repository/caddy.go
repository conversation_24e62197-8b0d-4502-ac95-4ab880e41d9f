package repository

import (
	"mi-restful-api/enum"
	"mi-restful-api/exception"
	"mi-restful-api/model"
	"net/http"

	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

type CaddyRepos struct {
	ConnMySQL      *gorm.DB
	ConnMySQLMncdb *gorm.DB
}

func (svc *CaddyRepos) CaddyFind(officeId string) ([]model.Caddy, error) {
	var data []model.Caddy
	err := svc.ConnMySQL.Model(&model.Caddy{}).Where("office_id=? ", officeId).
		Select("max(id) as id,caddy_id,caddy_name").
		Group("caddy_id,caddy_name").
		Order("caddy_id asc").
		Find(&data).Error

	return data, err
}

func (svc *CaddyRepos) CaddyOn(officeId, requestId string) (string, error) {
	restrictionTags := ""

	var data model.Office
	err := svc.ConnMySQLMncdb.Model(&model.Office{}).Where("office_id=? and deleted_at is null", officeId).
		First(&data).Error
	if err != nil {
		log.WithFields(log.Fields{"requestId": requestId, "error": err}).Error()
		if err == gorm.ErrRecordNotFound {
			return restrictionTags, exception.NewError(http.StatusNoContent, err.Error())
		} else {
			return restrictionTags, exception.NewError(enum.DBSQLEXECErrorCode, err.Error())
		}
	}

	var productType model.ProductType
	productType.ProductTypeId = data.ProductTypeId
	err = svc.ConnMySQLMncdb.Model(&model.ProductType{}).Where("product_type_id = ? and deleted_at is null", data.ProductTypeId).
		Select("restiction_tags").
		First(&productType).Error
	if err != nil {
		log.WithFields(log.Fields{"requestId": requestId, "error": err, "msg": "caddy on query product_type error"}).Error()
		if err == gorm.ErrRecordNotFound {
			return restrictionTags, exception.NewError(http.StatusNoContent, err.Error())
		} else {
			return restrictionTags, exception.NewError(enum.DBSQLEXECErrorCode, err.Error())
		}
	}
	restrictionTags = productType.RestrictionTags
	if data.ProductSubTypeId > 0 { //
		var productSubType model.ProductSubType

		productSubType.ProductSubTypeId = data.ProductSubTypeId
		productSubType.ProductTypeId = data.ProductTypeId
		svc.ConnMySQLMncdb.Model(&model.ProductSubType{}).Where("product_type_id = ? and product_subtype_id = ? and deleted_at is null", data.ProductTypeId, data.ProductSubTypeId).
			Select("restiction_tags").
			First(&productSubType)
		if len(productSubType.RestrictionTags) >= len(enum.CaddyOnTagName) {
			restrictionTags = productSubType.RestrictionTags
		}
	}

	return restrictionTags, err
}
