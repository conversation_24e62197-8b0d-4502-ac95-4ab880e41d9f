package logging

import (
	"log/slog"
	"mi-restful-api/enum"
	"time"
)

func LogFormat(level string, paramsMap map[string]any) {
	args := serializerParams(paramsMap)
	if args == nil {
		slog.Error("call log format but params info not give")
		return
	}

	args = append(args, "timestamp", time.Now().Format(time.RFC3339))
	switch level {
	case enum.LogTrace:
		fallthrough
	case enum.LogDebug:
		slog.Debug("", args...)
	case enum.LogInfo:
		slog.Info("", args...)
	case enum.LogWarn:
		slog.Warn("", args...)
	case enum.LogError:
		slog.Error("", args...)
	default:
		slog.Error("error log level setting error")
	}
}

func serializerParams(paramsMap map[string]any) []any {
	paramsLen := len(paramsMap)
	if paramsLen < 1 {
		return nil
	}

	params := make([]any, 0)
	for key, value := range paramsMap {
		params = append(params, key, value)
	}
	return params
}
