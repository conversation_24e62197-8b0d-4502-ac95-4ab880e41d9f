package logging

import (
	"log/slog"
	"os"
	"strings"
)

func Init() *slog.Logger {
	// init logger
	logLevel := logLevel(os.Getenv("APP_LOG_LEVEL"))
	opts := &slog.HandlerOptions{Level: logLevel}
	logger := slog.New(slog.NewJSONHandler(os.Stdout, opts))
	slog.SetDefault(logger)
	slog.SetLogLoggerLevel(logLevel)
	slog.Info("app logger loglevel", "loglevel", logLevel.String())
	return logger
}

func logLevel(lv string) slog.Level {
	switch strings.ToLower(lv) {
	case "debug":
		return slog.LevelDebug
	case "info":
		return slog.LevelInfo
	case "warn":
		return slog.LevelWarn
	case "error":
		return slog.LevelError
	default:
		return slog.LevelDebug
	}
}
