package model

import (
	"gorm.io/gorm"
	"time"
)

// Answer question
type Answer struct {
	ID       int `gorm:"primaryKey;autoIncrement;column:id" json:"id"`
	PlayerId int `gorm:"column:player_id" json:"player_id"`
	// question_id 問題id
	QuestionId int `gorm:"column:question_id" json:"question_id"`
	// comment_id 評価id
	CommentId int            `gorm:"column:comment_id" json:"comment_id"`
	CaddyId   string         `gorm:"column:caddy_id;type:varchar(64)" json:"caddy_id"`
	Weekday   int            `gorm:"column:weekday" json:"weekday"`
	CreatedAt time.Time      `gorm:"column:created_at;autoCreateTime:microseconds" json:"created_at"` // 評価作り時間
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at" json:"deleted_at"`                             // 削除時間
	OfficeID  string         `gorm:"column:office_id;type:varchar(64)" json:"office_id"`              // コース番号
}

func (Answer) TableName() string {
	return "answer"
}

type CaddyAnalysisQuery struct {
	Month      string `json:"month" gorm:"column:month"`
	QuestionId int    `json:"question_id" gorm:"column:question_id"`
	CommentId  int    `json:"comment_id" gorm:"column:comment_id"`
	CaddyId    string `json:"caddy_id" gorm:"column:caddy_id"`
	Count      int    `json:"count" gorm:"column:count"`
}

type StatisticalQuery struct {
	Month      string `json:"month" gorm:"column:month"`
	QuestionId int    `json:"question_id" gorm:"column:question_id"`
	CommentId  int    `json:"comment_id" gorm:"column:comment_id"`
	Count      int    `json:"count" gorm:"column:count"`
}

type AnswerLowestQuery struct {
	QuestionId int `json:"question_id" gorm:"column:question_id"`
	CommentId  int `json:"comment_id" gorm:"column:comment_id"`
	Score      int `json:"score" gorm:"column:min_score"`
}
