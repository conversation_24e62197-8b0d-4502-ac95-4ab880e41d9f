package model

import (
	"time"
)

type Player struct {
	ID         int    `gorm:"primaryKey;autoIncrement;column:id" json:"id"`
	PlayerId   string `gorm:"column:player_id;type:varchar(64)" json:"player_id"`
	PlayerName string `gorm:"column:player_name;type:varchar(64)" json:"player_name"`
	// 評価テーブルid
	QuestionnaireId int            `gorm:"column:questionnaire_id" json:"questionnaire_id"`
	OfficeID        string         `gorm:"column:office_id;type:varchar(64)" json:"office_id"` // コース番号
	CreatedAt       time.Time      `gorm:"column:created_at;autoCreateTime:microseconds" json:"created_at"`
	Answers         []Answer       `gorm:"foreignKey:PlayerId;references:ID"`
	PlayerFeedback  PlayerFeedback `gorm:"foreignKey:PlayerID;references:ID"`
}

func (Player) TableName() string {
	return "player"
}

type PlayerQuery struct {
	CartNo          string `gorm:"column:cart_no;type:varchar(64)" json:"cart_no"`   // カート番号
	CaddyID         string `gorm:"column:caddy_id;type:varchar(64)" json:"caddy_id"` // キャディID
	PlayedDate      string `gorm:"column:played_date;type:varchar(30)" json:"played_date"`
	StartTime       string `gorm:"column:start_time;type:varchar(30)" json:"start_time"`
	StartCourse     string `gorm:"column:start_course;type:varchar(191)" json:"start_course"`
	Id              int    `gorm:"column:id" json:"id"`
	PlayerId        string `gorm:"column:player_id;type:varchar(64)" json:"player_id"`
	PlayerName      string `gorm:"column:player_name" json:"player_name"`
	QuestionnaireId int    `gorm:"column:questionnaire_id" json:"questionnaire_id"`
	SortKey         string `gorm:"column:serial" json:"serial"`
}
