package model

// アンケート各種設定表的结构体
type QuesnaireSettings struct {
	ID                int            `gorm:"primaryKey;autoIncrement;column:id" json:"id"`                                  // 主キーID
	CaddyNameType     int            `gorm:"column:caddy_name_type;default:1;not null;" json:"caddy_name_type"`             // キャディの名前種類：1(キャディ)、2(コースアテンダント)
	OfficeID          string         `gorm:"column:office_id;type:varchar(64)" json:"office_id"`                            // office_id
}

// TableName 設置 GORM テーブル名前
func (QuesnaireSettings) TableName() string {
	return "quesnaire_settings"
}
