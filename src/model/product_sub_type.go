package model

import "time"

type ProductSubType struct {
	ProductTypeId      int       `gorm:"column:product_type_id"`
	ProductSubTypeId   int       `gorm:"column:product_subtype_id"`
	ProductSubTypeName string    `gorm:"column:product_subtype_name"`
	RestrictionTags    string    `gorm:"column:restiction_tags"`
	DeletedAt          time.Time `gorm:"column:deleted_at"`
}

func (*ProductSubType) TableName() string {
	return "product_subtype"
}
