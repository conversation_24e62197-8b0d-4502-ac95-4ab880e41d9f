package tee

import "time"

type TeeSheet struct {
	PartitionKey   string         `json:"partition_key" dynamodbav:"partition_key"` // office_key
	SortKey        string         `json:"sort_key" dynamodbav:"sort_key"`           // tee_sheet_${date}_${cart_no}_${start_time}
	Details        TeeSheetDetail `json:"details" dynamodbav:"details"`
	ExpirationTime int64          `json:"expiration_time" dynamodbav:"expiration_time"`
	UpdatedAt      time.Time      `json:"updated_at" dynamodbav:"updated_at"`
}

type TeeSheetDetail struct {
	CartNo                    string      `json:"cart_no" dynamodbav:"cart_no"`
	ScheduledStartCourseIndex string      `json:"scheduled_start_course_index" dynamodbav:"scheduled_start_course_index"`
	ScheduledStartTime        string      `json:"scheduled_start_time" dynamodbav:"scheduled_start_time"`
	StartCourseIndex          string      `json:"start_course_index" dynamodbav:"start_course_index"`
	StartTime                 string      `json:"start_time" dynamodbav:"start_time"`
	TeePlayer                 []TeePlayer `json:"tee_player" dynamodbav:"tee_player"`
}

type TeePlayer struct {
	PlayerNo    int    `json:"player_no" dynamodbav:"player_no"`
	PlayerName  string `json:"player_name" dynamodbav:"player_name"`
	Gender      int    `json:"gender" dynamodbav:"gender"`     //１：男性、２：女性
	Birthday    string `json:"birthday" dynamodbav:"birthday"` // eg 1942/10/13
	GlidNo      string `json:"glid_no" dynamodbav:"glid_no"`
	TeeId       string `json:"tee_id" dynamodbav:"tee_id"`
	Hdcp        string `json:"hdcp" dynamodbav:"hdcp"`
	HdcpIndex   string `json:"hdcp_index" dynamodbav:"hdcp_index"`
	OfficeKey   string `json:"office_key" dynamodbav:"office_key"`
	PlayDate    string `json:"play_date" dynamodbav:"play_date"`
	CartNo      int    `json:"cart_no" dynamodbav:"cart_no"`
	LockerNo    string `json:"locker_no" dynamodbav:"locker_no"`
	ScoreHash   string `json:"score_hash" dynamodbav:"score_hash"`
	OldCompeNo  string `json:"old_compe_no" dynamodbav:"old_compe_no"`
	PrivateHdcp string `json:"private_hdcp" dynamodbav:"private_hdcp"`
	WHSHdcp     string `json:"whs_hdcp" dynamodbav:"whs_hdcp"`
}

type TeeInfo struct {
	TeeId        string `json:"tee_id" dynamodbav:"tee_id"`
	MenTeeName   string `json:"men_tee_name" dynamodbav:"men_tee_name"`
	WomenTeeName string `json:"women_tee_name" dynamodbav:"women_tee_name"`
}

type OfficeTeeInfo struct {
	PartitionKey string `json:"partition_key" dynamodbav:"partition_key"`
	SortKey      string `json:"sort_key" dynamodbav:"sort_key"`
	Details      struct {
		JsysMemberShipFlg string    `json:"jsys_membership_flg" dynamodbav:"jsys_membership_flg"`
		TeeInfo           []TeeInfo `json:"tee_info" dynamodbav:"tee_info"`
	} `json:"details" dynamodbav:"details"`
	ExpirationTime int64     `json:"expiration_time" dynamodbav:"expiration_time"`
	UpdatedAt      time.Time `json:"updated_at" dynamodbav:"updated_at"`
}
