package course

type Hole struct {
	HoleIndex  string `json:"hole_index"`
	GreenIndex string `json:"green_index"`
	UsedHdcp   string `json:"used_hdcp"`
	UsedPar    string `json:"used_par"`
}

type Course struct {
	CourseIndex string `json:"course_index"`
	CourseName  string `json:"course_name"`
	Holes       []Hole `json:"holes"`
	StartHole   string `json:"start_hole"`
}

type CourseSetting struct {
	CourseIndex int    `json:"course_index" gorm:"column:course_index"`
	CourseName  string `json:"course_name" gorm:"column:course_name"`
	StartHoleNo int    `json:"start_hole_no" gorm:"column:start_hole_no"`
}

func (CourseSetting) TableName() string {
	return "course_setting"
}

type HoleSetting struct {
	HoleIndex  int `json:"hole_index" gorm:"column:hole_index"`
	GreenIndex int `json:"green_index" gorm:"column:green_index"`
	Par        int `json:"par" gorm:"column:par"`
	Hdcp       int `json:"hdcp" gorm:"column:hdcp"`
}

func (HoleSetting) TableName() string {
	return "hole_setting"
}
