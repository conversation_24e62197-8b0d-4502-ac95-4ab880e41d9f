package model

import (
	"gorm.io/gorm"
	"time"
)

// Evaluation 表示用評価表の構造体
type Evaluation struct {
	ID        int            `gorm:"primaryKey;autoIncrement;column:id" json:"id"`                    // 主キーID
	Stage     int            `gorm:"column:stage" json:"stage"`                                       // ステージ
	Score     int            `gorm:"column:score" json:"score"`                                       // スコア
	Content   string         `gorm:"column:content" json:"content"`                                   // 内容
	CreatedAt time.Time      `gorm:"column:created_at;autoCreateTime:microseconds" json:"created_at"` // 作り時間
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at" json:"deleted_at"`                             // 削除時間
	OfficeID  string         `gorm:"primaryKey;column:office_id;type:varchar(64)" json:"office_id"`   // コース番号
}

// TableName 設置 GORM テーブル名前
func (Evaluation) TableName() string {
	return "evaluation"
}
