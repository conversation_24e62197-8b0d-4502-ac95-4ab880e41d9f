package model

import (
	"gorm.io/gorm"
	"time"
)

// Caddy list
type Caddy struct {
	ID        int            `gorm:"primaryKey;autoIncrement;column:id" json:"id"`
	CaddyId   string         `gorm:"column:caddy_id;type:varchar(64)" json:"caddy_id"`
	CaddyName string         `gorm:"column:caddy_name;type:varchar(64)" json:"caddy_name"`
	CreatedAt time.Time      `gorm:"column:created_at;autoCreateTime:microseconds" json:"created_at"` // 評価作り時間
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at" json:"deleted_at"`                             // 削除時間
	OfficeID  string         `gorm:"column:office_id;type:varchar(64)" json:"office_id"`              // コース番号
}

func (Caddy) TableName() string {
	return "caddy"
}
