package model

import (
	"gorm.io/gorm"
	"time"
)

// Question 表示用問題表の構造体
type Question struct {
	ID        int            `gorm:"primaryKey;autoIncrement;column:id" json:"id"`                    // 主キーID
	Index     int8           `gorm:"column:index" json:"index"`                                       // 問題のソートキー
	Type      int8           `gorm:"column:type" json:"type"`                                         // 問題タイプ
	Content   string         `gorm:"column:content" json:"content"`                                   // 問題内容
	Require   int8           `gorm:"column:require" json:"require"`                                   // 問題必要フラグ
	CreatedAt time.Time      `gorm:"column:created_at;autoCreateTime:microseconds" json:"created_at"` // 問題作り時間
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at" json:"deleted_at"`                             // 削除時間
	OfficeID  string         `gorm:"column:office_id;type:varchar(64)" json:"office_id"`              // コース番号
}

// TableName 設置 GORM テーブル名前
func (Question) TableName() string {
	return "question"
}
