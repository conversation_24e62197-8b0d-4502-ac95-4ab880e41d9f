package model

// スタート案内設定表的结构体
type StartGuideSettings struct {
	ID                int            `gorm:"primaryKey;autoIncrement;column:id" json:"id"`                                  // 主キーID
	OfficeID          string         `gorm:"column:office_id;type:varchar(64)" json:"office_id"`                            // office_id
	EnableStartTime   *int           `gorm:"column:enable_start_time;default:0;not null;" json:"enable_start_time"`         // スタート予定時間表示
	MainTextAlways    *string        `gorm:"column:main_text_always;type:varchar(30)" json:"main_text_always"`              // メイン文言（常時表示）
	SubTextAlways     *string        `gorm:"column:sub_text_always;type:varchar(45)" json:"sub_text_always"`                // サブ文言（常時表示）
	EnableAutoStart   *int           `gorm:"column:enable_autostart;default:0;not null;" json:"enable_autostart"`           // 自動スタート案内表示
	AutoStartType     *int           `gorm:"column:autostart_type;default:0;not null;" json:"autostart_type"`               // 自動スタート案内基準
	StartTimeSchedule *int           `gorm:"column:start_time_schedule;default:1;not null;" json:"start_time_schedule"`     // スタート予定時間
	StartNumber       *int           `gorm:"column:start_number;default:1;not null;" json:"start_number"`                   // スタートまでの順番
	SubTextAuto       *string        `gorm:"column:sub_text_auto;type:varchar(45)" json:"sub_text_auto"`                    // サブ文言（自動案内）
}

// TableName 設置 GORM テーブル名前
func (StartGuideSettings) TableName() string {
	return "start_guide_settings"
}
