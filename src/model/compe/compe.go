package compe

import (
	"time"
)

type OnlineCompe struct {
	PartitionKey string `json:"partition_key" dynamodbav:"partition_key"`
	Sort<PERSON>ey      string `json:"sort_key" dynamodbav:"sort_key"`
	Details      struct {
		Basic            Basic            `json:"basic" dynamodbav:"basic"`
		CompeSetting     CompeSetting     `json:"compe_setting" dynamodbav:"compe_setting"`
		CompeTypeSetting CompeTypeSetting `json:"compe_type_setting" dynamodbav:"compe_type_setting"`
		OtherSetting     OtherSetting     `json:"other_setting" dynamodbav:"other_setting"`
	} `json:"details" dynamodbav:"details"`
	ExpirationTime int64     `json:"expiration_time" dynamodbav:"expiration_time"`
	UpdatedAt      time.Time `json:"updated_at" dynamodbav:"updated_at"`
}

type OfficeCompe struct {
	OfficeKey      string             `json:"partition_key" dynamodbav:"partition_key"`
	So<PERSON><PERSON><PERSON>        string             `json:"sort_key" dynamodbav:"sort_key"`
	Details        OfficeCompeDetails `json:"details" dynamodbav:"details"`
	ExpirationTime int64              `json:"expiration_time" dynamodbav:"expiration_time"`
	UpdatedAt      time.Time          `json:"updated_at" dynamodbav:"updated_at"`
}

type OfficeCompeDetails struct {
	CompeNo              int                 `json:"compe_no" dynamodbav:"compe_no"`
	CompeName            string              `json:"compe_name" dynamodbav:"compe_name"`
	CourseSetting        map[string]string   `json:"course_setting"  dynamodbav:"course_setting"` //コース設定
	HiddenHole           []HiddenHoleSetting `json:"hidden_hole"  dynamodbav:"hidden_hole"`       //隠しホール設定 list
	PrizeConditionSetted bool                `json:"prize_condition_setted" dynamodbav:"prize_condition_setted"`
	SharedKey            string              `json:"shared_key" dynamodbav:"shared_key"`
}

type OnlineCompeDefaultSetting struct {
	PartitionKey string `json:"partition_key" dynamodbav:"partition_key"`
	SortKey      string `json:"sort_key" dynamodbav:"sort_key"`
	Details      struct {
		CompeSetting DefaultCompeSetting `json:"compe_setting" dynamodbav:"compe_setting"` //コンペ設定
		OtherSetting DefaultOtherSetting `json:"other_setting" dynamodbav:"other_setting"` //その他設定
		Handicap     DefaultHandicap     `json:"handicap" dynamodbav:"handicap"`           //ハンディキャップSetting
	} `json:"details" dynamodbav:"details"`
	ExpirationTime int64     `json:"expiration_time" dynamodbav:"expiration_time"`
	UpdatedAt      time.Time `json:"updated_at" dynamodbav:"updated_at"`
}

type CompePlayer struct {
	PartitionKey   string             `json:"partition_key" dynamodbav:"partition_key"` // compe_${compe_number}
	SortKey        string             `json:"sort_key" dynamodbav:"sort_key"`           // online-compe_player_${offce_key}_${play_date}_${cart_no}_${player_no}
	Details        CompePlayerDetails `json:"details" dynamodbav:"details"`
	ExpirationTime int64              `json:"expiration_time" dynamodbav:"expiration_time"`
	UpdatedAt      time.Time          `json:"updated_at" dynamodbav:"updated_at"`
}

type CompePlayerDetails struct {
	CompeNo       int     `json:"compe_no" dynamodbav:"compe_no"`
	OfficeKey     string  `json:"office_key" dynamodbav:"office_key"`
	PlayerNo      int     `json:"player_no" dynamodbav:"player_no"`
	PlayerName    string  `json:"player_name" dynamodbav:"player_name"`
	Birthday      *string `json:"birthday" dynamodbav:"birthday"`
	Gender        *int    `json:"gender" dynamodbav:"gender"` //１：男性、２：女性
	GlidNo        string  `json:"glid_no" dynamodbav:"glid_no"`
	TeeId         *string `json:"tee_id" dynamodbav:"tee_id"`
	Hdcp          *string `json:"hdcp" dynamodbav:"hdcp"`
	HdcpIndex     *string `json:"hdcp_index" dynamodbav:"hdcp_index"`
	PlayingHdcp   *string `json:"playing_hdcp" dynamodbav:"playing_hdcp"` // by hdcp and course rating
	PlayDate      string  `json:"play_date" dynamodbav:"play_date"`
	CartNo        int     `json:"cart_no" dynamodbav:"cart_no"`
	CourseIndex   *int    `json:"course_index" dynamodbav:"course_index"`
	TeamClassType *int    `json:"team_class_type" dynamodbav:"team_class_type"`
	IsPaid        bool    `json:"is_paid" dynamodbav:"is_paid"`
}

type OldCompe struct {
	PartitionKey string `json:"partition_key" dynamodbav:"partition_key"`
	SortKey      string `json:"sort_key" dynamodbav:"sort_key"`
	Details      struct {
		AutoCloseTime         string `json:"auto_close_time" dynamodbav:"auto_close_time"`
		CompeName             string `json:"compe_name" dynamodbav:"compe_name"`
		CompeNo               string `json:"compe_no" dynamodbav:"compe_no"`
		CompeOrganizerAddress string `json:"compe_organizer_address" dynamodbav:"compe_organizer_address"`
		CompeOrganizerName    string `json:"compe_organizer_name" dynamodbav:"compe_organizer_name"`
		CompeTeeIdMen         string `json:"compe_tee_id_men" dynamodbav:"compe_tee_id_men"`
		CompeTeeIdWomen       string `json:"compe_tee_id_women" dynamodbav:"compe_tee_id_women"`
		CompeType             string `json:"compe_type" dynamodbav:"compe_type"`
		DecideScoreRange      string `json:"decide_score_range" dynamodbav:"decide_score_range"`
		EnableAttest          string `json:"enable_attest" dynamodbav:"enable_attest"`
		EnableCreateRanking   string `json:"enable_create_ranking" dynamodbav:"enable_create_ranking"`
		EnableMarkCommittee   string `json:"enable_mark_committee" dynamodbav:"enable_mark_committee"`
		FunctionSwitch        string `json:"function_switch" dynamodbav:"function_switch"`
		HdcpType              string `json:"hdcp_type" dynamodbav:"hdcp_type"`
		HiddenHole            string `json:"hidden_hole" dynamodbav:"hidden_hole"`
		HoleDispType          string `json:"hole_disp_type" dynamodbav:"hole_disp_type"`
		LeaderboardType       string `json:"leaderboard_type" dynamodbav:"leaderboard_type"`
		NetScoreCalcType      string `json:"net_score_calc_type" dynamodbav:"net_score_calc_type"`
		NetScoreFirstViewType string `json:"net_score_first_view_type" dynamodbav:"net_score_first_view_type"`
		PlayDate              string `json:"play_date" dynamodbav:"play_date"`
		PublicKey             string `json:"public_key" dynamodbav:"public_key"`
		StartPlayHoleNo       string `json:"start_play_hole_no" dynamodbav:"start_play_hole_no"`
	} `json:"details" dynamodbav:"details"`
	ExpirationTime int64     `json:"expiration_time" dynamodbav:"expiration_time"`
	UpdatedAt      time.Time `json:"updated_at" dynamodbav:"updated_at"`
}

type Basic struct {
	CompeNo   int    `json:"compe_no" dynamodbav:"compe_no" binding:"required,min=1"` //コンペNo
	CompeName string `json:"compe_name" dynamodbav:"compe_name" binding:"required" `  //コンペ名
	Duration  struct {
		From time.Time `json:"from"  binding:"required" dynamodbav:"from"`            //ISO 8601 or date type
		To   time.Time `json:"to" binding:"required,gtfield=From,gt" dynamodbav:"to"` // after from also should after now
	} `json:"duration"  dynamodbav:"duration" binding:"required"` // 開催日
	TargetOfficeType *int      `json:"target_office_type" dynamodbav:"target_office_type" binding:"required"` // 0 自コース 1 グループコース
	TargetOffice     *string   `json:"target_office" dynamodbav:"target_office"`                              // 自コースのオフィスKEY
	TargetOfficeList *[]string `json:"target_office_list" dynamodbav:"target_office_list"`                    //グループコース
	Organizer        string    `json:"organizer" dynamodbav:"organizer" binding:"required" `                  //主催者
	ParticipationFee *int      `json:"participation_fee" dynamodbav:"participation_fee" binding:"required"`   // 参加料金 円
	PromotionalImage *string   `json:"promotional_image" dynamodbav:"promotional_image" binding:"required"`   // 宣伝画像登録
	OldCompe         *struct {
		OldCompeNo        string `json:"old_compe_no" dynamodbav:"old_compe_no"`
		OldCompeOfficeKey string `json:"old_compe_office_key" dynamodbav:"old_compe_office_key"`
		OldCompeStartTime string `json:"old_compe_start_time" dynamodbav:"old_compe_start_time"`
	} `json:"old_compe" dynamodbav:"old_compe"`
}

type CompeSetting struct {
	EntryFromNavi      *int   `json:"entry_from_navi" binding:"required" dynamodbav:"entry_from_navi"`         //ナビからエントリー 0 許可しない 1許可する
	RankingAggregation *int   `json:"ranking_aggregation" binding:"required" dynamodbav:"ranking_aggregation"` //ランキング集計 0ホールを隠す 1最終3ホールを隠す 2最終6ホールを隠す 3最終9ホールを隠す
	Round              string `json:"round" binding:"required" dynamodbav:"round"`                             // 0.5ラウンド 1ラウンド 1.5ラウンド
	PrizeSetting       []struct {
		Type    string         `json:"type" binding:"required" dynamodbav:"type"`
		Setting map[string]int `json:"setting,omitempty" dynamodbav:"setting"`
		Name    string         `json:"name,omitempty" dynamodbav:"name"`
		Order   map[string]int `json:"order,omitempty" dynamodbav:"order"`
	} `json:"prize_setting" binding:"required" dynamodbav:"prize_setting"` //入賞設定
}

type CompeTypeSetting struct {
	Type  *int `json:"type" binding:"required" dynamodbav:"type"` // 0 team 1 個人戦
	Handy *struct {
		Distribution *int              `json:"distribution" binding:"required" dynamodbav:"distribution"`   // 0 配信しない 1 配信する
		RankingOrder map[string]string `json:"ranking_order" binding:"required" dynamodbav:"ranking_order"` //同点時の優先順位
		Handicap     struct {
			Type          *int   `json:"type" binding:"required" dynamodbav:"type"`                                   // 0 HDCP Index(WHS) 1 プライベートハンディキャップ
			HdcpDate      string `json:"hdcp_date" binding:"required" dynamodbav:"hdcp_date"`                         //時点のHDCPを使う
			HdcpAllowance int    `json:"hdcp_allowance" binding:"required,min=0,max=100" dynamodbav:"hdcp_allowance"` // HDCPの許容値 0-100 (%)
		} `json:"handicap" binding:"required" dynamodbav:"handicap"` //使用ハンディキャップ
		NetComputationType *int `json:"net_computation_type" binding:"required" dynamodbav:"net_computation_type"` //NET計算方法 0 HDCPナンバーで割り振り 1 按分方式
	} `json:"handy" dynamodbav:"handy"` //ハンディ optional item for 個人戦
	Peoria *struct {
		AggregationMethod struct {
			Type *int `json:"type" binding:"required" dynamodbav:"type"` //  0 ペリア(6H)、1 新ペリア(12H)、2 新新ペリア(9H)
		} `json:"aggregation_method" binding:"required" dynamodbav:"aggregation_method"` //集計方法
		ParLimit struct {
			Type *int `json:"type" binding:"required" dynamodbav:"type"` // 0 制限なし、1 PAR×2、2 PAR×2-1、3 PAR×3、4 PAR×3-1、　5  PAR+X、6 X、7 PAR×N+X　から選択。
			ParN *int `json:"par_n"  dynamodbav:"par_n"`                 //?
			ParX *int `json:"par_x" dynamodbav:"par_x"`                  //PAR×N+X
		} `json:"par_limit" binding:"required" dynamodbav:"par_limit"` //打数制限
		HandicapUpperLimit struct {
			Men   *int `json:"men"  dynamodbav:"men"`     // men hdcp max
			Women *int `json:"women"  dynamodbav:"women"` // women hdcp max
		} `json:"handicap_upper_limit" binding:"required" dynamodbav:"handicap_upper_limit"` //ハンデ上限設定
		Distribution *int              `json:"distribution" binding:"required" dynamodbav:"distribution"`   // 0 配信しない 1 配信する
		RankingOrder map[string]string `json:"ranking_order" binding:"required" dynamodbav:"ranking_order"` //同点時の優先順位
	} `json:"peoria" dynamodbav:"peoria"` //ぺリア optional item for 個人戦
}

type OtherSetting struct {
	MarkerSetting   *int `json:"marker_setting" binding:"required" dynamodbav:"marker_setting"` //  マーカー設定 0 しない 1 マーカーあり
	LeadboardChange struct {
		Type    *int    `json:"type" binding:"required" dynamodbav:"type"`       //0 しない 1 自由設定
		Default *string `json:"default" binding:"required" dynamodbav:"default"` //option : net, gross when 1  ; "" when  type is 0
	} `json:"leadboard_change" binding:"required" dynamodbav:"leadboard_change"` //リーダーボード切替
}

type PrivateSetting struct {
	CourseSetting map[string]string   `json:"course_setting" binding:"required" dynamodbav:"course_setting"` //コース設定
	HiddenHole    []HiddenHoleSetting `json:"hidden_hole" binding:"required" dynamodbav:"hidden_hole"`       //隠しホール設定 list
}

type HiddenHoleSetting struct {
	CourseName      string `json:"course_name" binding:"required" dynamodbav:"course_name"`             //コース名
	CourseIndex     string `json:"course_index" binding:"required" dynamodbav:"course_index"`           //コースインデックス
	HiddenHoleIndex []int  `json:"hidden_hole_index" binding:"required" dynamodbav:"hidden_hole_index"` //隠しホールインデックス
}

type DefaultCompeSetting struct {
	EntryFromNavi      int    `json:"entry_from_navi" dynamodbav:"entry_from_navi"`         // ナビからエントリー 0 許可しない 1許可する
	RankingAggregation int    `json:"ranking_aggregation" dynamodbav:"ranking_aggregation"` //ランキング集計 0ホールを隠す 1最終3ホールを隠す 2最終6ホールを隠す 3最終9ホールを隠す
	Round              string `json:"round" dynamodbav:"round"`
}

type DefaultOtherSetting struct {
	MarkerSetting   int `json:"marker_setting" dynamodbav:"marker_setting"` //マーカー設定 0 しない 1 マーカーあり
	LeadboardChange struct {
		Type    int    `json:"type" dynamodbav:"type"`       //0 しない 1 自由設定
		Default string `json:"default" dynamodbav:"default"` //?
	} `json:"leadboard_change" dynamodbav:"leadboard_change"` //リーダーボード切替
}

type DefaultHandicap struct {
	Type int `json:"type" dynamodbav:"type"` //ハンディキャップSetting 0 HDCP Index(WHS) 1 フロント連携HDCP
}

type OnlineCompeIndex struct {
	ID               int       `gorm:"primaryKey;autoIncrement;column:id" json:"id"`
	OfficeKey        string    `gorm:"column:office_key;type:varchar(64);not null;index" json:"office_key"`
	CompeID          int       `gorm:"column:compe_id;not null;index" json:"compe_id"`
	CompeName        string    `gorm:"column:compe_name;type:varchar(512);not null;" json:"compe_name"`               // コンペ名
	CompeType        int       `gorm:"column:compe_type;not null;index" json:"compe_type"`                            // 0 team 1 個人戦
	AggregationTypes string    `gorm:"column:aggregation_types;type:varchar(512);not null;" json:"aggregation_types"` // like 0,1,2,handy : 0 ペリア(6H)、1 新ペリア(12H)、2 新新ペリア(9H)  handy ハンディ
	TimeZone         string    `gorm:"column:time_zone;type:varchar(32);not null;" json:"time_zone"`                  // time_zone
	StartedAt        time.Time `gorm:"column:started_at;not null;index" json:"started_at"`                            // from
	EndedAt          time.Time `gorm:"column:ended_at;not null;index" json:"ended_at"`                                // to
	CreatedAt        time.Time `gorm:"column:created_at;autoCreateTime:microseconds" json:"created_at"`               // 作り時間
}

func (OnlineCompeIndex) TableName() string {
	return "online_compe_indices"
}
