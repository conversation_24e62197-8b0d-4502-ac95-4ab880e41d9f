package compe

import "time"

type Ranking struct {
	PartitionKey   string         `json:"partition_key" dynamodbav:"partition_key"`
	Sort<PERSON>ey        string         `json:"sort_key" dynamodbav:"sort_key"` // online-compe_{compe_no}_{office_key}_{play_date}_{ranking_type}
	ExpirationTime int64          `json:"expiration_time" dynamodbav:"expiration_time"`
	UpdatedAt      time.Time      `json:"updated_at" dynamodbav:"updated_at"`
	Details        RankingDetails `json:"details" dynamodbav:"details"`
}

type RankingDetails struct {
	PlayDate     string           `json:"play_date" dynamodbav:"play_date"`
	CompeNo      string           `json:"compe_no" dynamodbav:"compe_no"`
	Courses      []ScoreCourses   `json:"courses" dynamodbav:"courses"`
	Rankings     []PlayerRankings `json:"rankings" dynamodbav:"rankings"`
	BreakingNews []BreakingNews   `json:"breaking_news" dynamodbav:"breaking_news"`
}

type ScoreCourses struct {
	CourseIndex string  `json:"course_index" dynamodbav:"course_index"`
	CourseName  string  `json:"course_name" dynamodbav:"course_name"`
	StartHole   string  `json:"start_hole" dynamodbav:"start_hole"`
	Holes       []Holes `json:"holes" dynamodbav:"holes"`
}

type Holes struct {
	HoleIndex string `json:"hole_index" dynamodbav:"hole_index"`
	UsedPar   string `json:"used_par" dynamodbav:"used_par"`
	UsedHdcp  string `json:"used_hdcp" dynamodbav:"used_hdcp"`
}

type BreakingNews struct {
	PlayerNo string `json:"player_no" dynamodbav:"player_no"`
	Score    string `json:"score" dynamodbav:"score"`
}

type PlayerRankings struct {
	Position       string             `json:"pos" dynamodbav:"position"`
	PositionNet    string             `json:"pos_net" dynamodbav:"position_net"`
	OrderNet       string             `json:"order_net" dynamodbav:"order_net"`
	IsTied         int                `json:"is_tied" dynamodbav:"is_tied"`
	PlayerNo       string             `json:"player_no" dynamodbav:"player_no"`
	PlayerName     string             `json:"player_name" dynamodbav:"player_name"`
	Hole           string             `json:"hole" dynamodbav:"hole"`
	HoleNumber     string             `json:"hole_number" dynamodbav:"hole_number"`
	ParGross       int                `json:"par_gross" dynamodbav:"par_gross"`
	ParNet         float64            `json:"par_net" dynamodbav:"par_net"`
	ScoreGross     int                `json:"score_gross" dynamodbav:"score_gross"`
	ScoreNet       float64            `json:"score_net" dynamodbav:"score_net"`
	HdcpIndex      string             `json:"hdcp_index" dynamodbav:"hdcp_index"`
	CourseHdcp     string             `json:"course_hdcp" dynamodbav:"course_hdcp"`
	InputHoleCount int                `json:"input_hole_count" dynamodbav:"input_hole_count"`
	CourseIndex    string             `json:"course_index" dynamodbav:"course_index"`
	HoleIndex      string             `json:"hole_index" dynamodbav:"hole_index"`
	HoleScore      []RankingHoleScore `json:"hole_score" dynamodbav:"hole_score"`
}

type RankingHoleScore struct {
	HoleNumber  string `json:"hole_number" dynamodbav:"hole_number"`
	CourseIndex string `json:"course_index" dynamodbav:"course_index"`
	HoleIndex   string `json:"hole_index" dynamodbav:"hole_index"`
	Score       string `json:"score" dynamodbav:"score"`
	Stroke      string `json:"stroke" dynamodbav:"stroke"`

	HoleHdcp int `json:"hole_hdcp" dynamodbav:"hole_hdcp"`
}
