package compe

type Player struct {
	PlayerNo   int    `json:"player_no"`
	<PERSON>N<PERSON> string `json:"player_name"`
	HdcpIndex  string `json:"hdcp_index"`
	Hdcp       string `json:"hdcp"`
	Score      int    `json:"score"`
	Total      int    `json:"total"`
}

type Pair struct {
	CartNo    int      `json:"cart_no"`
	StartTime string   `json:"start_time"`
	Player    []Player `json:"player"`
}

type Pairing struct {
	CourseName string          `json:"course_name"`
	Pair       map[string]Pair `json:"pair"`
}
