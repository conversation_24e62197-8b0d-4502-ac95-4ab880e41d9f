package model

// システム設定表的结构体
type OfficeSettings struct {
	ID                     int       `gorm:"primaryKey;autoIncrement;column:id" json:"id"`                                        // 主キーID
	OfficeID                string    `gorm:"column:office_id;type:varchar(64)" json:"office_id"`                                    // office_id
	EnableSelfScorePrint   *int      `gorm:"column:enable_self_score_print;default:0;not null;" json:"enable_self_score_print"`   // セルフスコア印刷機能　0:無効　1:有効
	EnableQuestionnaire    *int      `gorm:"column:enable_questionnaire;default:0;not null;" json:"enable_questionnaire"`         // アンケート機能　0:無効　1:有効
	EnableStartGuide       *int      `gorm:"column:enable_start_guide;default:0;not null;" json:"enable_start_guide"`             // スタート案内機能　0:無効　1:有効
	CardReaderType         *int      `gorm:"column:card_reader_type;default:0;not null;" json:"card_reader_type"`                 // カードリード種類　0:なし　1:バーコードリード　2:ICカードリード　
}

// TableName 設置 GORM テーブル名前
func (OfficeSettings) TableName() string {
	return "office_settings"
}
