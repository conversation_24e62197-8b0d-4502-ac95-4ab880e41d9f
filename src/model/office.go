package model

import "time"

type Office struct {
	OfficeId         int       `gorm:"column:office_id"`
	OfficeKey        string    `gorm:"column:office_key"`
	OfficeName       string    `gorm:"column:office_name"`
	ProductTypeId    int       `gorm:"column:product_type_id"`
	ProductSubTypeId int       `gorm:"column:product_subtype_id"`
	DeletedAt        time.Time `gorm:"column:deleted_at"`
}

func (*Office) TableName() string {
	return "office"
}
