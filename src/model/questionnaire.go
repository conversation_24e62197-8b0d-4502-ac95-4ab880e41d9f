package model

import (
	"gorm.io/gorm"
	"time"
)

// Questionnaire 表示评价表的结构体
type Questionnaire struct {
	ID          int            `gorm:"primaryKey;autoIncrement;column:id" json:"id"`                    // 主キーID
	Serial      string         `gorm:"column:serial;type:varchar(64)" json:"serial"`                    // シリアル
	CartNo      string         `gorm:"column:cart_no;type:varchar(64)" json:"cart_no"`                  // カート番号
	CaddyID     string         `gorm:"column:caddy_id;type:varchar(64)" json:"caddy_id"`                // キャディID
	CreatedAt   time.Time      `gorm:"column:created_at;autoCreateTime:microseconds" json:"created_at"` // 評価作り時間
	Weekday     int            `gorm:"column:weekday" json:"weekday"`                                   // weekday
	DeletedAt   gorm.DeletedAt `gorm:"column:deleted_at" json:"deleted_at"`                             // delete time
	OfficeID    string         `gorm:"column:office_id;type:varchar(64)" json:"office_id"`              // office_id
	PlayedDate  string         `gorm:"column:played_date;type:varchar(30)" json:"played_date"`
	StartTime   string         `gorm:"column:start_time;type:varchar(30)" json:"start_time"`
	StartCourse string         `gorm:"column:start_course;type:varchar(191)" json:"start_course"`
	Players     []Player       `gorm:"foreignKey:QuestionnaireId;references:ID"`
}

// TableName 設置 GORM テーブル名前
func (Questionnaire) TableName() string {
	return "questionnaire"
}
