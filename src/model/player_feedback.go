package model

import (
	"gorm.io/gorm"
	"time"
)

// PlayerFeedback player feedback
type PlayerFeedback struct {
	ID             int            `gorm:"primaryKey;autoIncrement;column:id" json:"id"`
	PlayerID       int            `gorm:"column:player_id;not null;index"`                                   // player table`s id
	Content        string         `gorm:"column:content;type:text"`                                          // feedback text
	ContentCaddy   string         `gorm:"column:content_caddy;type:text"`                                    // キャディのfeedback text
	ContentGolf    string         `gorm:"column:content_golf;type:text"`                                     // ゴルフ場のfeedback text
	CreatedAt      time.Time      `gorm:"column:created_at;autoCreateTime:microseconds" json:"created_at"`   // 評価作り時間
	DeletedAt      gorm.DeletedAt `gorm:"column:deleted_at" json:"deleted_at"`                               // 削除時間
	OfficeID       string         `gorm:"column:office_id;type:varchar(64);not null;index" json:"office_id"` // コース番号
}

func (PlayerFeedback) TableName() string {
	return "player_feedback"
}
