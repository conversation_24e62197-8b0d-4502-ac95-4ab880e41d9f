package model

type MarshaliDynamoDb struct {
	PartitionKey   string      `dynamodbav:"partition_key"`
	Sort<PERSON>ey        string      `dynamodbav:"sort_key"`
	ExpirationTime int64       `dynamodbav:"expiration_time"`
	CartNo         string      `dynamodbav:"cart_no"`
	UpdatedAt      string      `dynamodbav:"updated_at"`
	Header         interface{} `dynamodbav:"header"`
	Version        interface{} `dynamodbav:"version"`
	Sender         interface{} `dynamodbav:"sender"`
	Receiver       interface{} `dynamodbav:"receiver"`
	Common         interface{} `dynamodbav:"common"`
	Details        interface{} `dynamodbav:"details"`
}

type Hole struct {
	HoleIndex string `json:"hole_index"`
	UsedHdcp  string `json:"used_hdcp"`
	UsedPar   string `json:"used_par"`
}

type Course struct {
	CourseIndex string `json:"course_index"`
	CourseName  string `json:"course_name"`
	Holes       []Hole `json:"holes"`
	StartHole   string `json:"start_hole"`
}

type HoleScore struct {
	HoleIndex string `json:"hole_index"`
	Putt      string `json:"putt"`
	Score     string `json:"score"`
}

type CourseScore struct {
	PlayerNo          string      `json:"player_no"`
	FixedScoreRequest string      `json:"fixed_score_request"`
	HoleScore         []HoleScore `json:"hole_score"`
	IsFixed           string      `json:"is_fixed"`
}

type PlayerScore struct {
	CourseScore []CourseScore `json:"course_score"`
	PlayerName  string        `json:"player_name"`
	PlayerNo    string        `json:"player_no"`
}

type ScoreDetail struct {
	CartNo                    string        `json:"cart_no"`
	CompeNo                   string        `json:"compe_no"`
	CompeName                 string        `json:"compe_name"`
	CaddieNo                  string        `json:"caddie_no"`
	CaddieName                string        `json:"caddie_name"`
	ScheduledStartCourseIndex string        `json:"scheduled_start_course_index"`
	ScheduledStartTime        string        `json:"scheduled_start_time"`
	Courses                   []Course      `json:"courses"`
	PlayerScore               []PlayerScore `json:"player_score"`
	UpdatedAt                 string        `json:"updated_at"`
	SortKey                   string        `json:"sort_key"`
}

type ScoreHeader struct {
	CartNo     string `json:"cart_no"`
	OfficeKey  string `json:"office_key"`
	OfficeName string `json:"office_name"`
	PlayDate   string `json:"play_date"`
}

type OperationInfoDetail struct {
	CollectionTime    string         `json:"collection_time"`
	EnableDelayTime   string         `json:"enable_delay_time"`
	ImageSourceHeight string         `json:"image_source_height"`
	ImageSourceWidth  string         `json:"image_source_width"`
	SnapshotData      []SnapshotData `json:"snapshot_data"`
}

type SnapshotData struct {
	ArrivalTime string       `json:"arrival_time"`
	CaddieNo    string       `json:"caddie_no"`
	CaddieName  string       `json:"caddie_name"`
	CartNo      string       `json:"cart_no"`
	CartType    string       `json:"cart_type"`
	CompeName   string       `json:"compe_name"`
	CompeNo     string       `json:"compe_no"`
	CourseIndex int          `json:"course_index"`
	DelayTime   string       `json:"delay_time"`
	StartTime   string       `json:"start_time"`
	Players     []SnapPlayer `json:"players"`
}

type SnapPlayer struct {
	PlayerName string `json:"player_name"`
	PlayerNo   string `json:"player_no"`
}

type CourseSettingDetail struct {
	CourseIndex int    `json:"course_index"`
	CourseName  string `json:"course_name"`
	OfficeId    int    `json:"office_id"`
}
