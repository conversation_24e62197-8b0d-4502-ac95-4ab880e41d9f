package model

import "time"

type PlayHistory struct {
	OfficeId        int       `gorm:"column:office_id"`
	CartNo          int       `gorm:"column:cart_no"`
	StartTime       time.Time `gorm:"column:start_time"`
	PlayHistoryDate time.Time `gorm:"column:play_history_date"`
	ArrivalTime     time.Time `gorm:"column:arrival_time"`
	CourseIndex     int       `gorm:"column:course_index"`
	IsSelf          int       `gorm:"column:is_self"` // 0 has caddy; 1 self
	CreatedAt       time.Time `gorm:"column:created_at"`
}

func (*PlayHistory) TableName() string {
	return "play_history"
}
