APP_PORT=888
APP_LOG_LEVEL=info
# finger out uri prefix
API_PREFIX=/api/v2
# local lambda
DEPLOY_MODE=server

# operate audit config
AUDIT_ON=on
AUDIT_SYNC_ON=on
DYNAMODB_AUDIT_TABLE_NAME=operate_audit

MYSQL_CONN=root:PSword!123@tcp(mysql:3309)/golf_teamdb
MYSQL_MNCDB_CONN=root:PSword!123@tcp(mysql:3309)/mncdb

ACCESS_CONTROL_ALLOW_DOMAIN=localhost

# app token redis
APP_REDIS_HOST=redis
APP_REDIS_PASSWORD=
APP_REDIS_PORT=6379
APP_REDIS_POOL_SIZE=1
APP_REDIS_DB=0

# aws config
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_SESSION_TOKEN=