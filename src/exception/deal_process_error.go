package exception

import "net/http"

type AppError struct {
	Status  int    // HTTP status code
	Message string // err msg
}

func NewError(code int, msg string) *AppError {
	return &AppError{
		Status:  code,
		Message: msg,
	}
}

func StatusCode(err error) int {
	if err == nil {
		return http.StatusOK
	}

	if myErr, ok := err.(*AppError); ok {
		return myErr.StatusCode()
	} else {
		return http.StatusInternalServerError
	}
}

func (e *AppError) Error() string {
	return e.Message
}

func (e *AppError) StatusCode() int {
	return e.Status
}
