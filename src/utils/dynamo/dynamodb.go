package dynamo

import (
	"context"
	"fmt"
	"mi-restful-api/client"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
)

type Response struct {
	Items []map[string]types.AttributeValue `json:"items"`
}

func Execute(sql string) (Response, error) {
	svc := client.GetDynamodb()

	input := &dynamodb.ExecuteStatementInput{
		Statement: aws.String(sql),
	}

	result, err := svc.ExecuteStatement(context.Background(), input)
	if err != nil {
		return Response{}, fmt.Errorf("failed to execute statement, %v", err)
	}

	// 返回查询结果
	return Response{Items: result.Items}, nil
}

// PutItem insert into
func PutItem(tableName string, item map[string]types.AttributeValue) (*dynamodb.PutItemOutput, error) {
	svc := client.GetDynamodb()

	// 构造插入请求
	input := &dynamodb.PutItemInput{
		Item:      item,
		TableName: aws.String(tableName),
	}
	fmt.Printf("table name :%s \r\n", tableName)

	// 执行插入操作
	return svc.PutItem(context.Background(), input)
}

// GetItem get one record
func GetItem(tableName string, item map[string]types.AttributeValue) (*dynamodb.GetItemOutput, error) {
	svc := client.GetDynamodb()

	// 构造插入请求
	input := &dynamodb.GetItemInput{
		TableName: aws.String(tableName),
		Key:       item,
	}

	// 执行读取操作
	return svc.GetItem(context.Background(), input)
}

func BatchGetItem(input *dynamodb.BatchGetItemInput) (*dynamodb.BatchGetItemOutput, error) {
	svc := client.GetDynamodb()

	// 执行插入操作
	return svc.BatchGetItem(context.Background(), input)
}

func Query(query *dynamodb.QueryInput) (*dynamodb.QueryOutput, error) {
	svc := client.GetDynamodb()

	// 执行插入操作
	return svc.Query(context.Background(), query)
}

func QueryItem(ctx context.Context, tableName string, keyConditionExpression string, expressionAttributeValues map[string]types.AttributeValue, scanIndexForward bool) (*dynamodb.QueryOutput, error) {
	svc := client.GetDynamodb()
	input := &dynamodb.QueryInput{
		TableName:                 aws.String(tableName),
		KeyConditionExpression:    aws.String(keyConditionExpression),
		ExpressionAttributeValues: expressionAttributeValues,
		ScanIndexForward:          aws.Bool(scanIndexForward),
	}
	return svc.Query(ctx, input)
}

func UpdateItem(input *dynamodb.UpdateItemInput) (*dynamodb.UpdateItemOutput, error) {
	svc := client.GetDynamodb()

	// 执行 UpdateItem 请求
	return svc.UpdateItem(context.Background(), input)
}

func DeleteItem(input *dynamodb.DeleteItemInput) (*dynamodb.DeleteItemOutput, error) {
	svc := client.GetDynamodb()

	// 执行 UpdateItem 请求
	return svc.DeleteItem(context.Background(), input)
}
