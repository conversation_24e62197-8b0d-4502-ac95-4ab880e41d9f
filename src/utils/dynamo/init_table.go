package dynamo

import (
	"context"
	"log"
	"log/slog"
	"mi-restful-api/client"
	"mi-restful-api/configs"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
)

func CreateScore() {
	svc := client.GetDynamodb()
	// 定义表的结构
	input := &dynamodb.CreateTableInput{
		TableName: aws.String("score"),
		KeySchema: []types.KeySchemaElement{
			{
				AttributeName: aws.String("office_id"),
				KeyType:       types.KeyTypeHash, // 分区键
			},
			{
				AttributeName: aws.String("sort_key"),
				KeyType:       types.KeyTypeRange, // 排序键
			},
		},
		AttributeDefinitions: []types.AttributeDefinition{
			{
				AttributeName: aws.String("office_id"),
				AttributeType: types.ScalarAttributeTypeS, // 字符串类型
			},
			{
				AttributeName: aws.String("sort_key"),
				AttributeType: types.ScalarAttributeTypeS, // 字符串类型
			},
		},
		ProvisionedThroughput: &types.ProvisionedThroughput{
			ReadCapacityUnits:  aws.Int64(5),
			WriteCapacityUnits: aws.Int64(5),
		},
	}

	// 创建表
	_, err := svc.CreateTable(context.Background(), input)
	if err != nil {
		slog.Error("Error creating table:", "err", err.Error())
		return
	}

	slog.Info("Table created successfully")
}

func CreateQuestion() {
	svc := client.GetDynamodb()
	// 定义表的结构
	input := &dynamodb.CreateTableInput{
		TableName: aws.String("question"),
		KeySchema: []types.KeySchemaElement{
			{
				AttributeName: aws.String("office_id"),
				KeyType:       types.KeyTypeHash, // 分区键
			},
			{
				AttributeName: aws.String("sort_key"),
				KeyType:       types.KeyTypeRange, // 排序键
			},
		},
		AttributeDefinitions: []types.AttributeDefinition{
			{
				AttributeName: aws.String("office_id"),
				AttributeType: types.ScalarAttributeTypeS, // 字符串类型
			},
			{
				AttributeName: aws.String("sort_key"),
				AttributeType: types.ScalarAttributeTypeN, // 数字类型，根据实际需求修改
			},
		},
		ProvisionedThroughput: &types.ProvisionedThroughput{
			ReadCapacityUnits:  aws.Int64(5),
			WriteCapacityUnits: aws.Int64(5),
		},
	}

	// 创建表
	_, err := svc.CreateTable(context.Background(), input)
	if err != nil {
		slog.Error("Error creating table:", "err", err.Error())
		return
	}

	slog.Info("Table created successfully")
}

func CreateComment() {
	svc := client.GetDynamodb()
	// 定义表的结构
	input := &dynamodb.CreateTableInput{
		TableName: aws.String("comment"),
		KeySchema: []types.KeySchemaElement{
			{
				AttributeName: aws.String("office_id"),
				KeyType:       types.KeyTypeHash, // 分区键
			},
			{
				AttributeName: aws.String("level"),
				KeyType:       types.KeyTypeRange, // 排序键
			},
		},
		AttributeDefinitions: []types.AttributeDefinition{
			{
				AttributeName: aws.String("office_id"),
				AttributeType: types.ScalarAttributeTypeS, // level describe
			},
			{
				AttributeName: aws.String("level"),
				AttributeType: types.ScalarAttributeTypeN, // number
			},
		},
		ProvisionedThroughput: &types.ProvisionedThroughput{
			ReadCapacityUnits:  aws.Int64(5),
			WriteCapacityUnits: aws.Int64(5),
		},
	}

	// 创建表
	_, err := svc.CreateTable(context.Background(), input)
	if err != nil {
		slog.Info("Error creating table:", "err", err.Error())
		return
	}

	slog.Info("Table created successfully")
}

func CreateGSI() {
	svc := client.GetDynamodb()
	// 定义表的结构
	input := &dynamodb.CreateTableInput{
		TableName: aws.String("Users"),
		KeySchema: []types.KeySchemaElement{
			{
				AttributeName: aws.String("office"),
				KeyType:       types.KeyTypeHash, // 分区键
			},
		},
		AttributeDefinitions: []types.AttributeDefinition{
			{
				AttributeName: aws.String("UserID"),
				AttributeType: types.ScalarAttributeTypeS, // 字符串类型
			},
			{
				AttributeName: aws.String("Email"),
				AttributeType: types.ScalarAttributeTypeS, // 字符串类型
			},
		},
		ProvisionedThroughput: &types.ProvisionedThroughput{
			ReadCapacityUnits:  aws.Int64(5),
			WriteCapacityUnits: aws.Int64(5),
		},
		GlobalSecondaryIndexes: []types.GlobalSecondaryIndex{
			{
				IndexName: aws.String("EmailIndex"),
				KeySchema: []types.KeySchemaElement{
					{
						AttributeName: aws.String("Email"),
						KeyType:       types.KeyTypeHash, // GSI 的分区键
					},
				},
				Projection: &types.Projection{
					ProjectionType: types.ProjectionTypeAll, // 投影所有属性
				},
				ProvisionedThroughput: &types.ProvisionedThroughput{
					ReadCapacityUnits:  aws.Int64(5),
					WriteCapacityUnits: aws.Int64(5),
				},
			},
		},
	}

	// 创建表
	_, err := svc.CreateTable(context.TODO(), input)
	if err != nil {
		log.Fatalf("failed to create table, %v", err)
	}

	slog.Info("Table created successfully")
}

func CreateAuditTable() {
	svc := client.GetDynamodb()
	slog.Info("init dynamodb table name :" + configs.GetOperateAuditConfig().DynamoAuditTableName)
	// 定义表的结构
	input := &dynamodb.CreateTableInput{
		TableName: aws.String(configs.GetOperateAuditConfig().DynamoAuditTableName),
		KeySchema: []types.KeySchemaElement{
			{
				AttributeName: aws.String("office_id"),
				KeyType:       types.KeyTypeHash, // 分区键
			},
			{
				AttributeName: aws.String("sort_key"),
				KeyType:       types.KeyTypeRange, // 排序键
			},
		},
		AttributeDefinitions: []types.AttributeDefinition{
			{
				AttributeName: aws.String("office_id"),
				AttributeType: types.ScalarAttributeTypeS, // 字符串类型
			},
			{
				AttributeName: aws.String("sort_key"),
				AttributeType: types.ScalarAttributeTypeS, // 字符串类型
			},
		},
		ProvisionedThroughput: &types.ProvisionedThroughput{
			ReadCapacityUnits:  aws.Int64(5),
			WriteCapacityUnits: aws.Int64(5),
		},
	}

	// 创建表
	_, err := svc.CreateTable(context.Background(), input)
	if err != nil {
		slog.Error("Error creating table:", "err", err.Error())
		return
	}

	slog.Info("Table created successfully")
}

func CreateCompeTable() {
	svc := client.GetDynamodb()
	compeTableConfig := configs.GetDynamodbCompeConfig()
	slog.Info("init dynamodb table name :" + compeTableConfig.CompeTableName)
	input := &dynamodb.CreateTableInput{
		TableName: aws.String(compeTableConfig.CompeTableName),
		KeySchema: []types.KeySchemaElement{
			{
				AttributeName: aws.String("partition_key"),
				KeyType:       types.KeyTypeHash,
			},
			{
				AttributeName: aws.String("sort_key"),
				KeyType:       types.KeyTypeRange,
			},
		},
		AttributeDefinitions: []types.AttributeDefinition{
			{
				AttributeName: aws.String("partition_key"),
				AttributeType: types.ScalarAttributeTypeS,
			},
			{
				AttributeName: aws.String("sort_key"),
				AttributeType: types.ScalarAttributeTypeS,
			},
		},
		ProvisionedThroughput: &types.ProvisionedThroughput{
			ReadCapacityUnits:  aws.Int64(5),
			WriteCapacityUnits: aws.Int64(5),
		},
	}

	_, err := svc.CreateTable(context.Background(), input)
	if err != nil {
		slog.Error("Error creating compe table:", "err", err.Error())
		return
	}

	slog.Info("Table compe created successfully")
}
