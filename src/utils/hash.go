package utils

import (
	"fmt"
	"hash/crc32"
	"log/slog"
	"math"
	"mi-restful-api/model"
	"strconv"
	"strings"
	"time"
)

func ScoreHash(playDate time.Time, officeKey string, cartNo int, compeNo int, playScore []model.PlayerScore) string {
	origHashSource := playDate.Format("20060102") + "-" + officeKey + "-" + strconv.Itoa(cartNo) + "-" + strconv.Itoa(compeNo)
	for _, score := range playScore {
		playerNo, _ := strconv.Atoi(score.PlayerNo)
		if playerNo != 0 {
			// 空白（全角、半角）を除去する。
			name := strings.Replace(score.PlayerName, "　", "", -1)
			name = strings.Replace(name, " ", "", -1)
			origHashSource += "-" + name
		}
	}
	checksum := crc32.ChecksumIEEE([]byte(origHashSource))
	return fmt.Sprintf("%x", checksum)
}

// from hdcp index \text{Playing Handicap} = \text{Handicap Index} \times \frac{\text{Slope Rating}}{113} + (\text{Course Rating} - \text{Par}) * hdcpAllowance/100
func ComputeHdcp(hdcpIndex string, slopeRating string, courseRating string, par string, hdcpAllowance int) string {
	slog.Debug("ComputeHdcp input", "hdcpIndex", hdcpIndex, "slopeRating", slopeRating, "courseRating", courseRating, "par", par, "hdcpAllowance", hdcpAllowance)
	hdcpIndexFloat, _ := strconv.ParseFloat(hdcpIndex, 64)
	slopeRatingFloat, _ := strconv.ParseFloat(slopeRating, 64)
	courseRatingFloat, _ := strconv.ParseFloat(courseRating, 64)
	hdcpAllowanceFloat := float64(hdcpAllowance) / 100
	parFloat, _ := strconv.ParseFloat(par, 64)

	playingHandicap := hdcpAllowanceFloat * (hdcpIndexFloat*slopeRatingFloat/113 + (courseRatingFloat - parFloat))
	// to int with Round and return int
	return strconv.Itoa(int(math.Round(playingHandicap)))
}

// sperate 544345434543445344 one by one and sum the number
func ComputePar(parStr string) string {
	if parStr == "" {
		return "72"
	}
	par := 0
	for _, c := range parStr {
		par += int(c - '0')
	}

	return strconv.Itoa(par)
}
