package utils

import (
	"context"
	"crypto/sha1"
	"encoding/json"
	"fmt"
	"log/slog"
	"mi-restful-api/client"
	"mi-restful-api/model"
	"os"
	"strconv"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

func GenerateAuthToken(sessionId string, sessionData model.SessionData, userAgent string, timestamps int64) (
	string, error) {

	slog.Info("GenerateAuthToken params:", "sessionId", sessionId, "userAgent", userAgent, "timestamps", timestamps)

	ctx := context.Background()
	redis := client.GetRedisClient()

	secretKey := os.Getenv("SECRET_KEY")
	claims := &jwt.MapClaims{
		"key":        sha1.Sum([]byte(sessionId)),
		"userAgent":  userAgent,
		"timestamps": time.Now().Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// Generate encoded token and handle error
	tokenString, err := token.SignedString([]byte(secretKey))
	if err != nil {
		return "", err
	}

	// for php logout use
	sessionData.SessionId = sessionId
	sessionJson, err := json.Marshal(sessionData)
	if err != nil {
		return "", err
	}

	slog.Info("GenerateAuthToken session", "raw", string(sessionJson))

	if _, err := redis.SetEx(ctx, "authToken:"+tokenString, sessionJson, time.Second*time.Duration(60)).Result(); err != nil {
		return "", err
	}

	return tokenString, nil
}

func GenerateAccessToken(authToken string) (string, string, *model.SessionData, *model.Office, error) {
	secretKey := os.Getenv("SECRET_KEY")
	accessTokenTTL, err := strconv.Atoi(os.Getenv("ACCESS_TOKEN_TTL"))
	if err != nil {
		return "", "", nil, nil, err
	}
	refreshTokenTTL, err := strconv.Atoi(os.Getenv("REFRESH_TOKEN_TTL"))
	if err != nil {
		return "", "", nil, nil, err
	}

	ctx := context.Background()
	redis := client.GetRedisClient()
	sessionJson, err := redis.GetDel(ctx, "authToken:"+authToken).Result()
	if err != nil {
		return "", "", nil, nil, fmt.Errorf("%s %v", "Invalid authToken:", err)
	}

	slog.Info("GenerateAccessToken session:", "raw", sessionJson)

	var sessionData model.SessionData
	if err := json.Unmarshal([]byte(sessionJson), &sessionData); err != nil {
		return "", "", nil, nil, fmt.Errorf("%s %v", "Unmarshal sessionData failed:", err)
	}

	db, err := client.GetMncDBClient()
	if err != nil {
		slog.Error("GenerateAccessToken get db client error:", "err", err)
		return "", "", nil, nil, fmt.Errorf("%s %v", "get db client error:", err)
	}

	var office model.Office
	err = db.Model(&model.Office{}).Where("office_id=? and deleted_at is null", sessionData.OfficeId).First(&office).Error
	if err != nil {
		slog.Error("GenerateAccessToken get office error:", "err", err)
		return "", "", nil, nil, fmt.Errorf("%s %v", "get office error:", err)
	}

	accessTokenString, err := jwt.NewWithClaims(jwt.SigningMethodHS256, &jwt.MapClaims{
		"userId":    sessionData.UserId,
		"officeId":  sessionData.OfficeId,
		"officeKey": office.OfficeKey,
		"exp":       time.Now().Add(time.Second * time.Duration(accessTokenTTL)).Unix(),
	},
	).SignedString([]byte(secretKey))

	if err != nil {
		return "", "", nil, nil, err
	}

	refreshTokenString, err := jwt.NewWithClaims(jwt.SigningMethodHS256, &jwt.MapClaims{
		"userId": sessionData.UserId,
		"exp":    time.Now().Add(time.Second * time.Duration(refreshTokenTTL)).Unix(),
	},
	).SignedString([]byte(secretKey))

	if err != nil {
		return "", "", nil, nil, err
	}

	if _, err := redis.SetEx(ctx, "accessToken:"+accessTokenString, sessionJson, time.Second*time.Duration(accessTokenTTL)).Result(); err != nil {
		return "", "", nil, nil, err
	}
	if _, err := redis.SetEx(ctx, "refreshToken:"+refreshTokenString, sessionJson, time.Second*time.Duration(refreshTokenTTL)).Result(); err != nil {
		return "", "", nil, nil, err
	}

	return accessTokenString, refreshTokenString, &sessionData, &office, nil
}

func ExchangeToken(refreshToken string) (string, string, error) {
	secretKey := os.Getenv("SECRET_KEY")
	accessTokenTTL, err := strconv.Atoi(os.Getenv("ACCESS_TOKEN_TTL"))
	if err != nil {
		return "", "", err
	}
	refreshTokenTTL, err := strconv.Atoi(os.Getenv("REFRESH_TOKEN_TTL"))
	if err != nil {
		return "", "", err
	}

	ctx := context.Background()
	redis := client.GetRedisClient()
	sessionJson, err := redis.GetDel(ctx, "refreshToken:"+refreshToken).Result()
	if err != nil {
		return "", "", err
	}

	var sessionData model.SessionData
	if err := json.Unmarshal([]byte(sessionJson), &sessionData); err != nil {
		return "", "", fmt.Errorf("%s %v", "Unmarshal sessionData failed:", err)
	}

	db, err := client.GetMncDBClient()
	if err != nil {
		slog.Error("GenerateAccessToken get db client error:", "err", err)
		return "", "", fmt.Errorf("%s %v", "get db client error:", err)
	}

	var office model.Office
	err = db.Model(&model.Office{}).Where("office_id=? and deleted_at is null", sessionData.OfficeId).First(&office).Error
	if err != nil {
		slog.Error("GenerateAccessToken get office error:", "err", err)
		return "", "", fmt.Errorf("%s %v", "get office error:", err)
	}

	accessTokenString, err := jwt.NewWithClaims(jwt.SigningMethodHS256, &jwt.MapClaims{
		"userId":    sessionData.UserId,
		"officeId":  sessionData.OfficeId,
		"officeKey": office.OfficeKey,
		"exp":       time.Now().Add(time.Second * time.Duration(accessTokenTTL)).Unix(),
	},
	).SignedString([]byte(secretKey))

	if err != nil {
		return "", "", err
	}

	refreshTokenString, err := jwt.NewWithClaims(jwt.SigningMethodHS256, &jwt.MapClaims{
		"userId": sessionData.UserId,
		"exp":    time.Now().Add(time.Second * time.Duration(refreshTokenTTL)).Unix(),
	},
	).SignedString([]byte(secretKey))

	if err != nil {
		return "", "", err
	}

	if _, err := redis.SetEx(ctx, "accessToken:"+accessTokenString, sessionJson, time.Second*time.Duration(accessTokenTTL)).Result(); err != nil {
		return "", "", err
	}
	if _, err := redis.SetEx(ctx, "refreshToken:"+refreshTokenString, sessionJson, time.Second*time.Duration(refreshTokenTTL)).Result(); err != nil {
		return "", "", err
	}

	return accessTokenString, refreshTokenString, nil
}

func ValidateAuthToken(token *jwt.Token, newUserAgent string) error {
	if claims, ok := token.Claims.(jwt.MapClaims); ok {
		userAgent := claims["userAgent"].(string)
		if userAgent != newUserAgent {
			return fmt.Errorf("%s %v", "Invalid usergent:", userAgent)
		}
		timestamps := int64(claims["timestamps"].(float64))
		if (time.Now().Unix() - timestamps) > 60 {
			return fmt.Errorf("%s %v", "Invalid timestamps:", timestamps)
		}
	}
	return nil
}

func AccessTokenExist(tokenString string) error {
	ctx := context.Background()
	redis := client.GetRedisClient()
	if result, err := redis.Exists(ctx, "accessToken:"+tokenString).Result(); err != nil || result == 0 {
		return fmt.Errorf("%s %v", "Check accessToken exists failed:", err)
	}
	return nil
}

func ParseToken(tokenString string) (*jwt.Token, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("%s %v", "Unexpected signing method:", token.Header["alg"])
		}
		return []byte(os.Getenv("SECRET_KEY")), nil
	})
	if err != nil {
		return nil, fmt.Errorf("%s %v", "ParseToken failed:", err)
	}
	return token, nil
}

func Logout(accessToken, refreshToken string) error {
	ctx := context.Background()
	redis := client.GetRedisClient()

	sessionJson, err := redis.GetDel(ctx, "refreshToken:"+refreshToken).Result()
	if err != nil {
		return err
	}

	_, err = redis.Del(ctx, "accessToken:"+accessToken).Result()
	if err != nil {
		return err
	}

	var sessionData model.SessionData
	if err := json.Unmarshal([]byte(sessionJson), &sessionData); err != nil {
		return fmt.Errorf("%s %v", "Unmarshal sessionData failed:", err)
	}

	phpRedis := client.GetPHPSessionRedisClient()
	if _, err = phpRedis.Del(ctx, "laravel:"+sessionData.SessionId).Result(); err != nil {
		return err
	}

	return nil
}
