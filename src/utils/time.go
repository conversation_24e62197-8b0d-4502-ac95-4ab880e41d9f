package utils

import (
	"math/rand"
	"time"
)

func StringToTime(value string) (t time.Time, err error) {
	t, err = time.Parse(time.RFC3339, value)
	if err != nil {
		t, err = time.Parse("2006-01-02 15:04:05-07:00", value)
		return t, err
	}
	return t, err
}

func TimeToString(t time.Time) string {
	return t.Format(time.RFC3339)
}

func TimeToStringByLayout(t time.Time, layout string) string {
	return t.Format(layout)
}

func GenerateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	var result string
	for i := 0; i < length; i++ {
		result += string(charset[rand.Intn(len(charset))])
	}
	return result
}
