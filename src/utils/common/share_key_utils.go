package common

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/binary"
	"fmt"
	"time"
)

func GenerateShareKey(compeNo int) string {
	timestamp := time.Now().Unix()
	combinedInput := fmt.Sprintf("%d%d", compeNo, timestamp)

	salt := generateSalt()
	saltedInput := combinedInput + salt

	hash := sha256.Sum256([]byte(saltedInput))
	number := extractSixDigits(hash[:])

	return number
}

func generateSalt() string {
	saltBytes := make([]byte, 8)
	_, err := rand.Read(saltBytes)
	if err != nil {
		panic("Failed to generate salt")
	}
	return fmt.Sprintf("%x", saltBytes)
}

func extractSixDigits(hash []byte) string {
	hashUint32 := binary.BigEndian.Uint32(hash[:4])
	sixDigitNumber := int(hashUint32 % 1000000)
	return fmt.Sprintf("%06d", sixDigitNumber)
}
