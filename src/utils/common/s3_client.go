package common

import (
	"context"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"mi-restful-api/configs"
	"os"
	"sort"
	"strconv"
	"strings"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/s3"
)

// S3Client handles interactions with AWS S3
type S3Client struct {
	s3Client *s3.Client
}

// NewS3Client creates a new S3 client
func NewS3Client() *S3Client {
	region := os.Getenv("AWS_REGION")
	cfg, err := config.LoadDefaultConfig(context.Background(), config.WithRegion(region))
	if err != nil {
		slog.Error("unable to load SDK config", "err", err)
	}
	client := s3.NewFromConfig(cfg)
	return &S3Client{client}
}

// UploadFile uploads a file to S3 and returns the public URL
func (s *S3Client) UploadFile(file io.Reader, key string, contentType string) (string, error) {
	region := os.Getenv("AWS_REGION")
	bucket := configs.GetAwsS3Config().S3Bucket
	svc := s.s3Client
	_, err := svc.PutObject(context.Background(), &s3.PutObjectInput{
		Bucket:      &bucket,
		Key:         &key,
		Body:        file,
		ContentType: &contentType,
	})
	if err != nil {
		slog.Error("svc put object", "err", err)
	}
	fileURL := fmt.Sprintf("https://%s.s3.%s.amazonaws.com/%s", bucket, region, key)

	return fileURL, nil
}

// MoveFile move a file from tmp/ to prod/
func (s *S3Client) MoveFile(compeNo string) (string, error) {
	tmpDir := configs.GetAwsS3Config().S3PromotionalPrefix + compeNo + "/tmp/"
	files, err := s.getFileList(tmpDir)
	if err != nil {
		slog.Error("Failed to get file list", "err", err)
		return "", err
	}

	sortFilenames := s.sortFiles(files)
	needFile := sortFilenames[0]
	key := strings.Replace(needFile, "/tmp", "", 1)
	newFileUrl, err := s.copyFile(needFile, key)
	if err != nil {
		return "", err
	}

	for _, file := range files {
		_ = s.deleteFile(file)
	}
	return newFileUrl, nil
}

func (s *S3Client) ClearFile(url string) error {
	prefix := configs.GetAwsS3Config().S3PromotionalPrefix
	parts := strings.Split(url, prefix)
	if len(parts) < 2 {
		return errors.New("split error")
	}
	key := prefix + parts[1]
	return s.deleteFile(key)
}

// sortFiles
// sorts a list of filenames based on the timestamp in the filename
func (s *S3Client) sortFiles(filenames []string) []string {
	sort.Slice(filenames, func(i, j int) bool {
		timeI, _ := extractTimestamp(filenames[i])
		timeJ, _ := extractTimestamp(filenames[j])
		return timeI > timeJ
	})
	return filenames
}

// getFileList
// Get a list of files in a specific directory in S3
func (s *S3Client) getFileList(dir string) ([]string, error) {
	bucket := configs.GetAwsS3Config().S3Bucket
	svc := s.s3Client
	list, err := svc.ListObjectsV2(context.Background(), &s3.ListObjectsV2Input{
		Bucket: &bucket,
		Prefix: aws.String(dir),
	})
	if err != nil {
		return nil, err
	}

	if len(list.Contents) == 0 {
		return nil, errors.New("no files found")
	}

	var filenames []string
	for _, item := range list.Contents {
		filenames = append(filenames, *item.Key)
	}

	return filenames, nil
}

// copyFile
// Copy a file from one location to another in S3
// eg:promotional-images/1/tmp/cat1-1743566433635-9f0e2f09.jpg to promotional-images/1/cat1-1743566433635-9f0e2f09.jpg
func (s *S3Client) copyFile(source string, key string) (string, error) {
	region := os.Getenv("AWS_REGION")
	bucket := configs.GetAwsS3Config().S3Bucket
	svc := s.s3Client
	_, err := svc.CopyObject(context.Background(), &s3.CopyObjectInput{
		Bucket:     aws.String(bucket),
		CopySource: aws.String(bucket + "/" + source),
		Key:        aws.String(key),
	})

	fileURL := fmt.Sprintf("https://%s.s3.%s.amazonaws.com/%s", bucket, region, key)
	return fileURL, err
}

// deleteFile
// Delete a file from S3
func (s *S3Client) deleteFile(key string) error {
	bucket := configs.GetAwsS3Config().S3Bucket
	svc := s.s3Client
	_, err := svc.DeleteObject(context.Background(), &s3.DeleteObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(key),
	})

	return err
}

// get timestamp from filename
func extractTimestamp(filename string) (int64, error) {
	parts := strings.Split(filename, "-")
	if len(parts) < 3 {
		return 0, fmt.Errorf("invalid filename format")
	}
	timestampStr := parts[2]
	timestamp, err := strconv.ParseInt(timestampStr, 10, 64)
	if err != nil {
		return 0, fmt.Errorf("failed to parse timestamp: %v", err)
	}
	return timestamp, nil
}
