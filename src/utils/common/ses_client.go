package common

import (
	"context"
	"fmt"
	"log/slog"
	redisClient "mi-restful-api/client"
	"mi-restful-api/configs"
	"net/url"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/ses"
	"github.com/aws/aws-sdk-go-v2/service/ses/types"
)

const emailTemplate = `リンクが共有されました。
開催日: %s から %s まで
競技名: %s
URL : %s?compeNo=%d&compeName=%s&key=%s
共有キー : %s
※このメールは送信専用となります。
この件について再び連絡を取りたい場合は、
お手数ですが弊社担当までご連絡ください。
-------------------------------------
株式会社テクノクラフト`

func SendTemplatedEmail(email string, sharedUrl string, shareKey string, compeNo int, compeName string, from time.Time, to time.Time) error {
	slog.Info("SendTemplatedEmail", "email", email, "sharedUrl", sharedUrl, "shareKey", shareKey, "compeNo", compeNo, "compeName", compeName)
	// Load AWS configuration
	cfg, err := config.LoadDefaultConfig(context.TODO(), config.WithRegion(configs.GetShareConfig().EmailAwsRegion))
	if err != nil {
		return fmt.Errorf("failed to load AWS config: %w", err)
	}

	// Create SES client
	sesClient := ses.NewFromConfig(cfg)

	//url encode compeName
	urlEncodedCompeName := url.QueryEscape(compeName)

	// Define email parameters
	input := &ses.SendEmailInput{
		Source: aws.String(configs.GetShareConfig().EmailSource), // Must be verified in SES
		Destination: &types.Destination{
			ToAddresses: []string{email},
		},
		Message: &types.Message{
			Subject: &types.Content{
				Data: aws.String("[Marshal-i]プロジェクタ共有リンクのお知らせ"),
			},
			Body: &types.Body{
				Text: &types.Content{
					Data: aws.String(fmt.Sprintf(emailTemplate, from, to, compeName, sharedUrl, compeNo, urlEncodedCompeName, shareKey, shareKey)),
				},
			},
		},
	}

	redis := redisClient.GetRedisClient()

	// check if email is sent before
	_, err = redis.Get(context.Background(), "email:"+email+":"+shareKey).Result()
	if err == nil {
		slog.Info("Email already sent", "email", email, "shareKey", shareKey)
		return nil
	}

	// Send email
	_, err = sesClient.SendEmail(context.TODO(), input)

	if err != nil {
		slog.Error("Failed to send email", "err", err, "email", email, "sharedUrl", sharedUrl, "shareKey", shareKey, "compeNo", compeNo, "compeName", compeName)
		return fmt.Errorf("failed to send email: %w", err)
	}

	//add email into redis to void send too many email
	_, err = redis.SetEx(context.Background(), "email:"+email+":"+shareKey, 1, time.Minute*5).Result()

	if err != nil {
		slog.Error("Failed to set email into redis", "err", err, "email", email, "shareKey", shareKey)
		return fmt.Errorf("failed to set email into redis: %w", err)
	}

	slog.Info("Email sent successfully!")
	return nil
}
