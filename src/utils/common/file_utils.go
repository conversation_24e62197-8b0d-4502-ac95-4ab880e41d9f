package common

import (
	"fmt"
	"path/filepath"
	"strings"
	"time"

	"github.com/google/uuid"
)

// IsImageContentType checks if the content type is an image
func IsImageContentType(contentType string) bool {
	validTypes := []string{
		"image/jpeg",
		"image/jpg",
		"image/png",
		"image/gif",
		"image/webp",
	}

	for _, validType := range validTypes {
		if contentType == validType {
			return true
		}
	}

	return false
}

// GenerateUniqueFilename creates a unique filename while preserving the original extension
func GenerateUniqueFilename(originalFilename string) string {
	ext := filepath.Ext(originalFilename)
	name := strings.TrimSuffix(originalFilename, ext)

	// Create a timestamp and UUID for uniqueness
	timestamp := time.Now().UnixNano() / int64(time.Millisecond)
	uniqueID := uuid.New().String()

	// Format: original-name-timestamp-uuid.ext
	return fmt.Sprintf("%s-%d-%s%s", name, timestamp, uniqueID[:8], ext)
}
