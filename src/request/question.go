package request

// Question create new question
type Question struct {
	// type 設問タイプ
	Type int8 `json:"type" binding:"required,gt=0,lt=3"`
	// content 設問内容
	Content string `json:"content" binding:"required,max=200"`
	// require 必須
	Require int8 `form:"require" json:"require" binding:"required"`
}

// PutQuestionParams update request
type PutQuestionParams struct {
	// question content
	Content string `form:"content" json:"content" binding:"required,max=200"`
	// require 必須
	Require int8 `form:"require" json:"require" binding:"required"`
}

// UpdateIndexParams update request
type UpdateIndexParams struct {
	// id 設問ID
	Id int `form:"id" json:"id" binding:"required,number"`
	// index 設問順番
	Index int8 `form:"index" json:"index" binding:"required,number"`
}
