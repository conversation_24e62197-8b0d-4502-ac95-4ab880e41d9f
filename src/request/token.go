package request

import "mi-restful-api/model"

type AuthReq struct {
	// session_id , key of session in redis
	SessionId string `json:"session_id" form:"session_id" binding:"required"`
	// user_agent , value from browser
	UserAgent string `json:"user_agent" form:"user_agent" binding:"required"`
	// timestamps , user client timestamps. eg. browser
	Timestamps int64 `json:"timestamps" form:"timestamps" binding:"required,number"`

	SessionData model.SessionData `json:"session_data" form:"session_data" binding:"required"`
}

type AccessReq struct {
	AuthToken string `json:"auth_token" form:"auth_token" binding:"required"`
}

type RefreshReq struct {
	RefreshToken string `json:"refresh_token" form:"refresh_token" binding:"required"`
}

type LogoutReq struct {
	AccessToken  string `json:"access_token" form:"access_token" binding:"required"`
	RefreshToken string `json:"refresh_token" form:"refresh_token" binding:"required"`
}
