package request

import (
	"time"
)

// QuestionnaireIndexReq list data
type QuestionnaireIndexReq struct {
	// start_date 開始時間
	StartDate time.Time `json:"start_date" form:"start_date" time_format:"2006-01-02" binding:"required"`
	// end_date 終了時間
	EndDate time.Time `json:"end_date" form:"end_date" time_format:"2006-01-02" binding:"required"`
	// caddy_id キャディID
	CaddyId int `json:"caddy_id" form:"caddy_id" binding:"omitempty"`
	// weekday 曜日  1 平日、2 週末、 3 指定なし
	Weekday int `json:"weekday" form:"weekday" binding:"required,min=0,max=3"`
	// page ページ数 default 1 start
	Page int `json:"page" form:"page" binding:"omitempty"`
	// limit １ページ当たり件数
	PageSize int `json:"limit" form:"limit" binding:"omitempty"`
}

// QuestionnaireShowReq get one request
type QuestionnaireShowReq struct {
	// id 評価表主キー
	Id int `form:"id" json:"id" binding:"required"`
}

// CartInfoRespReq get one request
type CartInfoRespReq struct {
	// id 評価表主キー
	Id int `form:"id" json:"id" binding:"required"`
}

// QuestionnaireCreateReq create request
type QuestionnaireCreateReq struct {
	// cart_no カート番号
	CartNo string `json:"cart_no" binding:"required"`
	// caddy_id キャディID
	CaddyId string `json:"caddy_id" binding:"omitempty,max=200"`
	// caddy_id キャディ名前
	CaddyName string `json:"caddy_name" binding:"omitempty,max=200"`
	// played_date プレイ時間
	PlayedDate string `json:"played_date" binding:"required,max=200"`
	// start_time スタート時間
	StartTime string `json:"start_time" binding:"omitempty,max=200"`
	// start_course スタートコース
	StartCourse string `json:"start_course" binding:"omitempty,max=200"`
	// player_id プレイヤーid
	PlayerId string `json:"player_id" binding:"required,max=200"`
	// player_name プレイヤー名
	PlayerName string `json:"player_name" binding:"required,max=200"`
	// survey 設問
	Surveys []Survey `json:"survey" binding:"required"`
	// sort_key dynamodb sort_key
	SortKey string `json:"sort_key" binding:"omitempty"`
	// serial from operation-info (combine date and players sorted ids)
	Serial string `json:"serial" binding:"omitempty"`
	// feedback Custom Comment
	Feedback string `json:"feedback" binding:"omitempty,max=1100"`
	// キャディのfeedback Custom Comment
	FeedbackCaddy string `json:"feedback_caddy" binding:"omitempty,max=1100"`
	// ゴルフ場のfeedback Custom Comment
	FeedbackGolf string `json:"feedback_golf" binding:"omitempty,max=1100"`
}

type Survey struct {
	// id question_id
	Id int `json:"id" binding:"required,number"`
	// answer 回答id
	AnswerId int `json:"answer" binding:"required,number"`
}

// QuestionnaireUpdateReq update request
type QuestionnaireUpdateReq struct {
	// id 評価表主キー
	Id int `form:"id" json:"id" binding:"required"`
	// question sort_key
	Index int `form:"index" json:"index" binding:"omitempty,number"`
	// question content
	Content string `form:"content" json:"content" binding:"omitempty,max=200"`
}

// QuestionnaireDelReq delete request
type QuestionnaireDelReq struct {
	// id 評価表主キー
	Id int `form:"id" json:"id" binding:"required"`
}

// QuestionnaireCartInfoReq delete request
type QuestionnaireCartInfoReq struct {
	// id 評価表主キー
	Id int `form:"id" json:"id" binding:"required"`
}
