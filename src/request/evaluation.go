package request

// EvaluationIndexReq create
type EvaluationIndexReq struct {
	// office_id コースid
	OfficeId string `json:"office_id" form:"office_id" binding:"required"`
	// page ページ デフォルト 1から開始
	Page int `json:"page" form:"page" binding:"omitempty"`
	// page_size １ページあたり件数
	PageSize int `json:"page_size" form:"page_size" binding:"omitempty"`
}

// EvaluationCreateReq create
type EvaluationCreateReq struct {
	// score スコア
	Score int `json:"score"`
	// content 表示内容
	Content string `json:"content"`
}

// EvaluationPutReq update
type EvaluationPutReq struct {
	// id 評価段階
	ID int `form:"id" json:"id" binding:"omitempty,number"`
	// stage だんかい
	Stage int `form:"stage" json:"stage" binding:"required,number"`
	// score 評価配点
	Score int `form:"score" json:"score" binding:"required,number"`
	// content 評価文言
	Content string `form:"content" json:"content" binding:"required,max=200"`
}

// EvaluationDelReq delete param
type EvaluationDelReq struct {
	// comment_id 評価設置表 id
	ID int `form:"comment_id" json:"comment_id" binding:"required,number"`
}
