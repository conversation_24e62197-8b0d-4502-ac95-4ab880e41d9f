package tee

type TeeSheetPlayerSearchReq struct {
	PlayerName string  `json:"player_name" form:"player_name" binding:"required"` // PlayerName
	Birthday   string  `json:"birthday" form:"birthday" binding:"required"`       // birthday 1990-01-01
	PlayDate   *string `json:"play_date" form:"play_date"`                        // play_date 20230101 , default should be today
}

type TeeSheetPlayerUpdateReq struct {
	CartNo             int     `json:"cart_no" binding:"required,min=1"`
	ScheduledStartTime *string `json:"scheduled_start_time" binding:"omitempty"` //should be "hh:mm" or null
	PlayDate           string  `json:"play_date" binding:"required"`
	PlayerNo           int     `json:"player_no" binding:"required,min=1"`
	GlidNo             *string `json:"glid_no"`
	HdcpIndex          *string `json:"hdcp_index"`
	Hdcp               *string `json:"hdcp"`
	Birthday           *string `json:"birthday"`
	Gender             *int    `json:"gender"`
}
