package request

import "time"

type CaddyAnalysisIndexReq struct {
	// start_date 開始時間
	StartDate string `json:"start_month" form:"start_month" time_format:"2006-01" binding:"required"`
	// end_date 終了時間
	EndDate string `json:"end_month" form:"end_month" time_format:"2006-01" binding:"required"`
	// caddy_id キャディID
	CaddyId string `json:"caddy_id" form:"caddy_id" binding:"omitempty"`
	// weekday 曜日  1 平日、2 週末、 3 指定なし
	Weekday int `json:"weekday" form:"weekday" binding:"required,min=0,max=3"`
}

type StatisticalIndexReq struct {
	// start_date 開始時間
	StartDate string `json:"start_month" form:"start_month" time_format:"2006-01" binding:"required"`
	// end_date 終了時間
	EndDate string `json:"end_month" form:"end_month" time_format:"2006-01" binding:"required"`
	// caddy_id キャディID
	CaddyId string `json:"caddy_id" form:"caddy_id" binding:"omitempty"`
	// weekday 曜日  1 平日、 2 週末、 3 指定なし
	Weekday int `json:"weekday" form:"weekday" binding:"required,min=0,max=3"`
}

type AnswerLowestReq struct {
	// start_date 開始時間
	StartDate time.Time `json:"start_date" form:"start_date" time_format:"2006-01-02" binding:"required"`
	// end_date 終了時間
	EndDate time.Time `json:"end_date" form:"end_date" time_format:"2006-01-02" binding:"required"`
	// caddy_id キャディID
	CaddyId int `json:"caddy_id" form:"caddy_id" binding:"omitempty"`
	// weekday 曜日  1 平日、2 週末、 3 指定なし
	Weekday int `json:"weekday" form:"weekday" binding:"required,min=0,max=3"`
}
