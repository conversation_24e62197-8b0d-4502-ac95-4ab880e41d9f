package request

// StartGuideSettingsCreateReq create request
type StartGuideSettingsCreateReq struct {
	// スタート予定時間表示
	EnableStartTime   *int        `json:"enable_start_time"  binding:"required,min=0,max=1"`
	// メイン文言（常時表示）
	MainTextAlways    *string     `json:"main_text_always"  binding:"required,max=30"`
	// サブ文言（常時表示）
	SubTextAlways     *string     `json:"sub_text_always"  binding:"required,max=45"`
	// 自動スタート案内表示
	EnableAutoStart   *int        `json:"enable_autostart"  binding:"required,min=0,max=1"`
	// 自動スタート案内基準
	AutoStartType     *int        `json:"autostart_type"  binding:"required,min=0,max=1"`
	// スタート予定時間
	StartTimeSchedule *int        `json:"start_time_schedule"  binding:"required,number"`
	// スタートまでの順番
	StartNumber       *int        `json:"start_number"  binding:"required,number"`
	// サブ文言（自動案内）
	SubTextAuto       *string     `json:"sub_text_auto"  binding:"required,max=45"`
}

