package request

import (
	"log/slog"
	"mi-restful-api/model"
	"strings"

	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/validator/v10"
)

// Score create data
type Score struct {
	// office_id コースid
	OfficeID string `json:"office_id" binding:"required"`
	// serial シリアル　 フォーマット[cartNo_timestamp_rand]
	Serial string `json:"serial" binding:"required"`
	// uid
	Uid string `json:"uid" binding:"required"`
	// cart_no カート番号
	CartNo int `json:"cart_no" binding:"required"`
	// caddy_id キャディid
	CaddyId string `json:"caddy_id" binding:"required,max=200"`
	// caddy_name キャディ名前
	CaddyName string `json:"caddy_name" binding:"required,max=200"`
	// player_id プレイヤid
	PlayerId string `json:"player_id" binding:"required,max=200"`
	// player_name プレイヤ名前
	PlayerName string `json:"player_name" binding:"required,max=200"`
	// questions 問題アレイ
	Questions []model.Question `json:"questions" binding:"required"`
}

// PutScoreParams update score
type PutScoreParams struct {
	// office_id コースid
	OfficeID string `form:"office_id" json:"office_id" binding:"required"`
	// sort_key ソートキー
	Key string `form:"sort_key" json:"sort_key" binding:"required"`
	// detail 更新内容
	Detail string `form:"detail" json:"detail" binding:"required,max=200"`
}

// DeleteScore delete score
type DeleteScore struct {
	// office_id コースid
	OfficeID string `form:"office_id" json:"office_id" binding:"required"`
	// sort_key ソートキー
	Key string `form:"sort_key" json:"sort_key" binding:"required"`
}

type MultiQueryParam struct {
	OfficeID string `json:"office_id" form:"office_id" binding:"required"`
	SortKey  string `json:"sort_key" form:"sort_key" binding:"omitempty"`
	Year     string `json:"year" form:"year" binding:"required"`
	Month    string `json:"month" form:"month" binding:"required,len=2"`
	CaddyId  string `json:"caddy_id" form:"caddy_id" binding:"omitempty"`
	DayType  string `json:"day_type" form:"day_type" binding:"omitempty,enum=workday:weekday:all"`
}

type ScoreList struct {
	OfficeID   string `json:"office_id" binding:"required"`
	Serial     string `json:"serial" binding:"required"`
	Uid        string `json:"uid" binding:"required"`
	CartNo     string `json:"cart_no" binding:"required"`
	CaddyId    string `json:"caddy_id" binding:"required,max=200"`
	CaddyName  string `json:"caddy_name" binding:"required,max=200"`
	PlayerId   string `json:"player_id" binding:"required,max=200"`
	PlayerName string `json:"player_name" binding:"required,max=200"`
	Detail     string `json:"detail" binding:"required,max=200"`
}

var customValidator *validator.Validate

func init() {
	// 新しい validator インスタンスを作る
	if v, ok := binding.Validator.Engine().(*validator.Validate); ok {
		customValidator = v
		err := customValidator.RegisterValidation("enum", enumValidator)
		if err != nil {
			slog.Error("validator", "err", err.Error())
		}
	}
}

// EnumValidator エニューメレーションをチェック
func enumValidator(fl validator.FieldLevel) bool {
	param := fl.Param()
	enums := strings.Split(param, ":")

	value := fl.Field().String()
	for _, enum := range enums {
		if value == enum {
			return true
		}
	}
	return false
}
