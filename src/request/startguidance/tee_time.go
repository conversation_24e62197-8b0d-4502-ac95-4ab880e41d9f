package startguidance

type CartInfoReq struct {
	LockerNo string `json:"locker_no" form:"locker_no" binding:"required,gte=0"` // LockerNo
}

type TeeTimePersonalReq struct {
	LockerNo string `json:"locker_no" form:"locker_no" binding:"required,gte=0"` // LockerNo
}

type TeeTimeReq struct {
	From  string `json:"from" form:"from" binding:"required,len=5,regexp=^([01]?[0-9]|2[0-3]):[0-5][0-9]$"` // from HH:MM
	Limit int    `json:"limit" form:"limit" binding:"required,gte=0"`                                       //  limit >0
}
