package compe

type OnlineCompeOfficeReq struct {
	Office<PERSON>ey *string `json:"office_key" form:"office_key"`                  //オフィスキー
	PlayDate  *string `json:"play_date" form:"play_date"`                    //開催日 eg. 20060102
	CompeType *int    `json:"compe_type" form:"compe_type"`                  //コンペタイプ 0 team(for phase 2) 1 個人戦
	CompeKind *string `json:"compe_kind" form:"compe_kind"`                  //競技方法  handy, peoria
	FreeWord  *string `json:"free_word" form:"free_word"`                    // key word in the compe name filter by this word
	Offset    *int    `json:"offset" form:"offset" binding:"required,gte=0"` // offset >=0
	Limit     int     `json:"limit" form:"limit" binding:"required,gte=100"` // limit more than 100
}
