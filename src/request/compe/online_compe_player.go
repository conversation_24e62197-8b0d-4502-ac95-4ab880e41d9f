package compe

import "mi-restful-api/model/player"

type OnlineCompePlayerJoinReq struct {
	PlayerNo      int     `json:"player_no" binding:"required,min=1"`
	PlayerName    string  `json:"player_name"`
	Gender        *int    `json:"gender"` //１：男性、２：女性
	Birthday      *string `json:"birthday"`
	GlidNo        *string `json:"glid_no"`
	TeeId         *string `json:"tee_id"`
	Hdcp          *string `json:"hdcp"`
	HdcpIndex     *string `json:"hdcp_index"`
	CompeNo       int     `json:"compe_no"`
	OfficeKey     string  `json:"office_key"`
	PlayDate      string  `json:"play_date"`
	TeamClassType *int    `json:"team_class_type"`
	CartNo        int     `json:"cart_no"`
	CourseIndex   *int    `json:"course_index"`
	IsPaid        bool    `json:"is_paid"`
}

type OnlineCompePlayerSearchReq struct {
	OfficeKey   string `json:"office_key"`
	PlayerNo    int    `json:"player_no"`
	PlayDateStr string `json:"play_date_str"`
}

type OnlineCompeJoinedPlayersReq struct {
	JoinedPlayers []player.JoinedPlayer `json:"joined_players" binding:"required,min=0,dive"`
}
