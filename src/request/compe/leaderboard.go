package compe

import "mi-restful-api/model/compe"

type Ranking struct {
	PartitionKey    string               `json:"partition_key"`
	Sort<PERSON>ey         string               `json:"sort_key"`
	Expiration_time int64                `json:"expiration_time"`
	Updated_at      string               `json:"updated_at"`
	Details         compe.RankingDetails `json:"details"`
}

type LeaderboardRankingShareReq struct {
	CompeNo  int    `json:"compe_no" binding:"required,min=1"`
	ShareKey string `json:"share_key" binding:"required"`
	Email    string `json:"email" binding:"required"`
}

type LeaderboardRankingShareKeyCreateReq struct {
	CompeNo  int     `json:"compe_no" binding:"required,min=1"`
	ShareKey *string `json:"share_key" binding:"omitempty,len=6"` // optinal size should be 6
}
