package compe

import "mi-restful-api/model/compe"

/*
	{
	 "basic": {
	  "compe_no": 1,
	  "compe_name": "片山津　平日月例",
	  "duration": {
	   "from": "2025-02-10T13:45:04+09:00",
	   "to": "2025-02-10T13:45:04+09:00"
	  },
	  "target_office_type": 0,
	  "target_office": "ipctest",
	  "target_office_list": [
	   "ipctest",
	   "ipctest1",
	   "ipctest2"
	  ],
	  "organizer": "片山津　平日月例",
	  "participation_fee": 1000,
	  "promotional_image": "s3://xxxxx.xxxx.xxxx.png",
	  "old_compe": {
	   "old_compe_no": "150",
	   "old_compe_office_key": "ipctest",
	   "old_compe_start_time": "20251101"
	  }
	 },
	  "compe_setting": {
	  "entry_from_navi": 0,
	  "ranking_aggregation": 0,
	  "round": "0.5",
	  "prize_setting": [
	   {
	    "type": "とび賞①",
	    "setting": {
	     "1": 10,
	     "2": 100,
	     "3": 10
	    }
	   },
	   {
	    "type": "additon",
	    "name": "金賞",
	    "order": {
	     "1": 1,
	     "2": 2,
	     "3": 3,
	     "5": 4
	    }
	   }
	  ]
	 },
	 {
	 "compe_type_setting": {
	  "type": 1,
	  "handy": {
	   "distribution": 1,
		"ranking_order": {
			"1": "brithday",
			"2": "gender",
			"3": "count_back",
			"5":"hdcp"
			},
	   "handicap": {
	    "type": 0,
	    "hdcp_date": "2025-01-09"
	   },
	   "net_computation_type": 0
	  },
	  "peoria": {
	   "aggregation_method": {
	    "type": 1
	   },
	   "par_limit": {
	    "type": 0,
	    "par_n": 1,
	    "par_x": 1
	   },
	   "handicap_upper_limit": {
	    "men": 0,
	    "women": 0
	   },
	   "distribution": 1,
		"ranking_order": {
			"1": "brithday",
			"2": "gender",
			"3": "count_back",
			"5":"hdcp"
			},
	  }
	 }
	},

	{
	 "other_setting": {
	  "marker_setting": 0,
	  "leadboard_change": {
	   "type": 0,
	   "default": "ネットをデフォルト表示"
	  }
	 }
	}

}
*/

type OnlineCompeCreationReq struct {
	compe.Basic            `json:"basic" binding:"required"`
	compe.CompeSetting     `json:"compe_setting" binding:"required"`
	compe.CompeTypeSetting `json:"compe_type_setting" binding:"required"`
	compe.OtherSetting     `json:"other_setting" binding:"required"`
	*compe.PrivateSetting  `json:"private_setting"`
}

type OnlineCompeUpdateReq struct {
	*compe.Basic            `json:"basic"`
	*compe.CompeSetting     `json:"compe_setting"`
	*compe.CompeTypeSetting `json:"compe_type_setting"`
	*compe.OtherSetting     `json:"other_setting"`
	*compe.PrivateSetting   `json:"private_setting"`
}
