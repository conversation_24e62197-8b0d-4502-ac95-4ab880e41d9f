package compe

import "mi-restful-api/model/compe"

/*
	{
	 "details": {
	  "compe_setting": {
	   "entry_from_navi": 0,
	   "ranking_aggregation": 0,
	   "round": "0.5"
	  },
	  "other_setting": {
	   "marker_setting": 0
	  },
	  "handicap": {
	   "type": 0
	  }
	 }
	}
*/
type OnlineCompeDefaultSettingReq struct {
	CompeSetting compe.DefaultCompeSetting `json:"compe_setting"` //コンペ設定
	OtherSetting compe.DefaultOtherSetting `json:"other_setting"` //その他設定
	Handicap     compe.DefaultHandicap     `json:"handicap"`      //ハンディキャップSetting
}
