package compe

type SearchPlayerInfoReq struct {
	SearchType int    `json:"search_type" form:"search_type" binding:"required"` // serach_type 1：internal api 2: external api 3: internal api + external api
	PlayerName string `json:"player_name" form:"player_name"`
	Birthday   string `json:"birthday" form:"birthday"`   // yyyy-MM-dd
	HdcpDate   string `json:"hdcp_date" form:"hdcp_date"` // handy.handicap.hdcp_date  yyyy-MM-dd
	GlidNo     string `json:"glid_no" form:"glid_no"`
}

type GolferSearchReq struct {
	Required struct {
		GlidNo         string `json:"glid_no"`
		OfficeId       string `json:"office_id"`
		CourseIndex    string `json:"course_index"`
		PlayerName     string `json:"player_name"`
		PlayerBirthday string `json:"player_birthday"`
		SearchType     string `json:"search_type"`
	} `json:"required"`
}

type SearchCourseRatingReq struct {
	Required struct {
		OfficeId    string `json:"office_id"`
		CourseIndex int    `json:"course_index"`
	} `json:"required"`
}

type GetGolferDataReq struct {
	Required struct {
		GlidNo         string `json:"glid_no"`
		TargetHdcpDate string `json:"target_hdcp_date"`
	} `json:"required"`
}
