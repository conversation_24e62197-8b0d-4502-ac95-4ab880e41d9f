package router

import (
	v1 "mi-restful-api/controller/v1"
	v1Compe "mi-restful-api/controller/v1/compe"
	v1StartGuidance "mi-restful-api/controller/v1/startguidance"
	"mi-restful-api/middlewares"
	"os"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

func Api(r *gin.Engine) {

	// Swagger 路由
	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	apiPrefix := os.Getenv("API_PREFIX")
	router := r.Group(apiPrefix)
	router.GET("/ping", v1.Ping)
	appRouter := router.Group("/app", middlewares.AppAuthCheck(), middlewares.RequestLogMiddleware(), middlewares.ResponseLogMiddleware())
	webRouter := router.Group("/web", middlewares.AuthApi(), middlewares.RequestLogMiddleware(), middlewares.ResponseLogMiddleware())

	// question manage
	appRouter.GET("/question", new(v1.Question).Index)
	webRouter.GET("/question", new(v1.Question).Index)
	//router.GET("/questions/:office_id", new(v1.Question).Show)

	webRouter.POST("/question", new(v1.Question).Store)
	webRouter.PUT("/question/:id", new(v1.Question).Update)
	webRouter.PUT("/questionindex", new(v1.Question).UpdateIndex)
	webRouter.DELETE("/question/:id", new(v1.Question).Delete)

	// evaluation manage
	webRouter.GET("/evaluation", new(v1.Evaluation).Index)
	appRouter.GET("/evaluation", new(v1.Evaluation).Index)
	//router.GET("/comments/:office_id", new(v1.Evaluation).Show)
	//router.POST("/comments", new(v1.Evaluation).Store)
	webRouter.PUT("/evaluation", new(v1.Evaluation).Update)
	//router.DELETE("/comments/:office_id", new(v1.Evaluation).Delete)

	// score manage
	webRouter.GET("/questionnaire", new(v1.Questionnaire).Index)
	appRouter.GET("/cartinfo", new(v1.Questionnaire).CartInfo)
	appRouter.GET("/cartinfo/v2", new(v1.Questionnaire).CartInfoV2)

	//router.GET("/scores/:office_id", new(v1.Questionnaire).Show)
	appRouter.POST("/answer", new(v1.Questionnaire).Store)
	//router.DELETE("/scores/:office_id", new(v1.Questionnaire).Delete)
	webRouter.GET("/questionnaire/csv", new(v1.Questionnaire).IndexCsv)

	webRouter.GET("/caddy", new(v1.AnswerAnalysis).AnalysisByCaddy)
	webRouter.GET("/statistical", new(v1.AnswerAnalysis).Statistical)

	webRouter.GET("/answer/lowest", new(v1.AnswerAnalysis).AnswerLowest)

	webRouter.GET("/caddylist", new(v1.Caddy).Index)
	appRouter.GET("/caddyon", new(v1.Caddy).CaddyOn)

	// アンケート各種設定のAPI
	appRouter.GET("/quesnairesettings", new(v1.QuesnaireSettings).Index)
	webRouter.GET("/quesnairesettings", new(v1.QuesnaireSettings).Index)
	webRouter.POST("/quesnairesettings", new(v1.QuesnaireSettings).Store)

	// システム設定取得API
	webRouter.GET("/office/settings", new(v1.OfficeSettings).Index)
	appRouter.GET("/office/settings", new(v1.OfficeSettings).Index)
	// システム設定保存API
	webRouter.POST("/office/settings", new(v1.OfficeSettings).Store)
	// バーコードからロッカーキーに変換API
	appRouter.GET("/office/barcodetolocker", new(v1.OfficeSettings).BarcodeToLocker)

	router.GET("/scores/month", new(v1.ScoreAnalysis).Index)
	router.GET("/scores/analysis", new(v1.ScoreAnalysis).GetDataByCondition)
	router.POST("/scores/month", new(v1.ScoreAnalysis).Store)

	router.GET("/scores/list", new(v1.ScoreAnalysisList).Index)
	router.POST("/scores/list", new(v1.ScoreAnalysisList).Store)

	compeWebRouter := router.Group("/web/online-compe", middlewares.AuthApi(), middlewares.RequestLogMiddleware(), middlewares.ResponseLogMiddleware())
	compeWebRouter.GET("/latest-no", new(v1Compe.OnlineCompe).GetLatestNo)
	compeWebRouter.POST("/img/upload/:compeNo", new(v1Compe.OnlineCompe).UploadImg)
	compeWebRouter.PUT("/create", new(v1Compe.OnlineCompe).Create)
	compeWebRouter.POST("/update", new(v1Compe.OnlineCompe).Update)
	compeWebRouter.GET("/:compeNo", new(v1Compe.OnlineCompe).OnlineCompe)
	compeWebRouter.POST("/join", new(v1Compe.OnlineCompe).JoinCompe)
	compeWebRouter.POST("/default-setting/update", new(v1Compe.OnlineCompe).UpdateDefaultSetting)
	compeWebRouter.GET("/default-setting", new(v1Compe.OnlineCompe).DefaultSetting)
	compeWebRouter.GET("/office-compe/list", new(v1Compe.OnlineCompe).ListOfficeCompe)
	compeWebRouter.GET("/course/list", new(v1Compe.OnlineCompe).ListCourse)
	compeWebRouter.GET("/compe-player/:compeNo/list", new(v1Compe.OnlineCompe).ListCompePlayers)
	compeWebRouter.POST("/compe-player/:compeNo/update", new(v1Compe.OnlineCompe).UpdateCompePlayers)
	compeWebRouter.GET("/player-compe/:playerNo", new(v1Compe.OnlineCompe).PlayerCompe)
	compeWebRouter.GET("/player-info/search", new(v1Compe.OnlineCompe).SearchPlayerInfo)
	compeWebRouter.GET("/tee/sheet", new(v1Compe.OnlineCompe).TeeSheet)
	compeWebRouter.POST("/tee/sheet/player/update", new(v1Compe.OnlineCompe).TeeSheetPlayerUpdate)
	compeWebRouter.GET("/tee/info", new(v1Compe.OnlineCompe).TeeInfo)
	compeWebRouter.GET("/tee/sheet/player/search", new(v1Compe.OnlineCompe).TeeSheetPlayerSearch)
	compeWebRouter.GET("/leaderboard/ranking/:compeNo", new(v1Compe.OnlineCompe).LeaderboardRanking)
	compeWebRouter.GET("/leaderboard/ranking/type/:compeNo", new(v1Compe.OnlineCompe).LeaderboardRankingType)
	compeWebRouter.POST("/leaderboard/ranking/share-key/create", new(v1Compe.OnlineCompe).LeaderboardRankingShareKeyCreate)

	compeAppRouter := router.Group("/app/online-compe", middlewares.AppAuthCheck(), middlewares.RequestLogMiddleware(), middlewares.ResponseLogMiddleware())

	compeAppRouter.GET("/player-compe/:playerNo", new(v1Compe.OnlineCompe).PlayerCompe)
	compeAppRouter.GET("/:compeNo", new(v1Compe.OnlineCompe).OnlineCompe)

	appRouter.GET("/leaderboard/ranking/:compeNo", new(v1Compe.OnlineCompe).LeaderboardRanking)
	appRouter.GET("/leaderboard/ranking/type/:compeNo", new(v1Compe.OnlineCompe).LeaderboardRankingType)

	router.GET("/web/online-compe/leaderboard/ranking/shared/:shareKey", new(v1Compe.OnlineCompe).LeaderboardRankingShared)
	router.GET("/web/online-compe/leaderboard/ranking/type/shared/:shareKey", new(v1Compe.OnlineCompe).LeaderboardRankingTypeShared)
	router.POST("/web/online-compe/leaderboard/ranking/share", new(v1Compe.OnlineCompe).LeaderboardRankingShare)

	// for phase2
	// compeWebRouter.GET("/pairing/:compeNo", new(v1Compe.OnlineCompe).Pairing)

	// for start-guidance
	startGuidanceWebRouter := router.Group("/web/start-guidance", middlewares.AuthApi(), middlewares.RequestLogMiddleware(), middlewares.ResponseLogMiddleware())
	// スタート案内設定取得API
	startGuidanceWebRouter.GET("/settings", new(v1StartGuidance.StartGuideSettings).Index)
	// スタート案内設定保存API
	startGuidanceWebRouter.POST("/settings", new(v1StartGuidance.StartGuideSettings).Store)
	// ティータイム取得API
	startGuidanceWebRouter.GET("/tee-time", new(v1StartGuidance.TeeTime).Index)

	startGuidanceAppRouter := router.Group("/app/start-guidance", middlewares.AppAuthCheck(), middlewares.RequestLogMiddleware(), middlewares.ResponseLogMiddleware())
	// ティータイム（個人）取得API
	startGuidanceAppRouter.GET("/tee-time/personal", new(v1StartGuidance.TeeTime).Personal)
	// スタート案内インフォメーション取得API
	startGuidanceAppRouter.GET("/info", new(v1StartGuidance.StartGuideSettings).Information)
	//
	startGuidanceAppRouter.GET("/cart-info", new(v1StartGuidance.TeeTime).CartInfo)

	// token related api, no need to check header "Authorization"
	router.POST("/token", new(v1.Token).Auth)
	router.POST("/refresh", new(v1.Token).Refresh)
	router.POST("/access", new(v1.Token).AccessToken)

	router.POST("/logout", new(v1.Token).Logout)
}
