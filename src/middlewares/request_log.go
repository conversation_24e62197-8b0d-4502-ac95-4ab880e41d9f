package middlewares

import (
	"bytes"
	"io"
	"mi-restful-api/enum"
	"mi-restful-api/logging"
	"mi-restful-api/utils/common"

	"github.com/gin-gonic/gin"
)

type ReqbodyLogWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w ReqbodyLogWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

func RequestLogMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {

		var reqBody []byte
		if c.Request.Body != nil {
			reqBody, _ = io.ReadAll(c.Request.Body)
		}
		c.Request.Body = io.NopCloser(bytes.NewBuffer(reqBody))

		c.Next()

		officeId := c.GetString("office_id")
		officeKey := c.GetString("office_key")
		clientType := c.GetString("client_type")
		requestId := common.GetRequestId(c)

		logging.LogFormat(enum.LogInfo, map[string]any{
			"category":    enum.LogCategoryApp,
			"msg":         "Request log",
			"module":      "GinMiddleware",
			"office_id":   officeId,
			"office_key":  officeKey,
			"client_type": clientType,
			"request_id":  requestId,
			"param": map[string]any{
				"req": map[string]any{
					"method": c.Request.Method,
					"path":   c.FullPath(),
					"query":  c.Request.URL.RawQuery,
					"body":   string(reqBody),
				},
			},
		})
	}
}
