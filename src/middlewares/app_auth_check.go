package middlewares

import (
	"log/slog"
	"mi-restful-api/configs"
	"mi-restful-api/enum"
	"mi-restful-api/model"
	"mi-restful-api/repository"
	"net/http"

	"github.com/gin-gonic/gin"
)

func AppAuthCheck() gin.HandlerFunc {
	return func(c *gin.Context) {
		authToken := c.Get<PERSON>eader("token")
		deviceId := c.GetHeader("device-id")
		if authToken == "" {
			c.JSON(http.StatusBadRequest, "auth params error, auth_token required")
			c.Abort()
			return
		}

		mockUserInfo := mockTestAuth(authToken, deviceId)
		if mockUserInfo != nil {
			c.Set("app_auth_info", mockUserInfo)
		} else {
			userInfo, err := repository.NewAppAuth().AppAuthCheck(authToken, deviceId)
			if err != nil {
				slog.Error("app token check error", "error", err)
				c.<PERSON>(http.StatusUnauthorized, "auth check error")
				c.Abort()
				return
			}
			slog.Debug("app token check success", "userInfo", userInfo)

			c.Set("app_auth_info", userInfo)
			c.Set("office_id", userInfo.DeviceTypeID)
			c.Set("office_key", userInfo.OfficeKey)
		}

		c.Set("client_type", enum.ClientTypeApp)
		c.Next()
	}
}

func mockTestAuth(authToken, deviceID string) *model.AppAuthInfo {
	var appAuthInfo model.AppAuthInfo
	if authToken == enum.MockAuthToken && deviceID == enum.MockAuthToken {
		appAuthInfo.OfficeID = configs.GetMockConfig().OfficeId
		appAuthInfo.OfficeKey = configs.GetMockConfig().OfficeKey
		appAuthInfo.DeviceID = deviceID

		return &appAuthInfo
	}

	return nil
}
