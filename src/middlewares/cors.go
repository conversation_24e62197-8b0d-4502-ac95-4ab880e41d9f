package middlewares

import (
	"log/slog"
	"mi-restful-api/configs"
	"mi-restful-api/enum"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

func Cors() gin.HandlerFunc {
	return func(c *gin.Context) {
		method := c.Request.Method
		origin := c.Request.Header.Get("Origin")
		slog.Info("Header Origin", "origin", origin)

		domains := configs.GetAccessControlDomainsConfig().Domains
		if domains == enum.AccessControlDomainAllowAll || domains == enum.AccessControlDomainNoConf {
			c.<PERSON>er("Access-Control-Allow-Origin", "*")
		} else if isAllowed<PERSON><PERSON><PERSON>(origin) {
			c.<PERSON>("Access-Control-Allow-Origin", origin)
		} else {
			c.<PERSON><PERSON>("Access-Control-Allow-Origin", "null") // or omit this header
		}

		c.<PERSON>("Access-Control-Allow-Headers", "Content-Type,AccessToken,X-CSRF-Token, Authorization, x-request-id, token, device-id")
		c.<PERSON>("Access-Control-Allow-Methods", "POST, GET, DELETE, PUT, OPTIONS")
		c.<PERSON>("Access-Control-Expose-Headers", "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type")
		c.Header("Access-Control-Allow-Credentials", "true")

		if method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
		}
		c.Next()
	}
}

func isAllowedOrigin(origin string) bool {
	allowedDomains := strings.Split(configs.GetAccessControlDomainsConfig().Domains, ",")

	for _, domain := range allowedDomains {
		if strings.HasSuffix(origin, domain) {
			return true
		}
	}
	return false
}
