package middlewares

import (
	"bytes"

	"mi-restful-api/enum"
	"mi-restful-api/logging"
	"mi-restful-api/utils/common"

	"github.com/gin-gonic/gin"
)

type RespbodyLogWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w RespbodyLogWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

func ResponseLogMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		blw := &RespbodyLogWriter{body: bytes.NewBufferString(""), ResponseWriter: c.Writer}
		c.Writer = blw

		c.Next()

		officeId := c.GetString("office_id")
		officeKey := c.GetString("office_key")
		clientType := c.GetString("client_type")
		requestId := common.GetRequestId(c)

		logging.LogFormat(enum.LogInfo, map[string]any{
			"category":    enum.LogCategoryApp,
			"msg":         "Response log",
			"module":      "GinMiddleware",
			"office_id":   officeId,
			"office_key":  officeKey,
			"client_type": clientType,
			"request_id":  requestId,
			"param": map[string]any{
				"resp": map[string]any{
					"status": c.Writer.Status(),
					"body":   blw.body.String(),
				},
			},
		})
	}
}
