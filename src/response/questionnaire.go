package response

import (
	"mi-restful-api/model"
	"sort"
	"time"
)

type QuestionnaireIndexResp struct {
	// message メッセージ
	Msg string `json:"message"`
	// code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー
	Code int `json:"code"`
	// data データ
	Data []QuestionnaireRespData `json:"data"`
	// current_page 現在ページ
	CurPage int `json:"current_page"`
	// from 開始件数
	From int `json:"from"`
	// to 終了件数
	To int `json:"to"`
	// last_page 全ページ数
	LastPage int `json:"last_page"`
	// per_page １ページ当たり件数
	PerPage int `json:"per_page"`
	// total 条件を満たす件数
	Total int `json:"total"`
}

func (svc *QuestionnaireIndexResp) Transfer(comments []model.Questionnaire) {
	for _, data := range comments {
		temp := questionnaireDTO(&data)

		svc.Data = append(svc.Data, temp)
	}

}

type QuestionnaireShowResp struct {
	// message メッセージ
	Msg string `json:"message"`
	// code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー
	Code int `json:"code"`
	// data データ
	Data *QuestionnaireRespData `json:"data"`
}

func (svc *QuestionnaireShowResp) Transfer(data model.Questionnaire) {
	resp := questionnaireDTO(&data)
	svc.Data = &resp
}

func questionnaireDTO(data *model.Questionnaire) QuestionnaireRespData {
	var temp QuestionnaireRespData
	temp.ID = data.ID
	temp.CartNo = data.CartNo
	temp.CaddyID = data.CaddyID
	temp.PlayedDate = data.PlayedDate
	temp.StartTime = data.StartTime
	temp.StartCourse = data.StartCourse
	temp.Survey = make([]Survey, 0) // init empty slice

	for _, player := range data.Players {
		var tempPlayer Survey
		tempPlayer.ID = player.PlayerId
		tempPlayer.Name = player.PlayerName
		tempPlayer.AnswerTime = player.CreatedAt
		tempPlayer.Answers = make([]Answer, 0) // init empty slice
		tempPlayer.Feedback = player.PlayerFeedback.Content
		tempPlayer.FeedbackCaddy = player.PlayerFeedback.ContentCaddy
		tempPlayer.FeedbackGolf = player.PlayerFeedback.ContentGolf

		for _, answer := range player.Answers {
			var tempAnswer Answer
			tempAnswer.ID = answer.QuestionId
			tempAnswer.Answer = answer.CommentId

			tempPlayer.Answers = append(tempPlayer.Answers, tempAnswer)
		}

		temp.Survey = append(temp.Survey, tempPlayer)
	}

	return temp
}

type QuestionnaireRespData struct {
	// id 主キーID
	ID int `json:"id"`
	// cart_no カート番号
	CartNo string `json:"cart_no"`
	// caddy_id キャディID
	CaddyID string `json:"caddy_id"`
	// played_date プレイ時間
	PlayedDate string `json:"played_date"`
	// start_Time スタート時間
	StartTime string `json:"start_time"`
	// start_course スタートコース
	StartCourse string `json:"start_course"`
	// survey 設問
	Survey []Survey `json:"survey"`
}

type Survey struct {
	// id プレイヤーID
	ID string `json:"id"`
	// name プレイヤー名
	Name string `json:"name"`
	// answer_time 回答時間
	AnswerTime time.Time `json:"answer_time"`
	// answers 回答
	Answers []Answer `json:"answers"`
	// feedback custom comment
	Feedback string `json:"feedback"`
	// キャディのfeedback custom comment
	FeedbackCaddy string `json:"feedback_caddy"`
	// ゴルフ場のfeedback custom comment
	FeedbackGolf string `json:"feedback_golf"`
}

type Answer struct {
	// id 設問ID
	ID int `json:"id"`
	// answer 回答
	Answer int `json:"answer"`
}

type QuestionnaireCreateResp struct {
	// message 简述
	Msg string `json:"message"`
	// code 简码 正常 0 ， 401 未授权， 500 内部错误
	Code int `json:"code"`
	// id 创建资源的id
	Id int `json:"id"`
}

type QuestionnaireUpdateResp struct {
	// message 简述
	Msg string `json:"message"`
	// code 简码 正常 0 ， 401 未授权， 500 内部错误
	Code int `json:"code"`
}

type QuestionnaireDelResp struct {
	// message 简述
	Msg string `json:"message"`
	// code 简码 正常 0 ， 401 未授权， 500 内部错误
	Code int `json:"code"`
}

type QuestionnaireExportCsv struct {
	// message 简述
	Msg string `json:"message"`
	// code 简码 正常 0 ， 401 未授权， 500 内部错误
	Code int `json:"code"`
}

type CartPlayer struct {
	// id プレイヤーID
	Id string `json:"id"`
	// name プレイヤー名
	Name string `json:"name"`
	// club_checked クラブ確認済み
	ClubChecked bool `json:"club_checked"`
}

type CartInfoData struct {
	// cart_no カート番号
	CartNo string `json:"cart_no"`
	// caddy_id キャディID
	CaddyID string `json:"caddy_id"`
	// caddy_id キャディName
	CaddyName string `json:"caddy_name"`
	// played_date プレイ時間
	PlayedDate string `json:"played_date"`
	// caddy_name スタート時間
	StartTime string `json:"start_time"`
	// start_course スタートコース
	StartCourse string `json:"start_course"`
	// player プレイヤー
	CartPlayer []CartPlayer `json:"player"`
	// sort_key sk
	SortKey string `json:"sort_key"`
	// serial for version 2
	Serial string `json:"serial"`
	// exist_score exist score
	ExistScore   bool   `json:"exist_score"`
	UpdatedAt    string `json:"-"`
	TimeDuration string `json:"-"`
}
type CartInfoResp struct {
	// message 简述
	Msg string `json:"message"`
	// code 简码 正常 0 ， 401 未授权， 500 内部错误
	Code int `json:"code"`
	// data 数据
	Data []CartInfoData `json:"data"`
}

func (svc *CartInfoResp) Transfer(data []model.PlayerQuery) {
	filters := make(map[int][]model.PlayerQuery)
	for _, datum := range data {
		filters[datum.QuestionnaireId] = append(filters[datum.QuestionnaireId], datum)
	}

	for _, filter := range filters {
		var temp CartInfoData
		for _, query := range filter {
			temp.CartNo = query.CartNo
			temp.CaddyID = query.CaddyID
			temp.PlayedDate = query.PlayedDate
			temp.StartTime = query.StartTime
			temp.StartCourse = query.StartCourse
			temp.SortKey = query.SortKey

			var player CartPlayer
			player.Id = query.PlayerId
			player.Name = query.PlayerName
			player.ClubChecked = true
			temp.CartPlayer = append(temp.CartPlayer, player)
		}

		svc.Data = append(svc.Data, temp)
	}

}

func (svc *CartInfoResp) Combine(data map[string]CartInfoData, version string) {
	var tempData []CartInfoData
	for _, dynamoData := range data {
		if len(svc.Data) < 1 {
			tempData = append(tempData, dynamoData)
			continue
		}

		for _, dbData := range svc.Data {
			if version == "v2" { // ver2 use date and player ids
				if dbData.SortKey == dynamoData.Serial {
					dynamoData.CartPlayer = tidyCartPlayer(dynamoData.CartPlayer, dbData.CartPlayer)
					break
				}
			} else { // ver1 use score sort_key
				if dbData.SortKey == dynamoData.SortKey {
					dynamoData.CartPlayer = tidyCartPlayer(dynamoData.CartPlayer, dbData.CartPlayer)
					break
				}
			}

		}

		tempData = append(tempData, dynamoData)
	}
	// sorted data
	sort.Slice(tempData, func(i, j int) bool {
		return tempData[i].StartTime > tempData[j].StartTime
	})

	svc.Data = tempData
}

func tidyCartPlayer(dynamoPlayers, dbPlayers []CartPlayer) []CartPlayer {
	for idx, player := range dynamoPlayers {
		for _, dbPlayer := range dbPlayers {
			if player.Id == dbPlayer.Id {
				dynamoPlayers[idx].ClubChecked = dbPlayer.ClubChecked
				break
			}
		}
	}

	return dynamoPlayers
}
