package tee

import (
	"mi-restful-api/model/tee"
)

type TeeSheetResp struct {
	Code int             `json:"code"`
	Msg  string          `json:"msg"`
	Data []TeeCourseData `json:"data"`
}

type TeeCourseData struct {
	CourseIndex string        `json:"course_index"`
	CourseName  string        `json:"course_name"`
	CartData    []TeeCartData `json:"cart_data"`
}

type TeePlayerData struct {
	PlayerNo     int     `json:"player_no"`
	PlayerName   string  `json:"player_name"`
	Gender       *int    `json:"gender"`
	Birthday     *string `json:"birthday"`
	GlidNo       string  `json:"glid_no"`
	TeeId        *string `json:"tee_id"`
	HdcpIndex    *string `json:"hdcp_index"`
	Hdcp         *string `json:"hdcp"`
	LockerNo     *int    `json:"locker_no"`
	JoinedCompes []struct {
		CompeNo int `json:"compe_no"`
	} `json:"joined_compes"`
}

type TeeCartData struct {
	CartNo             int             `json:"cart_no"`
	StartTime          string          `json:"start_time"`
	ScheduledStartTime string          `json:"scheduled_start_time"`
	DelegateCompe      *DelegateCompe  `json:"delegate_compe"`
	Players            []TeePlayerData `json:"players"`
}

type DelegateCompe struct {
	CompeName     string `json:"compe_name"`
	CompeNo       int    `json:"compe_no"`
	IsFrontSystem bool   `json:"is_front_system"`
}

type TeeInfoResp struct {
	Code int           `json:"code"`
	Msg  string        `json:"msg"`
	Data []tee.TeeInfo `json:"data"`
}

type TeePlayerResp struct {
	Code int             `json:"code"`
	Msg  string          `json:"msg"`
	Data []tee.TeePlayer `json:"data"`
}
