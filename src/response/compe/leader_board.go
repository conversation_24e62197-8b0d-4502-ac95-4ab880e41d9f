package compe

import "mi-restful-api/model/compe"

type LeaderBoardRankingResp struct {
	Code int    `json:"code"`
	Msg  string `json:"message"`
	Data struct {
		compe.RankingDetails
		UpdatedAt string `json:"updated_at"`
	} `json:"data"`
}

type LeaderBoardRankingTypeResp struct {
	Code int    `json:"code"`
	Msg  string `json:"message"`
	Data struct {
		RankingType []string `json:"ranking_type"`
	} `json:"data"`
}

type LeaderboardRankingShareKeyCreateResp struct {
	Code int    `json:"code"`
	Msg  string `json:"message"`
	Data struct {
		ShareKey string `json:"share_key"`
	} `json:"data"`
}
