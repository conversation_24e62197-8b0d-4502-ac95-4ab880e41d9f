package compe

import "mi-restful-api/model/compe"

/*
{
  "code": 200,
  "message": "Success",
  "data": {
    "pairing": [
      {
        "course_name": "Course 1",
        "pair": {
          "1": {
            "cart_no": 1,
            "start_time": "08:00",
            "player": [
              {
                "player_no": 1,
                "player_name": "Player 1",
                "hdcp_index": "10.5",
                "hdcp": "10.5",
                "score": 80,
                "total": 80
              },
              {
                "player_no": 2,
                "player_name": "Player 2",
                "hdcp_index": "12.5",
                "hdcp": "12.5",
                "score": 85,
                "total": 85
              }
            ]
          }
        }
      }
    ]
  }
}
*/

type PairingResp struct {
	Code int    `json:"code"`
	Msg  string `json:"message"`
	Data struct {
		Pairing []compe.Pairing `json:"Pairing"`
	} `json:"data"`
}
