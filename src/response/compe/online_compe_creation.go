package compe

type BasicResp struct {
	// message メッセージ
	Msg string `json:"message"`
	// code コード 正常 0 ， 401 認証エラー 500 内部エラー
	Code int `json:"code"`
}

type UploadImgResp struct {
	Code int    `json:"code"`
	Msg  string `json:"message"`
	Data struct {
		Url string `json:"url"`
	} `json:"data"`
}

type OnlineCompeLatestNoResp struct {
	// message メッセージ
	Msg string `json:"message"`
	// code コード 正常 0 ， 401 認証エラー 500 内部エラー
	Code int `json:"code"`

	Data struct {
		CompeNo int `json:"compe_no"` //コンペNo
	} `json:"data"`
}
