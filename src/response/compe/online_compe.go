package compe

import "mi-restful-api/model/compe"

type OnlineCompeResp struct {
	Code int    `json:"code"`
	Msg  string `json:"message"`
	Data struct {
		compe.Basic            `json:"basic"`
		compe.CompeSetting     `json:"compe_setting"`
		compe.CompeTypeSetting `json:"compe_type_setting"`
		compe.OtherSetting     `json:"other_setting"`
		compe.PrivateSetting   `json:"private_setting"`
	} `json:"data"`
}
