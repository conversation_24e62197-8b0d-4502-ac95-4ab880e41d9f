package compe

import (
	"time"
)

type OnlineCompeOfficeResp struct {
	Code int    `json:"code"`
	Msg  string `json:"message"`
	Data struct {
		Compes []MergedCompe `json:"compes"`
	} `json:"data"`
}

type MergedCompe struct {
	CompeNo   int    `json:"compe_no"`
	CompeName string `json:"compe_name"`
	Duration  *struct {
		From time.Time `json:"from"` // 開催日 >= From
		To   time.Time `json:"to"`   // 開催日 <= To
	} `json:"duration"`
	CompeType            *int     `json:"compe_type"`             // 0 team (only for phase2) 1 個人戦
	JoinedPlayersCount   int      `json:"joined_players_count"`   //参加人数
	AggregationTypes     []string `json:"aggregation_types"`      //競技方法list
	PrizeConditionSetted bool     `json:"prize_condition_setted"` // 入賞条件設定済み
	HiddenHoleSetted     bool     `json:"hidden_hole_setted"`     // 隠しホール設定済み
	SharedKey            *string  `json:"shared_key"`             // 共有キー
	IsFrontSystem        bool     `json:"is_front_system"`        // is フロントシステム

	ParticipationFee *int `json:"participation_fee"` // 参加料金 円

}
