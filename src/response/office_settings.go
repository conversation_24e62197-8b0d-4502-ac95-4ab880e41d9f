package response

import (
	"mi-restful-api/model"
)

type OfficeSettingsIndexResp struct {
	// message メッセージ
	Msg string `json:"message"`
	// code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー
	Code int `json:"code"`

	// data データ
	Data struct {
		// セルフスコア印刷機能　0:無効　1:有効
		EnableSelfScorePrint   *int     `json:"enable_self_score_print"`
		// アンケート機能　0:無効　1:有効
		EnableQuestionnaire    *int     `json:"enable_questionnaire"`
		// スタート案内機能　0:無効　1:有効
		EnableStartGuide       *int     `json:"enable_start_guide"`
		// カードリード種類　0:なし　1:バーコードリード　2:ICカードリード　
		CardReaderType         *int     `json:"card_reader_type"`
	} `json:"data"`
}

type OfficeSettingsCreateResp struct {
	// message 简述
	Msg string `json:"message"`
	// code 简码 正常 0 ， 401 未授权， 500 内部错误
	Code int `json:"code"`
}

func (svc *OfficeSettingsIndexResp) Transfer(data model.OfficeSettings) {
	if data.ID == 0 {
		zero := 0
		svc.Data.EnableSelfScorePrint = &zero
		svc.Data.EnableQuestionnaire = &zero
		svc.Data.EnableStartGuide = &zero
		svc.Data.CardReaderType = &zero
	} else {
		svc.Data.EnableSelfScorePrint = data.EnableSelfScorePrint
		svc.Data.EnableQuestionnaire = data.EnableQuestionnaire
		svc.Data.EnableStartGuide = data.EnableStartGuide
		svc.Data.CardReaderType = data.CardReaderType
	}
}

type BarcodeToLockerResp struct {
	// message メッセージ
	Msg string `json:"message"`
	// code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー
	Code int `json:"code"`

	// data データ
	Data struct {
		// 変換後のロッカーキー
		LockerNo     string     `json:"locker_no"`
	} `json:"data"`
}

func (svc *BarcodeToLockerResp) Transfer(data model.OfficeSettings, barcode string) {
	svc.Data.LockerNo = string(barcode[3:7])
}