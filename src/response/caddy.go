package response

import (
	"mi-restful-api/enum"
	"mi-restful-api/model"
	"strings"
)

type CaddyData struct {
	// id ID
	Id int `json:"id"`
	// caddy_no キャディ番号
	CaddyNo string `json:"caddy_no"`
	// caddy_name キャディ名
	CaddyName string `json:"caddy_name"`
}
type CaddyIndexResp struct {
	// code  コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー
	Code int `json:"code"`
	// message メッセージ
	Message string `json:"message"`
	// data データ
	Data []CaddyData `json:"data"`
}

func (svc *CaddyIndexResp) Transfer(data []model.Caddy) {
	for _, datum := range data {
		var temp CaddyData
		temp.Id = datum.ID
		temp.CaddyNo = datum.CaddyId
		temp.CaddyName = datum.CaddyName

		svc.Data = append(svc.Data, temp)
	}
}

type CaddyOnResp struct {
	// code  コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー
	Code int `json:"code"`
	// message メッセージ
	Message string `json:"message"`
	// caddyon データ
	CaddyOn string `json:"caddyon"`
	// printon データ
	PrintOn string `json:"printon"`
}

func (svc *CaddyOnResp) Transfer(restrictionTags string) {
	svc.CaddyOn = enum.CaddySwitchOff
	svc.PrintOn = enum.CaddyScorePrintOff

	tags := strings.Split(restrictionTags, ",")
	for _, tag := range tags {
		tag = strings.Trim(tag, " ")
		if tag == enum.CaddyOnTagName {
			svc.CaddyOn = enum.CaddySwitchOn
			break
		}
	}

	for _, tag := range tags {
		tag = strings.Trim(tag, " ")
		if tag == enum.CaddyScorePrint {
			svc.PrintOn = enum.CaddyScorePrintOn
			break
		}
	}
}
