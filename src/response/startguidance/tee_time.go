package startguidance

import "mi-restful-api/model/startguidance"

type PlayerTeeResp struct {
	Code int                          `json:"code"`
	Msg  string                       `json:"msg"`
	Data *startguidance.PlayerTeeTime `json:"data"`
}

type TeeTimeResp struct {
	Code int                         `json:"code"`
	Msg  string                      `json:"msg"`
	Data *startguidance.TeeTimesData `json:"data"`
}

type CartInfoResp struct {
	Msg  string                        `json:"message"`
	Code int                           `json:"code"`
	Data *startguidance.LockerCartInfo `json:"data"`
}
