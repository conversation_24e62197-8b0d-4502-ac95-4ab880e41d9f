package response

import (
	"mi-restful-api/model"
)

type EvaluationIndexResp struct {
	// message メッセージ
	Msg string `json:"message"`
	// code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー
	Code int `json:"code"`
	// data データ
	Data []EvaluationRespData `json:"data"`
}

func (svc *EvaluationIndexResp) Transfer(evaluations []model.Evaluation) {
	for _, data := range evaluations {
		var temp EvaluationRespData
		temp.Content = data.Content
		temp.Score = data.Score
		temp.Stage = data.Stage
		temp.ID = data.ID

		svc.Data = append(svc.Data, temp)
	}
}

type EvaluationShowResp struct {
	// message メッセージ
	Msg string `json:"message"`
	// code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー
	Code int `json:"code"`
	// data データ
	Data *EvaluationRespData `json:"data"`
}

func (svc *EvaluationShowResp) Transfer(evaluation model.Evaluation) {
	var temp EvaluationRespData
	temp.Content = evaluation.Content
	temp.Score = evaluation.Score
	temp.Stage = evaluation.Stage
	temp.ID = evaluation.ID

	svc.Data = &temp
}

type EvaluationRespData struct {
	// id 評価段階
	ID int `json:"id"`
	// score 評価配点
	Score int `json:"score"`
	// stage だんかい
	Stage int `json:"stage"`
	// content 評価文言
	Content string `json:"content"`
}

type EvaluationCreateResp struct {
	// message メッセージ
	Msg string `json:"message"`
	// code コード 正常 0 ， 401 認証エラー 500 内部エラー
	Code int `json:"code"`
	// id リソースid
	Id int `json:"id"`
}

type EvaluationUpdateResp struct {
	// message メッセージ
	Msg string `json:"message"`
	// code コード 正常 0 ， 401 認証エラー 500 内部エラー
	Code int `json:"code"`
	// data データ
	Data []EvaluationRespData `json:"data"`
}

func (svc *EvaluationUpdateResp) Transfer(evaluations []model.Evaluation) {
	var result []EvaluationRespData
	for _, evaluation := range evaluations {
		var temp EvaluationRespData
		temp.Content = evaluation.Content
		temp.Score = evaluation.Score
		temp.Stage = evaluation.Stage
		temp.ID = evaluation.ID

		result = append(result, temp)
	}

	svc.Data = result
}

type EvaluationDelResp struct {
	// message メッセージ
	Msg string `json:"message"`
	// code コード 正常 0 ， 401 認証エラー 500 内部エラー
	Code int `json:"code"`
}
