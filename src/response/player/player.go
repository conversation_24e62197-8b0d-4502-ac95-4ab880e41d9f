package player

import (
	"mi-restful-api/model/compe"
	"mi-restful-api/model/player"
)

type PlayerInfoResp struct {
	Code int                 `json:"code"`
	Msg  string              `json:"msg"`
	Data []player.PlayerInfo `json:"data"`
}

type JoinedPlayesResp struct {
	Code int                   `json:"code"`
	Msg  string                `json:"msg"`
	Data []player.JoinedPlayer `json:"data"`
}

type GolferSearchResp struct {
	Result []GolferSearchResult `json:"result"`
}

type CourseRatingSearchResp struct {
	Result []CourseRatingResult `json:"result"`
}

type CourseRatingResult struct {
	RawCourseRatingID int    `json:"raw_course_rating_id"`
	Prefecture        string `json:"prefecture"`
	ClubID            string `json:"club_id"`
	ClubName          string `json:"club_name"`
	OutCourseID       string `json:"out_course_id"`
	OutCourseName     string `json:"out_course_name"`
	OutGreenID        string `json:"out_green_id"`
	OutGreenName      string `json:"out_green_name"`
	OutTeeID          string `json:"out_tee_id"`
	OutMenTee         string `json:"out_men_tee"`
	OutWomenTee       string `json:"out_women_tee"`
	InCourseID        string `json:"in_course_id"`
	InCourseName      string `json:"in_course_name"`
	InGreenID         string `json:"in_green_id"`
	InGreenName       string `json:"in_green_name"`
	InTeeID           string `json:"in_tee_id"`
	InMenTee          string `json:"in_men_tee"`
	InWomenTee        string `json:"in_women_tee"`
	MenYardage        string `json:"men_yardage"`       // Assuming string, convert to int/float if numeric
	MenCourseRating   string `json:"men_course_rating"` // Assuming string, convert to float if numeric
	MenSlopeRating    string `json:"men_slope_rating"`  // Assuming string, convert to int/float if numeric
	MenPar            string `json:"men_par"`
	WomenYardage      string `json:"women_yardage"`       // Assuming string, convert to int/float if numeric
	WomenCourseRating string `json:"women_course_rating"` // Assuming string, convert to float if numeric
	WomenSlopeRating  string `json:"women_slope_rating"`  // Assuming string, convert to int/float if numeric
	WomenPar          string `json:"women_par"`
	StartsAt          string `json:"starts_at"` // Consider using time.Time if you need to parse it
	EndsAt            string `json:"ends_at"`   // Consider using time.Time if you need to parse it
	ClubIDInteger     int    `json:"club_id_integer"`
}

type GolferSearchResult struct {
	UserIdToken            string `json:"user_id_token"`
	GlidNo                 string `json:"glid_no"`
	PlayerName             string `json:"player_name"`
	PlayerBirthday         string `json:"player_birthday"`
	PlayerSex              string `json:"player_sex"`
	HdcpIndex              string `json:"hdcp_index"`
	HdcpIndexDay           string `json:"hdcp_index_day"`
	HdcpIndexRequiredCount string `json:"hdcp_index_required_count"`
	GolferType             string `json:"golfer_type"`
	TechnoIdStatus         string `json:"techno_id_status"`
	ScoreRequest           string `json:"score_request"`
}

type GetGolferDataResp struct {
	Result struct {
		GlidNo           string `json:"glid_no"`
		Name             string `json:"name"`
		NameKana         string `json:"name_kana"`
		NameEn           string `json:"name_en"`
		Birthday         string `json:"birthday"`
		Gender           int    `json:"gender"`
		ZipCode          string `json:"zip_code"`
		Prefecture       string `json:"prefecture"`
		Address1         string `json:"address1"`
		Address2         string `json:"address2"`
		Tel              string `json:"tel"`
		Mobile           string `json:"mobile"`
		HomeClubId       string `json:"home_club_id"`
		HomeClubName     string `json:"home_club_name"`
		HdcpIndex        string `json:"hdcp_index"`
		HdcpIndexUpdated string `json:"hdcp_index_updated"`
		LowHdcpIndex     string `json:"low_hdcp_index"`
		BestHdcpIndex    string `json:"best_hdcp_index"`
		TargetHdcpIndex  string `json:"target_hdcp_index"`
	} `json:"result"`
}

type PlayerCompeResp struct {
	Code int           `json:"code"`
	Msg  string        `json:"msg"`
	Data []PlayerCompe `json:"data"` // kv , key is compe no value is joined player
}

type PlayerCompe struct {
	CompeNo          int                    `json:"compe_no"`
	CompeName        string                 `json:"compe_name"`
	CompeBasic       compe.Basic            `json:"compe_basic"`
	CompeSetting     compe.CompeSetting     `json:"compe_setting"`
	CompeTypeSetting compe.CompeTypeSetting `json:"compe_type_setting"`
	OtherSetting     compe.OtherSetting     `json:"other_setting"`
	Player           player.JoinedPlayer    `json:"player"`
}
