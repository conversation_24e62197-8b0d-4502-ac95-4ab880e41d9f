package response

import "mi-restful-api/model"

type Evaluation struct {
	// id 評価ID
	Id int `json:"id"`
	// count 回答数
	Count int `json:"count"`
}
type AnswerSurvey struct {
	// id 設問ID
	Id int `json:"id"`
	// answers  評価
	Answers []Evaluation `json:"answers"`
}
type CaddySurvey struct {
	// caddy_id キャディID
	CaddyId string `json:"caddy_id"`
	// survey 設問
	AnswerSurvey []AnswerSurvey `json:"survey"`
}

type CaddyAnalysisData struct {
	// month 年月
	Month string `json:"month"`
	// survey キャディ
	Survey []CaddySurvey `json:"survey"`
}

type CaddyAnalysisResp struct {
	// data データ
	Data []CaddyAnalysisData `json:"data"`
	// code  コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー
	Code int `json:"code"`
	// message メッセージ
	Message string `json:"message"`
}

func (svc *CaddyAnalysisResp) Transfer(queries []model.CaddyAnalysisQuery) {
	monthMap := make(map[string]map[string]map[int][]Evaluation)
	for _, query := range queries {
		if _, ok := monthMap[query.Month]; !ok {
			monthMap[query.Month] = make(map[string]map[int][]Evaluation)
		}
		if _, ok := monthMap[query.Month][query.CaddyId]; !ok {
			monthMap[query.Month][query.CaddyId] = make(map[int][]Evaluation)
		}
		monthMap[query.Month][query.CaddyId][query.QuestionId] = append(
			monthMap[query.Month][query.CaddyId][query.QuestionId],
			Evaluation{Id: query.CommentId, Count: query.Count},
		)
	}

	var caddyAnalysisDataList []CaddyAnalysisData
	for month, caddies := range monthMap {
		var caddySurveys []CaddySurvey
		for caddyId, questions := range caddies {
			var answerSurveys []AnswerSurvey
			for questionId, evaluations := range questions {
				answerSurveys = append(answerSurveys, AnswerSurvey{
					Id:      questionId,
					Answers: evaluations,
				})
			}
			caddySurveys = append(caddySurveys, CaddySurvey{
				CaddyId:      caddyId,
				AnswerSurvey: answerSurveys,
			})
		}
		caddyAnalysisDataList = append(caddyAnalysisDataList, CaddyAnalysisData{
			Month:  month,
			Survey: caddySurveys,
		})
	}

	svc.Data = caddyAnalysisDataList
}

type StatisticalData struct {
	// month 年月
	Month string `json:"month"`
	// survey キャディ
	AnswerSurvey []AnswerSurvey `json:"survey"`
}

type StatisticalResp struct {
	// data データ
	Data []StatisticalData `json:"data"`
	// code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー
	Code int `json:"code"`
	// message メッセージ
	Message string `json:"message"`
}

func (svc *StatisticalResp) Transfer(queries []model.StatisticalQuery) {
	monthMap := make(map[string]map[int][]Evaluation)
	for _, query := range queries {
		if _, ok := monthMap[query.Month]; !ok {
			monthMap[query.Month] = make(map[int][]Evaluation)
		}
		monthMap[query.Month][query.QuestionId] = append(
			monthMap[query.Month][query.QuestionId],
			Evaluation{Id: query.CommentId, Count: query.Count},
		)
	}

	var caddyAnalysisDataList []StatisticalData
	for month, questions := range monthMap {
		var answerSurveys []AnswerSurvey
		for questionId, evaluations := range questions {
			answerSurveys = append(answerSurveys, AnswerSurvey{
				Id:      questionId,
				Answers: evaluations,
			})
		}
		caddyAnalysisDataList = append(caddyAnalysisDataList, StatisticalData{
			Month:        month,
			AnswerSurvey: answerSurveys,
		})
	}

	svc.Data = caddyAnalysisDataList
}

type AnswerLowestData struct {
	// id 評価ID
	QuestionId int `json:"question_id"`
	// Score 回答数
	Score int `json:"score"`
	// evaluation id
	EvaluationId int `json:"evaluation_id"`
}

type AnswerLowestResp struct {
	// data データ
	Data []AnswerLowestData `json:"data"`
	// code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー
	Code int `json:"code"`
	// message メッセージ
	Message string `json:"message"`
}

func (svc *AnswerLowestResp) Transfer(queries []model.AnswerLowestQuery) {
	var tempData []AnswerLowestData
	for _, query := range queries {
		var temp AnswerLowestData
		temp.EvaluationId = query.CommentId
		temp.Score = query.Score
		temp.QuestionId = query.QuestionId

		tempData = append(tempData, temp)
	}
	svc.Data = tempData
}
