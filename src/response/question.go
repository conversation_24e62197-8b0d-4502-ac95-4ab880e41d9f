package response

import (
	"mi-restful-api/model"
)

type QuestionIndexResp struct {
	// message メッセージ
	Msg string `json:"message"`
	// code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー
	Code int `json:"code"`
	// data 数据
	Data []QuestionRespData `json:"data"`
}

func (svc *QuestionIndexResp) Transfer(questions []model.Question) {
	for _, data := range questions {
		var temp QuestionRespData
		temp.Type = data.Type
		temp.Content = data.Content
		temp.Index = int(data.Index)
		temp.ID = data.ID
		temp.Require = data.Require

		svc.Data = append(svc.Data, temp)
	}
}

type QuestionShowResp struct {
	// message メッセージ
	Msg string `json:"message"`
	// code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー
	Code int `json:"code"`
	// data データ
	Data *QuestionRespData `json:"data"`
}

func (svc *QuestionShowResp) Transfer(data model.Question) {
	var temp QuestionRespData
	temp.ID = data.ID
	temp.Type = data.Type
	temp.Content = data.Content
	temp.Require = data.Require
	temp.Index = int(data.Index)

	svc.Data = &temp
}

type QuestionRespData struct {
	// id 設問ID
	ID int `json:"id"`
	// index 設問順番
	Index int `json:"index"`
	// type 設問タイプ
	Type int8 `json:"type"`
	// content 設問内容
	Content string `json:"content"`
	// require 必須
	Require int8 `json:"require"`
}

type QuestionCreateResp struct {
	// message メッセージ
	Msg string `json:"message"`
	// code コード 正常 0 ， 401 認証エラー 500 内部エラー
	Code int `json:"code"`
	// id リソースid
	Id int `json:"id"`
}

type QuestionUpdateResp struct {
	// message メッセージ
	Msg string `json:"message"`
	// code コード 正常 0 ， 401 認証エラー， 500 内部エラー
	Code int `json:"code"`
}

type QuestionDelResp struct {
	// message メッセージ
	Msg string `json:"message"`
	// code コード 正常 0 ， 401 認証エラー， 500 内部エラー
	Code int `json:"code"`
}
