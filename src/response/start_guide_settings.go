package response

import (
	"mi-restful-api/model"
)

type StartGuideSettingsIndexResp struct {
	// message メッセージ
	Msg string `json:"message"`
	// code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー
	Code int `json:"code"`

	// data データ
	Data struct {
		// スタート予定時間表示
		EnableStartTime   *int        `json:"enable_start_time"`
		// メイン文言（常時表示）
		MainTextAlways    *string     `json:"main_text_always"`
		// サブ文言（常時表示）
		SubTextAlways     *string     `json:"sub_text_always"`
		// 自動スタート案内表示
		EnableAutoStart   *int        `json:"enable_autostart"`
		// 自動スタート案内基準
		AutoStartType     *int        `json:"autostart_type"`
		// スタート予定時間
		StartTimeSchedule *int        `json:"start_time_schedule"`
		// スタートまでの順番
		StartNumber       *int        `json:"start_number"`
		// サブ文言（自動案内）
		SubTextAuto       *string     `json:"sub_text_auto"`
	} `json:"data"`
}

type StartGuideSettingsCreateResp struct {
	// message 简述
	Msg string `json:"message"`
	// code 简码 正常 0 ， 401 未授权， 500 内部错误
	Code int `json:"code"`
}

type StartGuideInfoIndexResp struct {
	// message メッセージ
	Msg string `json:"message"`
	// code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー
	Code int `json:"code"`

	// data データ
	Data struct {
		// メイン文言（常時表示）
		MainTextAlways    string     `json:"main_text_always"`
		// サブ文言（常時表示）
		SubTextAlways     string     `json:"sub_text_always"`
		// メイン文言（自動案内）
		MainTextAuto    string     `json:"main_text_auto"`
		// サブ文言（自動案内）
		SubTextAuto       string     `json:"sub_text_auto"`
	} `json:"data"`
}

func (svc *StartGuideSettingsIndexResp) Transfer(data model.StartGuideSettings) {
	if data.ID == 0 {
		zero := 0
		one := 1
		emptystr := ""
		svc.Data.EnableStartTime = &zero
		svc.Data.MainTextAlways = &emptystr
		svc.Data.SubTextAlways = &emptystr
		svc.Data.EnableAutoStart = &zero
		svc.Data.AutoStartType = &zero
		svc.Data.StartTimeSchedule = &one
		svc.Data.StartNumber = &one
		svc.Data.SubTextAuto = &emptystr
	} else {
		svc.Data.EnableStartTime = data.EnableStartTime
		svc.Data.MainTextAlways = data.MainTextAlways
		svc.Data.SubTextAlways = data.SubTextAlways
		svc.Data.EnableAutoStart = data.EnableAutoStart
		svc.Data.AutoStartType = data.AutoStartType
		svc.Data.StartTimeSchedule = data.StartTimeSchedule
		svc.Data.StartNumber = data.StartNumber
		svc.Data.SubTextAuto = data.SubTextAuto
	}
}

func (svc *StartGuideInfoIndexResp) Transfer(data model.StartGuideSettings) {
	if data.ID != 0 && *data.EnableStartTime == 1 {
		svc.Data.MainTextAlways = *data.MainTextAlways
		svc.Data.SubTextAlways = *data.SubTextAlways
		svc.Data.MainTextAuto = "カート番号111、222、333の皆様は、スタートホールへご移動ください"
		svc.Data.SubTextAuto = *data.SubTextAuto
	}
}