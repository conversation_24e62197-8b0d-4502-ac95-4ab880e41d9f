package response

type QuesnaireSettingsIndexResp struct {
	// message メッセージ
	Msg string `json:"message"`
	// code コード 正常 0 ， 401 認証エラー ，404 not found ， 500 内部エラー
	Code int `json:"code"`

	// data データ
	Data struct {
		CaddyNameType int `json:"caddy_name_type"` // キャディの名前種類：1(キャディ)、2(コースアテンダント)
	} `json:"data"`
}

type QuesnaireSettingsCreateResp struct {
	// message 简述
	Msg string `json:"message"`
	// code 简码 正常 0 ， 401 未授权， 500 内部错误
	Code int `json:"code"`
}

