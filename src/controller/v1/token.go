package v1

import (
	"mi-restful-api/configs"
	"mi-restful-api/enum"
	"mi-restful-api/logging"
	"mi-restful-api/request"
	"mi-restful-api/response"
	"mi-restful-api/utils"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

type Token struct {
}

func (rec *Token) Auth(c *gin.Context) {
	var req request.AuthReq
	var resp response.AuthToken

	if err := c.ShouldBindBodyWithJSON(&req); err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "authorization auth deal params parse error",
			"module":   "AuthorizationAuth",
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
				},
			},
		})

		c.<PERSON>(http.StatusBadRequest, gin.H{
			"error": err.<PERSON><PERSON>(),
		})
		return
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category": enum.LogCategoryApp,
		"message":  "authorization auth access log",
		"module":   "AuthorizationAuth",
		"param": map[string]any{
			"req": map[string]any{
				"method": "post",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
	})

	authToken, err := utils.GenerateAuthToken(req.SessionId, req.SessionData, req.UserAgent, req.Timestamps)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryRedis,
			"message":  "authorization auth register session info to redis error",
			"module":   "AuthorizationAuth",
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
					"data":   req,
				},
			},
		})

		c.JSON(http.StatusForbidden, gin.H{
			"error": err.Error(),
		})
		return
	}
	resp.Token = authToken
	resp.ExpiredAt = time.Now().Add(time.Second * 60)
	c.JSON(200, resp)
}

func (rec *Token) AccessToken(c *gin.Context) {
	var req request.AccessReq
	var resp response.Token
	userAgent := c.GetHeader("User-Agent")
	if err := c.ShouldBindBodyWithJSON(&req); err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "authorization auth token change access token,params parse error",
			"module":   "AuthorizationAccessToken",
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
				},
			},
		})

		c.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category": enum.LogCategoryApp,
		"message":  "authorization auth token change access token access log",
		"module":   "AuthorizationAccessToken",
		"param": map[string]any{
			"req": map[string]any{
				"method": "post",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
	})

	// mock auth token
	mockConfig := configs.GetMockConfig()
	if mockConfig.OfficeKey != "" && req.AuthToken == enum.MockAuthToken {
		resp.AccessToken = enum.MockAuthToken
		resp.RefreshToken = enum.MockAuthToken
		resp.Username = "mock_user_name"
		resp.OfficeId = mockConfig.OfficeId
		resp.OfficeKey = mockConfig.OfficeKey
		resp.OfficeName = "IPCテストコース"
		c.JSON(http.StatusOK, resp)
		return
	}

	jwtToken, err := utils.ParseToken(req.AuthToken)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "authorization auth token parse error",
			"module":   "AuthorizationAccessToken",
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
					"data":   req,
				},
			},
		})

		c.JSON(http.StatusUnauthorized, gin.H{
			"error": err.Error(),
		})
		return
	}

	if err = utils.ValidateAuthToken(jwtToken, userAgent); err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":   enum.LogCategoryApp,
			"message":    "authorization auth token security check error",
			"module":     "AuthorizationAccessToken",
			"err":        err,
			"user_agent": userAgent,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
					"data":   req,
				},
			},
		})

		c.JSON(http.StatusUnauthorized, gin.H{
			"error": err.Error(),
		})
		return
	}

	accessToken, refreshToken, sessionInfo, office, err := utils.GenerateAccessToken(req.AuthToken)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "authorization generate new token error",
			"module":   "AuthorizationAccessToken",
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
					"data":   req,
				},
			},
		})

		c.JSON(http.StatusUnauthorized, gin.H{
			"error": err.Error(),
		})
		return
	}

	resp.AccessToken = accessToken
	resp.RefreshToken = refreshToken
	resp.Username = sessionInfo.UserName
	resp.OfficeId = sessionInfo.OfficeId
	resp.OfficeKey = office.OfficeKey
	resp.OfficeName = office.OfficeName
	c.JSON(http.StatusOK, resp)
}

func (rec *Token) Refresh(c *gin.Context) {
	var req request.RefreshReq
	var resp response.Token

	if err := c.ShouldBindBodyWithJSON(&req); err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "authorization refresh token ,params parse error",
			"module":   "AuthorizationRefreshToken",
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
				},
			},
		})

		c.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category": enum.LogCategoryApp,
		"message":  "authorization refresh token access log",
		"module":   "AuthorizationRefreshToken",
		"param": map[string]any{
			"req": map[string]any{
				"method": "post",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
	})

	accessToken, refreshToken, err := utils.ExchangeToken(req.RefreshToken)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "authorization generate new token error",
			"module":   "AuthorizationRefreshToken",
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
					"data":   req,
				},
			},
		})

		c.JSON(http.StatusUnauthorized, gin.H{
			"error": err.Error(),
		})
		return
	}

	resp.AccessToken = accessToken
	resp.RefreshToken = refreshToken
	c.JSON(200, resp)
}

func (rec *Token) Logout(c *gin.Context) {
	var req request.LogoutReq
	var resp response.LogoutResp

	if err := c.ShouldBindBodyWithJSON(&req); err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "authorization logout ,params parse error",
			"module":   "AuthorizationLogout",
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
				},
			},
		})

		c.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category": enum.LogCategoryApp,
		"message":  "authorization logout access log",
		"module":   "AuthorizationLogout",
		"param": map[string]any{
			"req": map[string]any{
				"method": "post",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
	})

	err := utils.Logout(req.AccessToken, req.RefreshToken)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "authorization logout clean redis info error",
			"module":   "AuthorizationLogout",
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
					"data":   req,
				},
			},
		})

		c.JSON(http.StatusUnauthorized, gin.H{
			"error": err.Error(),
		})
		return
	}

	resp.Code = http.StatusOK
	resp.Msg = "ok"
	c.JSON(200, resp)
}
