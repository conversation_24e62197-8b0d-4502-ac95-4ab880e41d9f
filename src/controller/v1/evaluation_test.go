package v1

import (
	"bytes"
	"mi-restful-api/enum"
	"mi-restful-api/router"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestAppGetEvaluation(t *testing.T) {
	// 设置路由
	r := gin.Default()
	router.Api(r)
	// 创建模拟请求
	req := httptest.NewRequest("GET", "/app/evaluation", nil)
	req.Header.Set("token", enum.MockAuthToken)
	req.Header.Set("device-id", enum.MockAuthToken)
	w := httptest.NewRecorder()

	// call server
	r.ServeHTTP(w, req)

	// 测试到数据库链接错误
	assert.Equal(t, http.StatusInternalServerError, w.Code)
	assert.Equal(t, `{"message":"invalid db","code":601,"data":null}`, w.Body.String())
}

func TestWebGetEvaluation(t *testing.T) {
	// 设置路由
	r := gin.Default()
	router.Api(r)
	// 创建模拟请求
	req := httptest.NewRequest("GET", "/web/evaluation", nil)
	req.Header.Set("Authorization", enum.WebMockAuthToken)
	w := httptest.NewRecorder()

	// call server
	r.ServeHTTP(w, req)

	// 测试到数据库链接错误
	assert.Equal(t, http.StatusInternalServerError, w.Code)
	assert.Equal(t, `{"message":"invalid db","code":601,"data":null}`, w.Body.String())
}

func TestWebPutEvaluation(t *testing.T) {
	// 设置路由
	r := gin.Default()
	router.Api(r)
	// 创建模拟请求
	data := `[
		{
		"score":2,
		"stage":1,
		"content":"create and update 1"
		},
		{
		"score":20,
		"stage":2,
		"content":"create and update 2"
		},
		 {
		"score":30,
		"stage":3,
		"content":"create and update 3"
		},
		{
		"score":40,
		"stage":5,
		"content":"create and update 4"
		}
		,
		{
		"score":50,
		"stage":4,
		"content":"create and update 5"
		}
	]`
	buf := bytes.NewBuffer(nil)
	buf.WriteString(data)
	req := httptest.NewRequest("PUT", "/web/evaluation", buf)
	req.Header.Set("Authorization", enum.WebMockAuthToken)
	w := httptest.NewRecorder()

	// call server
	r.ServeHTTP(w, req)

	// 测试到数据库链接错误
	assert.Equal(t, http.StatusInternalServerError, w.Code)
	assert.Equal(t, `{"message":"invalid db","code":601,"data":null}`, w.Body.String())
}
