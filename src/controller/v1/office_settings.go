package v1

import (
	"mi-restful-api/client"
	"mi-restful-api/enum"
	"mi-restful-api/exception"
	"mi-restful-api/logging"
	"mi-restful-api/model"
	"mi-restful-api/repository"
	"mi-restful-api/request"
	"mi-restful-api/response"
	"mi-restful-api/utils/common"
	"net/http"

	"github.com/gin-gonic/gin"
)

type OfficeSettings struct {
	TableName string
}

// @Summary システム設定取得
// @Description システム設定取得
// @Produce  json
// @Success 200 {object} response.OfficeSettingsIndexResp "response"
// @Failure 400 {object} response.OfficeSettingsIndexResp "Bad Request ,param error"
// @Failure 401 {object} response.OfficeSettingsIndexResp "Unauthorized"
// @Failure 404 {object} response.OfficeSettingsIndexResp "Not Found"
// @Failure 500 {object} response.OfficeSettingsIndexResp "Internal Server Error"
// @Failure 601 {object} response.OfficeSettingsIndexResp "mysql conn error"
// @Failure 602 {object} response.OfficeSettingsIndexResp "mysql sql error"
// @Router /web/office/settings [get]
// @Router /app/office/settings [get]
// @Tags OfficeSettings
func (rec *OfficeSettings) Index(c *gin.Context) {
	requestId := common.GetRequestId(c)
	var resp response.OfficeSettingsIndexResp

	officeId, err := repository.GetOfficeId(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryAuth,
			"message":   "OfficeSettings index token parse msg error",
			"office_id":  officeId,
			"module":    "OfficeSettingsIndex",
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "OfficeSettings index access log",
		"module":    "OfficeSettingsIndex",
		"office_id":  officeId,
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "get",
				"path":   c.FullPath(),
			},
		},
	})

	db, err := client.GetDbClient()
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "OfficeSettings index get db client error",
			"module":    "OfficeSettingsIndex",
			"office_id":  officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Code = enum.DBConnErrorCode
		resp.Msg = err.Error()
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	var handle = repository.OfficeSettings{ConnMySQL: db}
	data, err := handle.IndexOfficeSettingsData(officeId)

	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "OfficeSettings index from rdb get data error",
			"module":    "OfficeSettingsIndex",
			"office_id":  officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = enum.DBSQLEXECErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	resp.Transfer(data)
	resp.Msg = "ok"
	resp.Code = 200

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "OfficeSettings index response log",
		"module":    "OfficeSettingsIndex",
		"office_id":  officeId,
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "get",
				"path":   c.FullPath(),
			},
		},
		"response": resp,
	})

	c.JSON(http.StatusOK, resp)
}

// @Summary システム設定保存
// @Description システム設定保存
// @ID post-officesettings-store
// @Accept  json
// @Produce  json
// @Param data body request.OfficeSettingsCreateReq true "Request payload"
// @Success 200 {object} response.OfficeSettingsCreateResp "response"
// @Failure 400 {object} response.OfficeSettingsCreateResp "Bad Request ,param error"
// @Failure 401 {object} response.OfficeSettingsCreateResp "Unauthorized"
// @Failure 404 {object} response.OfficeSettingsCreateResp "Not Found"
// @Failure 500 {object} response.OfficeSettingsCreateResp "Internal Server Error"
// @Failure 601 {object} response.OfficeSettingsCreateResp "mysql conn error"
// @Failure 602 {object} response.OfficeSettingsCreateResp "mysql sql error"
// @Router /web/office/settings [post]
// @Tags OfficeSettings
func (rec *OfficeSettings) Store(c *gin.Context) {
	var resp response.OfficeSettingsCreateResp
	requestId := common.GetRequestId(c)

	officeId, err := repository.GetOfficeId(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryAuth,
			"message":   "OfficeSettings store token parse msg error",
			"office_id":  officeId,
			"module":    "OfficeSettingsStore",
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	var req request.OfficeSettingsCreateReq
	if err := c.ShouldBindBodyWithJSON(&req); err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   "OfficeSettings store params parse error",
			"module":    "OfficeSettingsStore",
			"office_id":  officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
					"data":   req,
				},
			},
		})

		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "OfficeSettings store access log",
		"module":    "OfficeSettingsStore",
		"office_id":  officeId,
		"device-id": c.GetHeader("device-id"),
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "post",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
	})

	if audit := repository.NewOperateAudit(); audit.AuditOn == enum.OperateAuditON {
		logging.LogFormat(enum.LogInfo, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   "OfficeSettings store audit check open",
			"module":    "OfficeSettingsStore",
			"office_id":  officeId,
			"request":   requestId,
			"sync":      audit.AuditSyncOn,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
				},
			},
		})

		if audit.AuditSyncOn == enum.OperateAuditSyncOn {
			go audit.SaveOperate(model.OfficeSettings{}.TableName(), officeId, req, "")
		} else {
			audit.SaveOperate(model.OfficeSettings{}.TableName(), officeId, req, "")
		}
	}

	db, err := client.GetDbClient()
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "OfficeSettings store get db client error",
			"module":    "OfficeSettingsStore",
			"office_id":  officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = enum.DBConnErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	var handle = repository.OfficeSettings{ConnMySQL: db}
	err = handle.StoreOfficeSettingsData(req, officeId)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "OfficeSettings store to db error",
			"module":    "OfficeSettingsStore",
			"office_id":  officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
					"data":   req,
				},
			},
		})

		resp.Code = exception.StatusCode(err)
		resp.Msg = err.Error()
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	resp.Msg = "ok"
	resp.Code = 200

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "OfficeSettings store response log",
		"module":    "OfficeSettingsStore",
		"office_id":  officeId,
		"request":   requestId,
		"device-id": c.GetHeader("device-id"),
		"param": map[string]any{
			"req": map[string]any{
				"method": "post",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
		"response": resp,
	})

	c.JSON(http.StatusOK, resp)
}

// @Summary バーコードからロッカーキーに変換
// @Description バーコードからロッカーキーに変換
// @Produce  json
// @Success 200 {object} response.BarcodeToLockerResp "response"
// @Failure 400 {object} response.BarcodeToLockerResp "Bad Request ,param error"
// @Failure 401 {object} response.BarcodeToLockerResp "Unauthorized"
// @Failure 404 {object} response.BarcodeToLockerResp "Not Found"
// @Failure 500 {object} response.BarcodeToLockerResp "Internal Server Error"
// @Failure 601 {object} response.BarcodeToLockerResp "mysql conn error"
// @Failure 602 {object} response.BarcodeToLockerResp "mysql sql error"
// @Router /app/office/barcodetolocker [get]
// @Tags OfficeSettings
func (rec *OfficeSettings) BarcodeToLocker(c *gin.Context) {
	requestId := common.GetRequestId(c)
	var resp response.BarcodeToLockerResp

	officeId, err := repository.GetOfficeId(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryAuth,
			"message":   "OfficeSettings BarcodeToLocker token parse msg error",
			"office_id":  officeId,
			"module":    "OfficeSettingsBarcodeToLocker",
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	var req request.BarcodeToLockerReq
	if err := c.ShouldBind(&req); err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   "OfficeSettings BarcodeToLocker params parse error",
			"module":    "OfficeSettingsBarcodeToLocker",
			"office_id":  officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
					"data":   req,
				},
			},
		})

		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "OfficeSettings BarcodeToLocker access log",
		"module":    "OfficeSettingsBarcodeToLocker",
		"office_id":  officeId,
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "get",
				"path":   c.FullPath(),
			},
		},
	})

	db, err := client.GetDbClient()
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "OfficeSettings BarcodeToLocker get db client error",
			"module":    "OfficeSettingsBarcodeToLocker",
			"office_id":  officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Code = enum.DBConnErrorCode
		resp.Msg = err.Error()
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	var handle = repository.OfficeSettings{ConnMySQL: db}
	data, err := handle.IndexOfficeSettingsData(officeId)

	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "OfficeSettings BarcodeToLocker from rdb get data error",
			"module":    "OfficeSettingsBarcodeToLocker",
			"office_id":  officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = enum.DBSQLEXECErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	resp.Transfer(data, req.Barcode)
	resp.Msg = "ok"
	resp.Code = 200

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "OfficeSettings BarcodeToLocker response log",
		"module":    "OfficeSettingsBarcodeToLocker",
		"office_id":  officeId,
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "get",
				"path":   c.FullPath(),
			},
		},
		"response": resp,
	})

	c.JSON(http.StatusOK, resp)
}
