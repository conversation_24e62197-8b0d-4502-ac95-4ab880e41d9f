package v1

import (
	"mi-restful-api/client"
	"mi-restful-api/enum"
	"mi-restful-api/exception"
	"mi-restful-api/logging"
	"mi-restful-api/repository"
	"mi-restful-api/request"
	"mi-restful-api/response"
	"mi-restful-api/utils/common"
	"net/http"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type AnswerAnalysis struct {
}

// @Summary キャディ評価
// @Description キャディ評価
// @ID get-answer-caddy
// @Produce  json
// @Param start_month query string true "開始年月" default("2024-07")
// @Param end_month query string true "終了年月" default("2024-07")
// @Param caddy_id query int false "キャティID"
// @Param weekday query int true "曜日[ 1 平日， 2 周末， 3 未指定]"  default(3)
// @Success 200 {object} response.CaddyAnalysisResp "response"
// @Failure 400 {object} response.CaddyAnalysisResp "Bad Request ,param error"
// @Failure 401 {object} response.CaddyAnalysisResp "Unauthorized"
// @Failure 404 {object} response.CaddyAnalysisResp "Not Found"
// @Failure 500 {object} response.CaddyAnalysisResp "Internal Server Error"
// @Failure 601 {object} response.CaddyAnalysisResp "mysql conn error"
// @Failure 602 {object} response.CaddyAnalysisResp "mysql sql error"
// @Router /web/caddy [get]
// @Tags Analysis
func (svc *AnswerAnalysis) AnalysisByCaddy(c *gin.Context) {
	var resp response.CaddyAnalysisResp
	requestId := common.GetRequestId(c)

	officeId, err := repository.GetOfficeId(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryAuth,
			"message":   "answer analysis caddy token parse msg error",
			"office_id": officeId,
			"module":    "AnalysisCaddy",
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Message = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	var req request.CaddyAnalysisIndexReq
	if err := c.ShouldBind(&req); err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   "answer analysis caddy params parse error",
			"module":    "AnalysisCaddy",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Message = err.Error()
		resp.Code = http.StatusBadRequest
		c.JSON(resp.Code, resp)
		return
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "answer analysis caddy access log",
		"module":    "AnalysisCaddy",
		"office_id": officeId,
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "get",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
	})

	db, err := client.GetDbClient()
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "answer analysis caddy get db client error",
			"module":    "AnalysisCaddy",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Message = err.Error()
		resp.Code = enum.DBConnErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}
	handle := repository.AnswerAnalysis{ConnMySQL: db}
	result, err := handle.AnswerByCaddy(officeId, req)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "answer analysis caddy get answer info from db error",
			"module":    "AnalysisCaddy",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Message = err.Error()
		if err == gorm.ErrRecordNotFound {
			resp.Code = http.StatusNoContent
			c.JSON(http.StatusNoContent, resp)
			return
		} else {
			resp.Code = enum.DBSQLEXECErrorCode
			c.JSON(http.StatusInternalServerError, resp)
			return
		}
	}

	if len(result) < 1 {
		resp.Transfer(result)
		resp.Message = "no data"
		resp.Code = http.StatusNoContent
		c.JSON(http.StatusNoContent, resp)
		return
	}

	resp.Transfer(result)
	resp.Message = "ok"
	resp.Code = 200

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "answer analysis caddy response log",
		"module":    "AnalysisCaddy",
		"office_id": officeId,
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "get",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
		"response": resp,
	})

	c.JSON(http.StatusOK, resp)
}

// @Summary アンケート統計
// @Description アンケート統計
// @ID get-answer-statistical
// @Produce  json
// @Param start_month query string true "開始年月" default("2024-07")
// @Param end_month query string true "終了年月" default("2024-07")
// @Param caddy_id query int false "キャティID"
// @Param weekday query int true "曜日[ 1 平日， 2 周末， 3 未指定]"  default(3)
// @Success 200 {object} response.StatisticalResp "response"
// @Failure 400 {object} response.StatisticalResp "Bad Request ,param error"
// @Failure 401 {object} response.StatisticalResp "Unauthorized"
// @Failure 404 {object} response.StatisticalResp "Not Found"
// @Failure 500 {object} response.StatisticalResp "Internal Server Error"
// @Failure 601 {object} response.StatisticalResp "mysql conn error"
// @Failure 602 {object} response.StatisticalResp "mysql sql error"
// @Router /web/statistical [get]
// @Tags Analysis
func (svc *AnswerAnalysis) Statistical(c *gin.Context) {
	var resp response.StatisticalResp
	requestId := common.GetRequestId(c)

	officeId, err := repository.GetOfficeId(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryAuth,
			"message":   "answer analysis statistical token parse msg error",
			"office_id": officeId,
			"module":    "AnalysisStatistical",
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Message = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	var req request.StatisticalIndexReq
	if err := c.ShouldBind(&req); err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   "answer analysis statistical params parse error",
			"module":    "AnalysisStatistical",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Message = err.Error()
		resp.Code = http.StatusBadRequest
		c.JSON(resp.Code, resp)
		return
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "answer analysis statistical access log",
		"module":    "AnalysisStatistical",
		"office_id": officeId,
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "get",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
	})

	db, err := client.GetDbClient()
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "answer analysis statistical get db client error",
			"module":    "AnalysisStatistical",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Message = err.Error()
		resp.Code = enum.DBConnErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}
	handle := repository.AnswerAnalysis{
		ConnMySQL: db,
	}
	result, err := handle.AnswerStatistical(officeId, req)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "answer analysis statistical get answer info from db error",
			"module":    "AnalysisStatistical",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Message = err.Error()
		if err == gorm.ErrRecordNotFound {
			resp.Code = http.StatusNoContent
			c.JSON(http.StatusNoContent, resp)
			return
		} else {
			resp.Code = enum.DBSQLEXECErrorCode
			c.JSON(http.StatusInternalServerError, resp)
			return
		}
	}

	if len(result) < 1 {
		resp.Transfer(result)
		resp.Message = "no data"
		resp.Code = http.StatusNoContent
		c.JSON(http.StatusNoContent, resp)
		return
	}

	resp.Transfer(result)
	resp.Message = "ok"
	resp.Code = 200

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "answer analysis statistical response log",
		"module":    "AnalysisStatistical",
		"office_id": officeId,
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "get",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
		"response": resp,
	})

	c.JSON(http.StatusOK, resp)
}

// @Summary 統計問題の最低点
// @Description 統計問題の最低点
// @ID get-answer-lowest
// @Produce  json
// @Param start_month query string true "開始年月" default("2024-07")
// @Param end_month query string true "終了年月" default("2024-07")
// @Param caddy_id query int false "キャティID"
// @Success 200 {object} response.StatisticalResp "response"
// @Failure 400 {object} response.StatisticalResp "Bad Request ,param error"
// @Failure 401 {object} response.StatisticalResp "Unauthorized"
// @Failure 404 {object} response.StatisticalResp "Not Found"
// @Failure 500 {object} response.StatisticalResp "Internal Server Error"
// @Failure 601 {object} response.StatisticalResp "mysql conn error"
// @Failure 602 {object} response.StatisticalResp "mysql sql error"
// @Router /web/answer/lowest [get]
// @Tags Analysis
func (svc *AnswerAnalysis) AnswerLowest(c *gin.Context) {
	var resp response.AnswerLowestResp
	requestId := common.GetRequestId(c)

	officeId, err := repository.GetOfficeId(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryAuth,
			"message":   "answer analysis answer lowest token parse msg error",
			"office_id": officeId,
			"module":    "AnalysisAnswerLowest",
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Message = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	var req request.AnswerLowestReq
	if err := c.ShouldBind(&req); err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   "answer analysis answer lowest params parse error",
			"module":    "AnalysisAnswerLowest",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Message = err.Error()
		resp.Code = http.StatusBadRequest
		c.JSON(resp.Code, resp)
		return
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "answer analysis answer lowest  access log",
		"module":    "AnalysisAnswerLowest",
		"office_id": officeId,
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "get",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
	})

	db, err := client.GetDbClient()
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "answer analysis answer lowest get db client error",
			"module":    "AnalysisAnswerLowest",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Message = err.Error()
		resp.Code = enum.DBConnErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}
	handle := repository.AnswerLowest{
		ConnMySQL: db,
	}
	result, err := handle.AnswerLowest(officeId, req)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "answer analysis answer lowest get answer info from db error",
			"module":    "AnalysisAnswerLowest",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Message = err.Error()
		if err == gorm.ErrRecordNotFound {
			resp.Code = http.StatusNoContent
			c.JSON(http.StatusNoContent, resp)
			return
		} else {
			resp.Code = enum.DBSQLEXECErrorCode
			c.JSON(http.StatusInternalServerError, resp)
			return
		}
	}

	if len(result) < 1 {
		resp.Message = "no data"
		resp.Code = http.StatusNoContent
		c.JSON(http.StatusNoContent, resp)
		return
	}

	resp.Transfer(result)
	resp.Message = "ok"
	resp.Code = 200

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "answer analysis answer lowest  response log",
		"module":    "AnalysisAnswerLowest",
		"office_id": officeId,
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "get",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
		"response": resp,
	})

	c.JSON(http.StatusOK, resp)
}
