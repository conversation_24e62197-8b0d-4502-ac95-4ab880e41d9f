package v1

import (
	"fmt"
	"log/slog"
	"mi-restful-api/utils/dynamo"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"github.com/gin-gonic/gin"
)

type Score struct {
}

func (rec *Score) IndexScanV2(c *gin.Context) {
	start := time.Now()
	var count int32 = 0
	var evaluated map[string]types.AttributeValue
	for true {
		v2, err := getScanV2(c.Query("office_id"), evaluated)
		if err != nil {
			slog.Info(err.Error())
			c.JSON(500, gin.H{
				"msg":  "error",
				"info": err,
			})
			return
		}

		count = count + v2.Count

		if v2.LastEvaluatedKey == nil {
			slog.Info("end over")

			c.<PERSON>(200, gin.H{
				"msg":   "ok",
				"count": count,
				"data":  v2,
				"time":  fmt.Sprintf("Task took %s \r\n", time.Since(start)),
			})
			break
		}

		evaluated = v2.LastEvaluatedKey
	}

}

func getScanV2(officeId string, evaluated map[string]types.AttributeValue) (*dynamodb.QueryOutput, error) {
	input := &dynamodb.QueryInput{
		TableName:              aws.String("score"),
		KeyConditionExpression: aws.String("#office_id = :office_id"),
		ExpressionAttributeNames: map[string]string{
			"#office_id": "office_id",
		},
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":office_id": &types.AttributeValueMemberS{Value: officeId}, //
		},
		ScanIndexForward:  aws.Bool(true), // 昇順
		Select:            types.SelectCount,
		ExclusiveStartKey: evaluated,
	}

	return dynamo.Query(input)
}

func (rec *Score) IndexScan(c *gin.Context) {
	var evaluated map[string]types.AttributeValue
	var count int32 = 0
	start := time.Now()
	var page int32 = 0
	var pageSize int32 = 20
	for true {
		data, err := rec.getScanData(evaluated, c.Query("office_id"), 1000)
		if err != nil {
			c.JSON(500, gin.H{
				"msg":  "error",
				"info": err,
			})
			return
		}

		count = count + data.Count
		if data.LastEvaluatedKey == nil {
			c.JSON(200, gin.H{
				"msg":   "ok",
				"count": count,
				"time":  fmt.Sprintf("Task took %s \r\n", time.Since(start)),
			})
			break
		}
		page++
		evaluated = data.LastEvaluatedKey

		if count > 2900 {
			data, err = rec.getScanData(evaluated, c.Query("office_id"), pageSize)
			c.JSON(200, gin.H{
				"msg":      "page 5",
				"count":    data.Items,
				"evaluate": data.LastEvaluatedKey,
				"time":     fmt.Sprintf("Task took %s \r\n", time.Since(start)),
			})
			return
		}

	}
}

func (rec *Score) getScanData(evaluated map[string]types.AttributeValue, officeId string, limit int32) (*dynamodb.QueryOutput, error) {
	input := &dynamodb.QueryInput{
		TableName:              aws.String("score"),
		KeyConditionExpression: aws.String("#office_id = :office_id"),
		ExpressionAttributeNames: map[string]string{
			"#office_id": "office_id",
		},
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":office_id": &types.AttributeValueMemberS{Value: officeId}, //
		},
		ScanIndexForward: aws.Bool(true), // 昇順
		Limit:            aws.Int32(limit),
		//Select:            types.SelectCount,
		ExclusiveStartKey: evaluated,
	}

	return dynamo.Query(input)
}
