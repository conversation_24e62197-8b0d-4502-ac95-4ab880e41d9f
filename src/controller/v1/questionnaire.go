package v1

import (
	"errors"
	"fmt"
	"math"
	"mi-restful-api/client"
	"mi-restful-api/enum"
	"mi-restful-api/exception"
	"mi-restful-api/logging"
	"mi-restful-api/model"
	"mi-restful-api/repository"
	"mi-restful-api/request"
	"mi-restful-api/response"
	"mi-restful-api/utils/common"
	"net/http"
	"strconv"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Questionnaire struct {
	TableName string
}

// @Summary 評価リスト
// @Description 評価リスト
// @ID get-score-list
// @Produce  json
// @Param data body request.QuestionnaireIndexReq true "Request json data"
// @Success 200 {object} response.QuestionnaireIndexResp "response"
// @Failure 400 {object} response.QuestionnaireIndexResp "Bad Request ,param error"
// @Failure 401 {object} response.QuestionnaireIndexResp "Unauthorized"
// @Failure 404 {object} response.QuestionnaireIndexResp "Not Found"
// @Failure 500 {object} response.QuestionnaireIndexResp "Internal Server Error"
// @Failure 601 {object} response.QuestionnaireIndexResp "mysql conn error"
// @Failure 602 {object} response.QuestionnaireIndexResp "mysql sql error"
// @Router /web/questionnaire [get]
// @Tags Questionnaire
func (rec *Questionnaire) Index(c *gin.Context) {
	requestId := common.GetRequestId(c)
	var resp response.QuestionnaireIndexResp

	officeId, err := repository.GetOfficeId(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryAuth,
			"message":   "questionnaire index token parse msg error",
			"office_id": officeId,
			"module":    "QuestionnaireIndex",
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	var req request.QuestionnaireIndexReq
	if err := c.ShouldBind(&req); err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   "questionnaire index params parse error",
			"module":    "QuestionnaireIndex",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "questionnaire index access log",
		"module":    "QuestionnaireIndex",
		"office_id": officeId,
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "get",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
	})

	if req.PageSize < 1 {
		req.PageSize = 0
	}
	if req.Page < 1 {
		req.Page = 1
	}
	offset := (req.Page - 1) * req.PageSize

	db, err := client.GetDbClient()
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "questionnaire index get db client error",
			"module":    "QuestionnaireIndex",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Code = enum.DBConnErrorCode
		resp.Msg = err.Error()
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	builder := db.Model(model.Questionnaire{}).
		Where("office_id= ? and created_at >= ? AND created_at <= ?", officeId, req.StartDate, req.EndDate.Add(time.Duration(86399)*time.Second))
	if req.CaddyId > 0 {
		builder.Where("caddy_id=?", req.CaddyId)
	}
	if req.Weekday == 1 {
		builder.Where("weekday = 1")
	} else if req.Weekday == 2 {
		builder.Where("weekday = 2")
	}

	var total int64
	err = builder.Count(&total).Error
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "questionnaire index from rdb get data count error",
			"module":    "QuestionnaireIndex",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Code = enum.DBSQLEXECErrorCode
		resp.Msg = err.Error()
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	var data []model.Questionnaire
	builder.Offset(offset)

	if req.PageSize > 0 {
		builder.Limit(req.PageSize)
	}

	builder.Order("created_at desc")

	err = builder.Preload("Players.Answers").Preload("Players.PlayerFeedback").Find(&data).Error
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "questionnaire index from rdb get data error",
			"module":    "QuestionnaireIndex",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = enum.DBSQLEXECErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	resp.Transfer(data)
	resp.CurPage = req.Page
	resp.Msg = "ok"
	resp.Code = 200
	rec.pageTidy(&resp, total, req.PageSize, offset)

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "questionnaire index response log",
		"module":    "QuestionnaireIndex",
		"office_id": officeId,
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "get",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
		"response": resp,
	})

	if len(data) > 0 {
		c.JSON(http.StatusOK, resp)

	} else {
		c.JSON(http.StatusNoContent, resp)
	}
}

func (rec *Questionnaire) pageTidy(resp *response.QuestionnaireIndexResp, total int64, pageSize, offset int) {
	resp.Total = int(total)
	resp.PerPage = pageSize
	resp.From = offset + 1
	if pageSize == 0 {
		resp.To = int(total)
	} else if (int(total) - offset) > pageSize {
		resp.To = resp.From + pageSize
	} else if int(total)-offset > 0 {
		resp.To = int(total)
	} else {
		resp.To = 0
	}
	if pageSize == 0 {
		resp.LastPage = 1
	} else {
		resp.LastPage = int(math.Ceil(float64(resp.Total) / float64(pageSize)))
	}
}

func (rec *Questionnaire) Show(c *gin.Context) {
	var resp response.QuestionnaireShowResp
	var req request.QuestionnaireShowReq
	if err := c.ShouldBind(&req); err != nil {
		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	db, err := client.GetDbClient()
	if err != nil {
		resp.Code = http.StatusInternalServerError
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}
	officeId, err := repository.GetOfficeId(c)
	if err != nil {
		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	var data model.Questionnaire
	err = db.Model(&model.Questionnaire{}).Where("id=? and office_id=? and deleted_at is null", req.Id, officeId).First(&data).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			resp.Code = http.StatusBadRequest
			resp.Msg = err.Error()
			c.JSON(resp.Code, resp)
			return
		} else {
			resp.Code = http.StatusInternalServerError
			resp.Msg = err.Error()
			c.JSON(resp.Code, resp)
			return
		}
	}

	resp.Transfer(data)
	resp.Msg = "ok"
	resp.Code = 200
	c.JSON(http.StatusOK, resp)
}

// @Summary 新しい評価を追加
// @Description create a score record
// @ID post-score-store
// @Accept  json
// @Produce  json
// @Param data body request.QuestionnaireCreateReq true "Request payload"
// @Success 200 {object} response.QuestionnaireCreateResp "response"
// @Failure 400 {object} response.QuestionnaireCreateResp "Bad Request ,param error"
// @Failure 401 {object} response.QuestionnaireCreateResp "Unauthorized"
// @Failure 404 {object} response.QuestionnaireCreateResp "Not Found"
// @Failure 500 {object} response.QuestionnaireCreateResp "Internal Server Error"
// @Failure 601 {object} response.QuestionnaireCreateResp "mysql conn error"
// @Failure 602 {object} response.QuestionnaireCreateResp "mysql sql error"
// @Failure 603 {object} response.QuestionnaireCreateResp "weekday deal error"
// @Router /app/answer [post]
// @Tags Questionnaire
func (rec *Questionnaire) Store(c *gin.Context) {
	var resp response.QuestionnaireCreateResp
	requestId := common.GetRequestId(c)

	officeId, err := repository.GetOfficeId(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryAuth,
			"message":   "questionnaire store token parse msg error",
			"office_id": officeId,
			"module":    "QuestionnaireStore",
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	var req request.QuestionnaireCreateReq
	if err := c.ShouldBindBodyWithJSON(&req); err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   "questionnaire store params parse error",
			"module":    "QuestionnaireStore",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	if len(req.Serial) > 1 {
		// will continue follow
	} else if len(req.SortKey) > 1 {
		//Handle compatibility for sort_key
		req.Serial = req.SortKey
	} else {
		err := errors.New("sort_key or serial at least one exist")
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   "questionnaire store params parse error",
			"module":    "QuestionnaireStore",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "questionnaire store access log",
		"module":    "QuestionnaireStore",
		"office_id": officeId,
		"device-id": c.GetHeader("device-id"),
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "post",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
	})

	if audit := repository.NewOperateAudit(); audit.AuditOn == enum.OperateAuditON {
		logging.LogFormat(enum.LogInfo, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   "questionnaire store audit check open",
			"module":    "QuestionnaireStore",
			"office_id": officeId,
			"request":   requestId,
			"sync":      audit.AuditSyncOn,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
				},
			},
		})

		if audit.AuditSyncOn == enum.OperateAuditSyncOn {
			go audit.SaveOperate(model.Questionnaire{}.TableName(), officeId, req, "")
		} else {
			audit.SaveOperate(model.Questionnaire{}.TableName(), officeId, req, "")
		}
	}

	db, err := client.GetDbClient()
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "questionnaire store get db client error",
			"module":    "QuestionnaireStore",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = enum.DBConnErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	var handle = repository.CommentToDb{ConnMySQL: db}
	id, err := handle.StoreAnswerData(req, officeId)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "questionnaire store to db error",
			"module":    "QuestionnaireStore",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
					"data":   req,
				},
			},
		})

		resp.Code = exception.StatusCode(err)
		resp.Msg = err.Error()
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	resp.Id = id
	resp.Msg = "ok"
	resp.Code = 200

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "questionnaire store response log",
		"module":    "QuestionnaireStore",
		"office_id": officeId,
		"request":   requestId,
		"device-id": c.GetHeader("device-id"),
		"param": map[string]any{
			"req": map[string]any{
				"method": "post",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
		"response": resp,
	})

	c.JSON(http.StatusOK, resp)
}

func (rec *Questionnaire) getSortKey(serial, uid string) string {
	// オリジナルデータの sort_key score_{yyyyMMdd}_{serial}_{uid}_{HHmmss}
	now := time.Now()
	return fmt.Sprintf("score_%s_%s_%s_%s", now.Format("20060102"), serial, uid, now.Format("150405"))
}

// @Summary カート情報取得
// @Description カート情報取得
// @ID get-cartinfo
// @Accept  json
// @Produce  json
// @Param id query int true "カートID" default(23)
// @Success 200 {object} response.CartInfoResp "response"
// @Failure 400 {object} response.CartInfoResp "Bad Request ,param error"
// @Failure 401 {object} response.CartInfoResp "Unauthorized"
// @Failure 404 {object} response.CartInfoResp "Not Found"
// @Failure 500 {object} response.CartInfoResp "Internal Server Error"
// @Failure 601 {object} response.CartInfoResp "mysql conn error"
// @Failure 602 {object} response.CartInfoResp "mysql sql error"
// @Router /app/cartinfo [get]
// @Tags Questionnaire
func (rec *Questionnaire) CartInfo(c *gin.Context) {
	var resp response.CartInfoResp
	requestId := common.GetRequestId(c)

	// obtain userinfo
	userInfo, err := repository.GetAppAuthInfo(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryAuth,
			"message":   "questionnaire cart-info token parse msg error",
			"office_id": "",
			"module":    "QuestionnaireCartInfo",
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	var req request.CartInfoRespReq
	if err := c.ShouldBind(&req); err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":   enum.LogCategoryApp,
			"message":    "questionnaire cart-info params parse error",
			"module":     "QuestionnaireCartInfo",
			"office_id":  userInfo.OfficeID,
			"office_key": userInfo.OfficeKey,
			"request":    requestId,
			"err":        err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = http.StatusBadRequest
		c.JSON(resp.Code, resp)
		return
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "questionnaire cart-info access log",
		"module":    "QuestionnaireCartInfo",
		"office_id": userInfo.OfficeID,
		"request":   requestId,
		"device-id": c.GetHeader("device-id"),
		"cart_no":   req.Id,
		"param": map[string]any{
			"req": map[string]any{
				"method": "get",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
	})

	// get data by dynamodb
	scorePrimaryKey := model.MarshaliDynamoDb{
		PartitionKey: userInfo.OfficeKey,
		SortKey:      "score_" + time.Now().Format("20060102"),
	}
	items, err := repository.GetCardInfoFromDynamoDB(scorePrimaryKey, strconv.Itoa(req.Id)) // ver1
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryDynamodb,
			"message":   "questionnaire cart-info get data from dynamodb error",
			"module":    "QuestionnaireCartInfo",
			"office_id": userInfo.OfficeID,
			"request":   requestId,
			"query":     scorePrimaryKey,
			"cart_no":   req.Id,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	if items == nil || len(items) < 1 {
		resp.Msg = "no data"
		resp.Code = http.StatusNoContent
		c.JSON(http.StatusNoContent, resp)
		return
	}

	// get answer info by rds
	data, err := repository.GetCartInfoOfRDS(userInfo.OfficeID, req.Id)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "questionnaire cart-info get answer from rdb error",
			"module":    "QuestionnaireCartInfo",
			"office_id": userInfo.OfficeID,
			"request":   requestId,
			"cart_no":   req.Id,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	// combine data
	resp.Msg = "ok"
	resp.Code = 200
	resp.Transfer(data)
	resp.Combine(items, "v1")

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "questionnaire cart-info v1 response log",
		"module":    "QuestionnaireCartInfo",
		"office_id": userInfo.OfficeID,
		"device-id": c.GetHeader("device-id"),
		"request":   requestId,
		"cart_no":   req.Id,
		"param": map[string]any{
			"req": map[string]any{
				"method": "get",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
		"response": resp,
	})

	c.JSON(http.StatusOK, resp)
}

// @Summary カート情報取得 v2
// @Description カート情報取得 v2
// @ID get-cartinfo-v2
// @Accept  json
// @Produce  json
// @Param id query int true "カートID" default(23)
// @Success 200 {object} response.CartInfoResp "response"
// @Failure 400 {object} response.CartInfoResp "Bad Request ,param error"
// @Failure 401 {object} response.CartInfoResp "Unauthorized"
// @Failure 404 {object} response.CartInfoResp "Not Found"
// @Failure 500 {object} response.CartInfoResp "Internal Server Error"
// @Failure 601 {object} response.CartInfoResp "mysql conn error"
// @Failure 602 {object} response.CartInfoResp "mysql sql error"
// @Router /app/cartinfo/v2 [get]
// @Tags Questionnaire
func (rec *Questionnaire) CartInfoV2(c *gin.Context) {
	var resp response.CartInfoResp
	requestId := common.GetRequestId(c)

	// obtain userinfo
	userInfo, err := repository.GetAppAuthInfo(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryAuth,
			"message":   "questionnaire cart-info v2 token parse msg error",
			"office_id": "",
			"module":    "QuestionnaireCartInfo",
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	var req request.CartInfoRespReq
	if err := c.ShouldBind(&req); err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":   enum.LogCategoryApp,
			"message":    "questionnaire cart-info v2 params parse error",
			"module":     "QuestionnaireCartInfo",
			"office_id":  userInfo.OfficeID,
			"office_key": userInfo.OfficeKey,
			"request":    requestId,
			"err":        err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = http.StatusBadRequest
		c.JSON(resp.Code, resp)
		return
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "questionnaire cart-info v2 access log",
		"module":    "QuestionnaireCartInfo",
		"office_id": userInfo.OfficeID,
		"request":   requestId,
		"cart_no":   req.Id,
		"device-id": c.GetHeader("device-id"),
		"param": map[string]any{
			"req": map[string]any{
				"method": "get",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
	})

	// get data by dynamodb
	items, err := combineScoreUnderLineAndPlayHistoryItems(requestId, userInfo.OfficeKey, userInfo.OfficeID, req.Id)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryDynamodb,
			"message":   "questionnaire cart-info v2 get data from dynamodb error",
			"module":    "QuestionnaireCartInfo",
			"office_id": userInfo.OfficeID,
			"request":   requestId,
			"cart_no":   req.Id,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	if items == nil || len(items) < 1 {
		resp.Msg = "no data"
		resp.Code = http.StatusNoContent
		c.JSON(http.StatusNoContent, resp)
		return
	}

	// get answer info by rds
	data, err := repository.GetCartInfoOfRDS(userInfo.OfficeID, req.Id)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "questionnaire cart-info v2 get answer from rdb error",
			"module":    "QuestionnaireCartInfo",
			"office_id": userInfo.OfficeID,
			"request":   requestId,
			"cart_no":   req.Id,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	// combine data
	resp.Msg = "ok"
	resp.Code = 200
	resp.Transfer(data)
	resp.Combine(items, "v2")

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "questionnaire cart-info v2 response log",
		"module":    "QuestionnaireCartInfo",
		"office_id": userInfo.OfficeID,
		"device-id": c.GetHeader("device-id"),
		"request":   requestId,
		"cart_no":   req.Id,
		"param": map[string]any{
			"req": map[string]any{
				"method": "get",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
		"response": resp,
	})

	c.JSON(http.StatusOK, resp)
}

func combineScoreUnderLineAndPlayHistoryItems(requestId, officeKey, officeId string, cartNo int) (map[string]response.CartInfoData, error) {
	var multiThread sync.WaitGroup
	multiThread.Add(2)

	// get data by dynamodb
	var respScore map[string]response.CartInfoData
	var scoreItemsErr error
	var respPlayHistory map[string]response.CartInfoData
	var playHistoryItemsErr error
	go func(threadHandle *sync.WaitGroup) {
		defer threadHandle.Done()
		scorePrimaryKey := model.MarshaliDynamoDb{
			PartitionKey: officeKey,
			SortKey:      "score_" + time.Now().Format("20060102"),
		}
		respScore, scoreItemsErr = repository.GetCardInfoFromDynamoDB(scorePrimaryKey, strconv.Itoa(cartNo)) // ver1
	}(&multiThread)

	go func(threadHandle *sync.WaitGroup) {
		defer threadHandle.Done()
		respPlayHistory, playHistoryItemsErr = repository.GetCartInfoNewVersion(requestId, officeKey, officeId, cartNo) // ver2
	}(&multiThread)

	// wait all go routine return
	multiThread.Wait()

	logging.LogFormat(enum.LogDebug, map[string]any{
		"category":     enum.LogCategoryApp,
		"message":      "cart info v2 combine data ,cart_info_v2_debug",
		"module":       "CartInfoV2Index",
		"cart_no":      cartNo,
		"request":      requestId,
		"score_err":    scoreItemsErr,
		"history_err":  playHistoryItemsErr,
		"score_data":   respScore,
		"history_data": respPlayHistory,
	})

	if scoreItemsErr != nil && playHistoryItemsErr != nil {
		return respScore, scoreItemsErr
	} else if scoreItemsErr != nil {
		return respPlayHistory, playHistoryItemsErr
	} else if playHistoryItemsErr != nil {
		return respScore, scoreItemsErr
	} else {
		return combineItems(respScore, respPlayHistory), nil
	}
}

func combineItems(scoreItems, playHistoryItems map[string]response.CartInfoData) map[string]response.CartInfoData {
	scoreLength := len(scoreItems)
	historyLength := len(playHistoryItems)
	finalData := make(map[string]response.CartInfoData, 0)

	logging.LogFormat(enum.LogDebug, map[string]any{
		"category":      enum.LogCategoryApp,
		"message":       "cart info v2 combine data ,cart_info_v2_debug",
		"module":        "CartInfoV2Index",
		"history_count": historyLength,
		"score_count":   scoreLength,
		"score_data":    scoreItems,
		"history_data":  playHistoryItems,
	})

	if scoreLength > 0 && historyLength > 0 {
		filterMap := make(map[string]string, 0)
		for playKey, playData := range playHistoryItems {
			if _, ok := filterMap[playData.Serial]; ok {
				// exist skip
			} else {
				finalData[playKey] = playData
				filterMap[playData.Serial] = playData.Serial
			}
		}

		for scoreKey, scoreData := range scoreItems {
			if _, ok := filterMap[scoreData.Serial]; ok {
				// exist skip
			} else {
				finalData[scoreKey] = scoreData
				filterMap[scoreData.Serial] = scoreData.Serial
			}
		}
		return finalData
	} else if scoreLength > 0 {
		return scoreItems
	} else if historyLength > 0 {
		return playHistoryItems
	} else {
		return nil
	}
}

// @Summary 新しい評価を追加
// @Description create a score record
// @ID get-questionnaire-csv
// @Accept  json
// @Produce  json
// @Success 200 {object} string "response"
// @Failure 400 {object} response.QuestionnaireExportCsv "Bad Request ,param error"
// @Failure 401 {object} response.QuestionnaireExportCsv "Unauthorized"
// @Failure 404 {object} response.QuestionnaireExportCsv "Not Found"
// @Failure 500 {object} response.QuestionnaireExportCsv "Internal Server Error"
// @Failure 601 {object} response.QuestionnaireExportCsv "mysql conn error"
// @Failure 602 {object} response.QuestionnaireExportCsv "mysql sql error"
// @Failure 604 {object} response.QuestionnaireExportCsv "csv header write error"
// @Failure 605 {object} response.QuestionnaireExportCsv "csv data write error"
// @Router /web/questionnaire/csv [get]
// @Tags Questionnaire
func (rec *Questionnaire) IndexCsv(c *gin.Context) {
	var resp response.QuestionnaireExportCsv
	requestId := common.GetRequestId(c)
	officeId, err := repository.GetOfficeId(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryAuth,
			"message":   "questionnaire csv export token parse msg error",
			"office_id": officeId,
			"module":    "QuestionnaireCsv",
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	var req request.QuestionnaireIndexReq
	if err := c.ShouldBind(&req); err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   "questionnaire index params parse error",
			"module":    "QuestionnaireIndex",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "questionnaire csv export access log",
		"module":    "QuestionnaireCsv",
		"office_id": officeId,
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "get",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
	})

	db, err := client.GetDbClient()
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "questionnaire csv export get db client error",
			"module":    "QuestionnaireCsv",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = enum.DBConnErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	builder := db.Model(model.Questionnaire{}).
		Where("office_id= ? and created_at >= ? AND created_at <= ?", officeId, req.StartDate, req.EndDate.Add(time.Duration(86399)*time.Second))
	if req.CaddyId > 0 {
		builder.Where("caddy_id=?", req.CaddyId)
	}
	if req.Weekday == 1 || req.Weekday == 2 {
		builder.Where("weekday = ?", req.Weekday)
	}

	var data []model.Questionnaire
	builder.Order("created_at desc")
	err = builder.Preload("Players.Answers").Preload("Players.PlayerFeedback").Find(&data).Error

	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "questionnaire get answer csv data error",
			"module":    "QuestionnaireCsv",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = enum.DBSQLEXECErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	handle := repository.DateToCsv{}
	buf, err := handle.ExportQuestionnaireCsv(data, officeId)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   "questionnaire csv export data tidy data format error",
			"module":    "QuestionnaireCsv",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	// レスポンスのヘッダーを設定、CSVを返す
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Disposition", "attachment; filename=answer.csv")
	c.Header("Content-Type", "text/csv; charset=utf-8")
	c.Data(http.StatusOK, "text/csv", buf.Bytes())
}

func (rec *Questionnaire) Update(c *gin.Context) {
	// TODO
}

func (rec *Questionnaire) Delete(c *gin.Context) {
	var resp response.QuestionnaireDelResp
	var req request.QuestionnaireDelReq
	if err := c.ShouldBind(&req); err != nil {
		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	officeId, err := repository.GetOfficeId(c)
	if err != nil {
		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	dbClient, err := client.GetDbClient()
	if err != nil {
		resp.Msg = err.Error()
		resp.Code = http.StatusInternalServerError
		c.JSON(resp.Code, resp)
		return
	}
	err = dbClient.Where("office_id = ? AND id = ?", officeId, req.Id).Delete(&model.Evaluation{}).Error
	if err != nil {
		resp.Msg = err.Error()
		resp.Code = http.StatusInternalServerError
		c.JSON(resp.Code, resp)
		return
	}

	resp.Msg = "ok"
	resp.Code = 200
	c.JSON(resp.Code, resp)
}
