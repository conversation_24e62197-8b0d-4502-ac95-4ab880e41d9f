package v1

import (
	"mi-restful-api/client"
	"mi-restful-api/enum"
	"mi-restful-api/exception"
	"mi-restful-api/logging"
	"mi-restful-api/model"
	"mi-restful-api/repository"
	"mi-restful-api/request"
	"mi-restful-api/response"
	"mi-restful-api/utils/common"
	"net/http"

	"github.com/gin-gonic/gin"
)

type QuesnaireSettings struct {
	TableName string
}

// @Summary アンケート各種設定取得
// @Description アンケート各種設定取得
// @Produce  json
// @Success 200 {object} response.QuesnaireSettingsIndexResp "response"
// @Failure 400 {object} response.QuesnaireSettingsIndexResp "Bad Request ,param error"
// @Failure 401 {object} response.QuesnaireSettingsIndexResp "Unauthorized"
// @Failure 404 {object} response.QuesnaireSettingsIndexResp "Not Found"
// @Failure 500 {object} response.QuesnaireSettingsIndexResp "Internal Server Error"
// @Failure 601 {object} response.QuesnaireSettingsIndexResp "mysql conn error"
// @Failure 602 {object} response.QuesnaireSettingsIndexResp "mysql sql error"
// @Router /web/quesnairesettings [get]
// @Router /app/quesnairesettings [get]
// @Tags QuesnaireSettings
func (rec *QuesnaireSettings) Index(c *gin.Context) {
	requestId := common.GetRequestId(c)
	var resp response.QuesnaireSettingsIndexResp

	officeId, err := repository.GetOfficeId(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryAuth,
			"message":   "QuesnaireSettings index token parse msg error",
			"office_id": officeId,
			"module":    "QuesnaireSettingsIndex",
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "QuesnaireSettings index access log",
		"module":    "QuesnaireSettingsIndex",
		"office_id": officeId,
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "get",
				"path":   c.FullPath(),
			},
		},
	})

	db, err := client.GetDbClient()
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "QuesnaireSettings index get db client error",
			"module":    "QuesnaireSettingsIndex",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Code = enum.DBConnErrorCode
		resp.Msg = err.Error()
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	builder := db.Model(model.QuesnaireSettings{}).
		Where("office_id= ?", officeId)

	var data model.QuesnaireSettings

	err = builder.Find(&data).Error
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "QuesnaireSettings index from rdb get data error",
			"module":    "QuesnaireSettingsIndex",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = enum.DBSQLEXECErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	if data.ID == 0 {
		resp.Data.CaddyNameType = 1 // デフォルトキャディの名前種類：1(キャディ)
	} else {
		resp.Data.CaddyNameType = data.CaddyNameType
	}
	resp.Msg = "ok"
	resp.Code = 200

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "QuesnaireSettings index response log",
		"module":    "QuesnaireSettingsIndex",
		"office_id": officeId,
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "get",
				"path":   c.FullPath(),
			},
		},
		"response": resp,
	})

	c.JSON(http.StatusOK, resp)
}

// @Summary アンケート各種設定を追加・編集
// @Description create a score record
// @ID post-quesnairesettings-store
// @Accept  json
// @Produce  json
// @Param data body request.QuesnaireSettingsCreateReq true "Request payload"
// @Success 200 {object} response.QuesnaireSettingsCreateResp "response"
// @Failure 400 {object} response.QuesnaireSettingsCreateResp "Bad Request ,param error"
// @Failure 401 {object} response.QuesnaireSettingsCreateResp "Unauthorized"
// @Failure 404 {object} response.QuesnaireSettingsCreateResp "Not Found"
// @Failure 500 {object} response.QuesnaireSettingsCreateResp "Internal Server Error"
// @Failure 601 {object} response.QuesnaireSettingsCreateResp "mysql conn error"
// @Failure 602 {object} response.QuesnaireSettingsCreateResp "mysql sql error"
// @Router /web/quesnairesettings [post]
// @Tags QuesnaireSettings
func (rec *QuesnaireSettings) Store(c *gin.Context) {
	var resp response.QuesnaireSettingsCreateResp
	requestId := common.GetRequestId(c)

	officeId, err := repository.GetOfficeId(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryAuth,
			"message":   "QuesnaireSettings store token parse msg error",
			"office_id": officeId,
			"module":    "QuesnaireSettingsStore",
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	var req request.QuesnaireSettingsCreateReq
	if err := c.ShouldBindBodyWithJSON(&req); err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   "QuesnaireSettings store params parse error",
			"module":    "QuesnaireSettingsStore",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
					"data":   req,
				},
			},
		})

		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "QuesnaireSettings store access log",
		"module":    "QuesnaireSettingsStore",
		"office_id": officeId,
		"device-id": c.GetHeader("device-id"),
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "post",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
	})

	if audit := repository.NewOperateAudit(); audit.AuditOn == enum.OperateAuditON {
		logging.LogFormat(enum.LogInfo, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   "QuesnaireSettings store audit check open",
			"module":    "QuesnaireSettingsStore",
			"office_id": officeId,
			"request":   requestId,
			"sync":      audit.AuditSyncOn,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
				},
			},
		})

		if audit.AuditSyncOn == enum.OperateAuditSyncOn {
			go audit.SaveOperate(model.QuesnaireSettings{}.TableName(), officeId, req, "")
		} else {
			audit.SaveOperate(model.QuesnaireSettings{}.TableName(), officeId, req, "")
		}
	}

	db, err := client.GetDbClient()
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "QuesnaireSettings store get db client error",
			"module":    "QuesnaireSettingsStore",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = enum.DBConnErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	var handle = repository.QuesnaireSettings{ConnMySQL: db}
	err = handle.StoreQuesnaireSettingsData(req, officeId)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "QuesnaireSettings store to db error",
			"module":    "QuesnaireSettingsStore",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
					"data":   req,
				},
			},
		})

		resp.Code = exception.StatusCode(err)
		resp.Msg = err.Error()
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	resp.Msg = "ok"
	resp.Code = 200

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "QuesnaireSettings store response log",
		"module":    "QuesnaireSettingsStore",
		"office_id": officeId,
		"request":   requestId,
		"device-id": c.GetHeader("device-id"),
		"param": map[string]any{
			"req": map[string]any{
				"method": "post",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
		"response": resp,
	})

	c.JSON(http.StatusOK, resp)
}
