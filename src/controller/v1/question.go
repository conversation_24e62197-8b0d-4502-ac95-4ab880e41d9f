package v1

import (
	"mi-restful-api/client"
	"mi-restful-api/enum"
	"mi-restful-api/exception"
	"mi-restful-api/logging"
	"mi-restful-api/model"
	"mi-restful-api/repository"
	"mi-restful-api/request"
	"mi-restful-api/response"
	"mi-restful-api/utils/common"
	"net/http"
	"strconv"
	"time"

	log "github.com/sirupsen/logrus"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Question struct {
	TableName string
}

// @Summary 問題設置リストを取得
// @Description 問題設置リストを取得
// @Produce  json
// @Success 200 {object} response.QuestionIndexResp "response"
// @Failure 400 {object} response.QuestionIndexResp "Bad Request"
// @Failure 401 {object} response.QuestionIndexResp "Unauthorized"
// @Failure 404 {object} response.QuestionIndexResp "Not Found"
// @Failure 500 {object} response.QuestionIndexResp "Internal Server Error"
// @Failure 601 {object} response.QuestionIndexResp "mysql conn error"
// @Failure 602 {object} response.QuestionIndexResp "mysql sql error"
// @Router /web/question [get]
// @Router /app/question [get]
// @Tags Question
func (rec *Question) Index(c *gin.Context) {
	var resp response.QuestionIndexResp
	requestId := common.GetRequestId(c)

	officeId, err := repository.GetOfficeId(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryAuth,
			"message":  "question index from session get office id err",
			"module":   "QuestionIndex",
			"request":  requestId,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "question index access log",
		"office_id": officeId,
		"module":    "QuestionIndex",
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "get",
				"path":   c.FullPath(),
			},
		},
	})

	dbClient, err := client.GetDbClient()
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   err.Error(),
			"office_id": officeId,
			"module":    "QuestionIndex",
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = enum.DBConnError
		resp.Code = enum.DBConnErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}
	repo := repository.Question{
		ConnMySQL: dbClient,
	}
	data, err := repo.FindQuestion(officeId)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   err.Error(),
			"office_id": officeId,
			"module":    "QuestionIndex",
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		if err == gorm.ErrRecordNotFound {
			resp.Msg = gorm.ErrRecordNotFound.Error()
			resp.Code = http.StatusNoContent
			c.JSON(http.StatusNoContent, resp)
			return
		}
		resp.Msg = enum.DBSQLEXECError
		resp.Code = enum.DBSQLEXECErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	if len(data) < 1 {
		resp.Msg = "no data"
		resp.Code = http.StatusNoContent
		resp.Transfer(data)

		c.JSON(http.StatusNoContent, resp)
		return
	}

	resp.Msg = "ok"
	resp.Code = 200
	resp.Transfer(data)

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "question index response log",
		"office_id": officeId,
		"module":    "QuestionIndex",
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "get",
				"path":   c.FullPath(),
			},
		},
		"response": data,
	})

	c.JSON(200, resp)
}

func (rec *Question) Show(c *gin.Context) {
	var resp response.QuestionShowResp
	questionId, err2 := strconv.Atoi(c.Query("id"))
	if err2 != nil {
		resp.Msg = err2.Error()
		resp.Code = http.StatusInternalServerError
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	requestId := common.GetRequestId(c)

	officeId, err := repository.GetOfficeId(c)
	if err != nil {
		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	dbClient, err := client.GetDbClient()
	if err != nil {
		log.WithFields(log.Fields{"requestId": requestId, "error": err}).Error()

		resp.Msg = enum.DBConnError
		resp.Code = enum.DBConnErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	repo := repository.Question{
		ConnMySQL: dbClient,
	}
	data, err := repo.GetQuestion(officeId, questionId)
	if err != nil {
		resp.Msg = err.Error()
		if err == gorm.ErrRecordNotFound {
			resp.Code = http.StatusNotFound
			c.JSON(http.StatusNotFound, resp)
		} else {
			resp.Code = http.StatusInternalServerError
			c.JSON(http.StatusInternalServerError, resp)
		}
		return
	}

	resp.Transfer(*data)
	if data.ID == 0 {
		resp.Data = nil
	}
	resp.Msg = "ok"
	resp.Code = 200
	c.JSON(200, resp)
}

// @Summary 新しい問題設置を作成
// @Description 新しい問題設置を作成
// @ID get-question-store
// @Accept  json
// @Produce  json
// @Param data body request.Question true "Request payload"
// @Success 200 {object} response.QuestionCreateResp "response"
// @Failure 400 {object} response.QuestionCreateResp "Bad Request ,param error"
// @Failure 401 {object} response.QuestionCreateResp "Unauthorized"
// @Failure 404 {object} response.QuestionCreateResp "Not Found"
// @Failure 500 {object} response.QuestionCreateResp "Internal Server Error"
// @Failure 601 {object} response.QuestionCreateResp "mysql conn error"
// @Failure 602 {object} response.QuestionCreateResp "mysql sql error"
// @Router /web/question [post]
// @Tags Question
func (rec *Question) Store(c *gin.Context) {
	var resp response.QuestionCreateResp
	requestId := common.GetRequestId(c)

	officeId, err := repository.GetOfficeId(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryAuth,
			"message":   "question store token against info error",
			"office_id": officeId,
			"module":    "QuestionStore",
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	var req request.Question
	if err := c.ShouldBindBodyWithJSON(&req); err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   "question store params parse error",
			"module":    "QuestionStore",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = http.StatusBadRequest
		c.JSON(resp.Code, resp)
		return
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "question store access log",
		"module":    "QuestionStore",
		"office_id": officeId,
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "post",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
	})

	if audit := repository.NewOperateAudit(); audit.AuditOn == enum.OperateAuditON {
		logging.LogFormat(enum.LogInfo, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   "question store audit check open",
			"module":    "QuestionStore",
			"office_id": officeId,
			"request":   requestId,
			"sync":      audit.AuditSyncOn,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
				},
			},
		})
		if audit.AuditSyncOn == enum.OperateAuditSyncOn {
			go audit.SaveOperate(model.Question{}.TableName(), officeId, req, "")
		} else {
			audit.SaveOperate(model.Question{}.TableName(), officeId, req, "")
		}
	}

	dbClient, err := client.GetDbClient()
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "question store get db client error",
			"module":    "QuestionStore",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
				},
			},
		})
		resp.Msg = err.Error()
		resp.Code = enum.DBConnErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	var data model.Question
	data.CreatedAt = time.Now()
	data.Content = req.Content
	data.Require = req.Require
	data.OfficeID = officeId
	data.Type = req.Type

	handle := repository.Question{ConnMySQL: dbClient}
	id, err := handle.CreateQuestion(data, officeId)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   "question store create data error",
			"module":    "QuestionStore",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(resp.Code, resp)
		return
	}

	resp.Msg = "ok"
	resp.Code = 200
	resp.Id = id

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "question store response log",
		"module":    "QuestionStore",
		"office_id": officeId,
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "post",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
		"response": resp,
	})

	c.JSON(resp.Code, resp)
}

// @Summary 問題内容を更新
// @Description 問題内容とソートIDを更新
// @ID put-question-update
// @Accept  json
// @Produce  json
// @Param id path string true "问题id"
// @Param content body string true "設問内容"
// @Success 200 {object} response.QuestionShowResp "response"
// @Failure 400 {object} response.QuestionShowResp "Bad Request ,param error"
// @Failure 401 {object} response.QuestionShowResp "Unauthorized"
// @Failure 404 {object} response.QuestionShowResp "Not Found"
// @Failure 500 {object} response.QuestionShowResp "Internal Server Error"
// @Failure 601 {object} response.QuestionShowResp "mysql conn error"
// @Failure 602 {object} response.QuestionShowResp "mysql sql error"
// @Router /web/question/:id [put]
// @Tags Question
func (rec *Question) Update(c *gin.Context) {
	questionId := c.Param("id")
	requestId := common.GetRequestId(c)

	var resp response.QuestionShowResp
	officeId, err := repository.GetOfficeId(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryAuth,
			"message":   "question update token against msg get error",
			"office_id": officeId,
			"module":    "QuestionUpdate",
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "put",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	var req request.PutQuestionParams
	if err := c.ShouldBindBodyWithJSON(&req); err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   "question update params parse error",
			"module":    "QuestionUpdate",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "put",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = http.StatusBadRequest
		c.JSON(resp.Code, resp)
		return
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "question update access log",
		"module":    "QuestionUpdate",
		"office_id": officeId,
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "put",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
	})

	if questionId == "" {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   "question update param error， question id is empty",
			"module":    "QuestionUpdate",
			"office_id": officeId,
			"request":   requestId,
			"param": map[string]any{
				"req": map[string]any{
					"method": "put",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = "id required"
		resp.Code = http.StatusBadRequest
		c.JSON(resp.Code, resp)
		return
	}

	if audit := repository.NewOperateAudit(); audit.AuditOn == enum.OperateAuditON {
		logging.LogFormat(enum.LogInfo, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   "question update audit check open",
			"module":    "QuestionUpdate",
			"office_id": officeId,
			"request":   requestId,
			"sync":      audit.AuditSyncOn,
			"param": map[string]any{
				"req": map[string]any{
					"method": "put",
					"path":   c.FullPath(),
				},
			},
		})

		if audit.AuditSyncOn == enum.OperateAuditSyncOn {
			go audit.SaveOperate(model.Question{}.TableName(), officeId, req, "")
		} else {
			audit.SaveOperate(model.Question{}.TableName(), officeId, req, "")
		}
	}

	id, err2 := strconv.Atoi(questionId)
	if err2 != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   "question update param question id parse int error",
			"module":    "QuestionUpdate",
			"office_id": officeId,
			"request":   requestId,
			"err":       err2,
			"param": map[string]any{
				"req": map[string]any{
					"method": "put",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = "id need number"
		resp.Code = http.StatusBadRequest
		c.JSON(resp.Code, resp)
		return
	}

	dbClient, err := client.GetDbClient()
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "question update get db client error",
			"module":    "QuestionUpdate",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "put",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = enum.DBConnErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	handle := repository.Question{
		ConnMySQL: dbClient,
	}
	data, err := handle.UpdateQuestion(officeId, id, req)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "question update to db error",
			"module":    "QuestionUpdate",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "put",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = enum.DBSQLEXECErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	resp.Msg = "ok"
	resp.Code = 200
	resp.Transfer(*data)

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "question update response log",
		"module":    "QuestionUpdate",
		"office_id": officeId,
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "put",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
		"response": resp,
	})

	c.JSON(resp.Code, resp)
}

// @Summary question delete
// @Description delete question
// @ID get-question-delete
// @Accept  json
// @Produce  json
// @Param id path string true "設問ID"
// @Success 200 {object} response.QuestionDelResp "response"
// @Failure 400 {object} response.QuestionDelResp "Bad Request ,param error"
// @Failure 401 {object} response.QuestionDelResp "Unauthorized"
// @Failure 404 {object} response.QuestionDelResp "Not Found"
// @Failure 500 {object} response.QuestionDelResp "Internal Server Error"
// @Failure 601 {object} response.QuestionDelResp "mysql conn error"
// @Failure 602 {object} response.QuestionDelResp "mysql sql error"
// @Router /web/question/{id} [delete]
// @Tags Question
func (rec *Question) Delete(c *gin.Context) {
	requestId := common.GetRequestId(c)
	var resp response.QuestionDelResp

	questionId := c.Param("id")
	if questionId == "" {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "question delete param error， question id is empty",
			"module":   "QuestionDelete",
			"request":  requestId,
			"param": map[string]any{
				"req": map[string]any{
					"method": "delete",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = "id required"
		resp.Code = http.StatusBadRequest
		c.JSON(resp.Code, resp)
		return
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category": enum.LogCategoryApp,
		"message":  "question delete access log",
		"module":   "QuestionDelete",
		"request":  requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "delete",
				"path":   c.FullPath(),
			},
		},
	})

	officeId, err := repository.GetOfficeId(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryAuth,
			"message":   "question delete token against msg get error",
			"office_id": officeId,
			"module":    "QuestionDelete",
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "delete",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	if audit := repository.NewOperateAudit(); audit.AuditOn == enum.OperateAuditON {
		logging.LogFormat(enum.LogInfo, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   "question delete audit check open",
			"module":    "QuestionDelete",
			"office_id": officeId,
			"request":   requestId,
			"sync":      audit.AuditSyncOn,
			"param": map[string]any{
				"req": map[string]any{
					"method": "delete",
					"path":   c.FullPath(),
				},
			},
		})

		if audit.AuditSyncOn == enum.OperateAuditSyncOn {
			go audit.SaveOperate(model.Question{}.TableName(), officeId, map[string]string{"id": questionId}, "")
		} else {
			audit.SaveOperate(model.Question{}.TableName(), officeId, map[string]string{"id": questionId}, "")
		}
	}

	dbClient, err := client.GetDbClient()
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "question delete get db client error",
			"module":    "QuestionDelete",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "delete",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = enum.DBConnErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	handle := repository.Question{ConnMySQL: dbClient}
	err = handle.DeleteQuestion(questionId, officeId)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "question delete from db error",
			"module":    "QuestionDelete",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "delete",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		if resp.Code == http.StatusNotFound {
			c.JSON(http.StatusNotFound, resp)
		} else {
			c.JSON(http.StatusInternalServerError, resp)
		}
		return
	}

	resp.Msg = "ok"
	resp.Code = 200

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category": enum.LogCategoryApp,
		"message":  "question delete response log",
		"module":   "QuestionDelete",
		"request":  requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "delete",
				"path":   c.FullPath(),
			},
		},
		"response": resp,
	})

	c.JSON(resp.Code, resp)
}

// @Summary 問題内容を更新
// @Description 問題内容とソートIDを更新
// @ID put-question-index
// @Accept  json
// @Produce  json
// @Param id body string true "設問ID"
// @Param index body string true "設問順番"
// @Param data body request.UpdateIndexParams true "Request payload"
// @Success 200 {object} response.QuestionShowResp "response"
// @Failure 400 {object} response.QuestionShowResp "Bad Request ,param error"
// @Failure 401 {object} response.QuestionShowResp "Unauthorized"
// @Failure 404 {object} response.QuestionShowResp "Not Found"
// @Failure 500 {object} response.QuestionShowResp "Internal Server Error"
// @Failure 601 {object} response.QuestionShowResp "mysql conn error"
// @Failure 602 {object} response.QuestionShowResp "mysql sql error"
// @Router /web/questionindex [put]
// @Tags Question
func (rec *Question) UpdateIndex(c *gin.Context) {
	requestId := common.GetRequestId(c)
	var resp response.QuestionShowResp

	var req request.UpdateIndexParams
	if err := c.ShouldBindBodyWithJSON(&req); err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryApp,
			"message":  "question update index params parse error",
			"module":   "QuestionUpdateIndex",
			"request":  requestId,
			"err":      err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "put",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = http.StatusBadRequest
		c.JSON(resp.Code, resp)
		return
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category": enum.LogCategoryApp,
		"message":  "question update index access log",
		"module":   "QuestionUpdateIndex",
		"request":  requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "put",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
	})

	officeId, err := repository.GetOfficeId(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryAuth,
			"message":   "question update index token against msg get error",
			"office_id": officeId,
			"module":    "QuestionUpdateIndex",
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "put",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	if audit := repository.NewOperateAudit(); audit.AuditOn == enum.OperateAuditON {
		logging.LogFormat(enum.LogInfo, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   "question update index audit check open",
			"module":    "QuestionUpdateIndex",
			"office_id": officeId,
			"request":   requestId,
			"sync":      audit.AuditSyncOn,
			"param": map[string]any{
				"req": map[string]any{
					"method": "put",
					"path":   c.FullPath(),
				},
			},
		})

		if audit.AuditSyncOn == enum.OperateAuditSyncOn {
			go audit.SaveOperate(model.Question{}.TableName(), officeId, req, "")
		} else {
			audit.SaveOperate(model.Question{}.TableName(), officeId, req, "")
		}
	}

	dbClient, err := client.GetDbClient()
	if err != nil {
		log.WithFields(log.Fields{"requestId": requestId, "error": err}).Error()
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "question update index get db client error",
			"module":    "QuestionUpdateIndex",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "put",
					"path":   c.FullPath(),
					"data":   req,
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = enum.DBConnErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	handle := repository.Question{ConnMySQL: dbClient}
	err = handle.UpdateQuestionIndex(&req, officeId)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "question update index to db error",
			"module":    "QuestionUpdateIndex",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "put",
					"path":   c.FullPath(),
					"data":   req,
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = http.StatusBadRequest
		c.JSON(resp.Code, resp)
		return
	}

	data, err := handle.GetQuestion(officeId, req.Id)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "question update index get question error",
			"module":    "QuestionUpdateIndex",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "put",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, resp)
		} else {
			c.JSON(http.StatusInternalServerError, resp)
		}
		return
	}

	resp.Msg = "ok"
	resp.Code = 200
	resp.Transfer(*data)

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category": enum.LogCategoryApp,
		"message":  "question update index response log",
		"module":   "QuestionUpdateIndex",
		"request":  requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "put",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
		"response": resp,
	})

	c.JSON(resp.Code, resp)
}
