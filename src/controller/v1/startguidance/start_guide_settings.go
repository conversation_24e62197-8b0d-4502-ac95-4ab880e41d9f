package v1startguidance

import (
	"mi-restful-api/client"
	"mi-restful-api/enum"
	"mi-restful-api/exception"
	"mi-restful-api/logging"
	"mi-restful-api/model"
	"mi-restful-api/repository"
	"mi-restful-api/request"
	"mi-restful-api/response"
	"mi-restful-api/utils/common"
	"net/http"

	"github.com/gin-gonic/gin"
)

type StartGuideSettings struct {
	TableName string
}

// @Summary スタート案内設定取得
// @Description スタート案内設定取得
// @Produce  json
// @Success 200 {object} response.StartGuideSettingsIndexResp "response"
// @Failure 400 {object} response.StartGuideSettingsIndexResp "Bad Request ,param error"
// @Failure 401 {object} response.StartGuideSettingsIndexResp "Unauthorized"
// @Failure 404 {object} response.StartGuideSettingsIndexResp "Not Found"
// @Failure 500 {object} response.StartGuideSettingsIndexResp "Internal Server Error"
// @Failure 601 {object} response.StartGuideSettingsIndexResp "mysql conn error"
// @Failure 602 {object} response.StartGuideSettingsIndexResp "mysql sql error"
// @Router /web/start-guidance/settings [get]
// @Tags StartGuideSettings
func (rec *StartGuideSettings) Index(c *gin.Context) {
	requestId := common.GetRequestId(c)
	var resp response.StartGuideSettingsIndexResp

	officeId, err := repository.GetOfficeId(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryAuth,
			"message":   "StartGuideSettings index token parse msg error",
			"office_id": officeId,
			"module":    "StartGuideSettingsIndex",
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "StartGuideSettings index access log",
		"module":    "StartGuideSettingsIndex",
		"office_id": officeId,
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "get",
				"path":   c.FullPath(),
			},
		},
	})

	db, err := client.GetDbClient()
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "StartGuideSettings index get db client error",
			"module":    "StartGuideSettingsIndex",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Code = enum.DBConnErrorCode
		resp.Msg = err.Error()
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	var handle = repository.StartGuideSettings{ConnMySQL: db}
	data, err := handle.IndexStartGuideSettingsData(officeId)

	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "StartGuideSettings index from rdb get data error",
			"module":    "StartGuideSettingsIndex",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = enum.DBSQLEXECErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	resp.Transfer(data)
	resp.Msg = "ok"
	resp.Code = 200

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "StartGuideSettings index response log",
		"module":    "StartGuideSettingsIndex",
		"office_id": officeId,
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "get",
				"path":   c.FullPath(),
			},
		},
		"response": resp,
	})

	c.JSON(http.StatusOK, resp)
}

// @Summary スタート案内設定を保存
// @Description スタート案内設定を保存
// @ID post-startguidesettings-store
// @Accept  json
// @Produce  json
// @Param data body request.StartGuideSettingsCreateReq true "Request payload"
// @Success 200 {object} response.StartGuideSettingsCreateResp "response"
// @Failure 400 {object} response.StartGuideSettingsCreateResp "Bad Request ,param error"
// @Failure 401 {object} response.StartGuideSettingsCreateResp "Unauthorized"
// @Failure 404 {object} response.StartGuideSettingsCreateResp "Not Found"
// @Failure 500 {object} response.StartGuideSettingsCreateResp "Internal Server Error"
// @Failure 601 {object} response.StartGuideSettingsCreateResp "mysql conn error"
// @Failure 602 {object} response.StartGuideSettingsCreateResp "mysql sql error"
// @Router /web/start-guidance/settings [post]
// @Tags StartGuideSettings
func (rec *StartGuideSettings) Store(c *gin.Context) {
	var resp response.StartGuideSettingsCreateResp
	requestId := common.GetRequestId(c)

	officeId, err := repository.GetOfficeId(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryAuth,
			"message":   "StartGuideSettings store token parse msg error",
			"office_id": officeId,
			"module":    "StartGuideSettingsStore",
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	var req request.StartGuideSettingsCreateReq
	if err := c.ShouldBindBodyWithJSON(&req); err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   "StartGuideSettings store params parse error",
			"module":    "StartGuideSettingsStore",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
					"data":   req,
				},
			},
		})

		resp.Code = http.StatusBadRequest
		resp.Msg = err.Error()
		c.JSON(resp.Code, resp)
		return
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "StartGuideSettings store access log",
		"module":    "StartGuideSettingsStore",
		"office_id": officeId,
		"device-id": c.GetHeader("device-id"),
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "post",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
	})

	if audit := repository.NewOperateAudit(); audit.AuditOn == enum.OperateAuditON {
		logging.LogFormat(enum.LogInfo, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   "StartGuideSettings store audit check open",
			"module":    "StartGuideSettingsStore",
			"office_id": officeId,
			"request":   requestId,
			"sync":      audit.AuditSyncOn,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
				},
			},
		})

		if audit.AuditSyncOn == enum.OperateAuditSyncOn {
			go audit.SaveOperate(model.StartGuideSettings{}.TableName(), officeId, req, "")
		} else {
			audit.SaveOperate(model.StartGuideSettings{}.TableName(), officeId, req, "")
		}
	}

	db, err := client.GetDbClient()
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "StartGuideSettings store get db client error",
			"module":    "StartGuideSettingsStore",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = enum.DBConnErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	var handle = repository.StartGuideSettings{ConnMySQL: db}
	err = handle.StoreStartGuideSettingsData(req, officeId)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "StartGuideSettings store to db error",
			"module":    "StartGuideSettingsStore",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "post",
					"path":   c.FullPath(),
					"data":   req,
				},
			},
		})

		resp.Code = exception.StatusCode(err)
		resp.Msg = err.Error()
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	resp.Msg = "ok"
	resp.Code = 200

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "StartGuideSettings store response log",
		"module":    "StartGuideSettingsStore",
		"office_id": officeId,
		"request":   requestId,
		"device-id": c.GetHeader("device-id"),
		"param": map[string]any{
			"req": map[string]any{
				"method": "post",
				"path":   c.FullPath(),
				"data":   req,
			},
		},
		"response": resp,
	})

	c.JSON(http.StatusOK, resp)
}

// @Summary スタート案内インフォメーション取得
// @Description スタート案内インフォメーション取得
// @Produce  json
// @Success 200 {object} response.StartGuideInfoIndexResp "response"
// @Failure 400 {object} response.StartGuideInfoIndexResp "Bad Request ,param error"
// @Failure 401 {object} response.StartGuideInfoIndexResp "Unauthorized"
// @Failure 404 {object} response.StartGuideInfoIndexResp "Not Found"
// @Failure 500 {object} response.StartGuideInfoIndexResp "Internal Server Error"
// @Failure 601 {object} response.StartGuideInfoIndexResp "mysql conn error"
// @Failure 602 {object} response.StartGuideInfoIndexResp "mysql sql error"
// @Router /app/start-guidance/info [get]
// @Tags StartGuideInfo
func (rec *StartGuideSettings) Information(c *gin.Context) {
	requestId := common.GetRequestId(c)
	var resp response.StartGuideInfoIndexResp

	officeId, err := repository.GetOfficeId(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryAuth,
			"message":   "StartGuideInfo index token parse msg error",
			"office_id": officeId,
			"module":    "StartGuideInfoIndex",
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "StartGuideInfo index access log",
		"module":    "StartGuideInfoIndex",
		"office_id": officeId,
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "get",
				"path":   c.FullPath(),
			},
		},
	})

	db, err := client.GetDbClient()
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "StartGuideInfo index get db client error",
			"module":    "StartGuideInfoIndex",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Code = enum.DBConnErrorCode
		resp.Msg = err.Error()
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	var handle = repository.StartGuideSettings{ConnMySQL: db}
	data, err := handle.IndexStartGuideSettingsData(officeId)

	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "StartGuideInfo index from rdb get data error",
			"module":    "StartGuideInfoIndex",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = enum.DBSQLEXECErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	resp.Transfer(data)
	resp.Msg = "ok"
	resp.Code = 200

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "StartGuideInfo index response log",
		"module":    "StartGuideInfoIndex",
		"office_id": officeId,
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "get",
				"path":   c.FullPath(),
			},
		},
		"response": resp,
	})

	c.JSON(http.StatusOK, resp)
}
