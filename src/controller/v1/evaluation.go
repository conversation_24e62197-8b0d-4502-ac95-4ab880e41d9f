package v1

import (
	"mi-restful-api/client"
	"mi-restful-api/enum"
	"mi-restful-api/exception"
	"mi-restful-api/logging"
	"mi-restful-api/model"
	"mi-restful-api/repository"
	"mi-restful-api/request"
	"mi-restful-api/response"
	"mi-restful-api/utils/common"
	"net/http"
	"strconv"
	"time"

	log "github.com/sirupsen/logrus"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Evaluation struct {
	TableName string
}

// @Summary 評価設置リストを取得
// @Description 評価設置リストを取得
// @Produce  json
// @Success 200 {object} response.EvaluationIndexResp "response"
// @Failure 400 {object} response.EvaluationIndexResp "Bad Request ,param error"
// @Failure 401 {object} response.EvaluationIndexResp "Unauthorized"
// @Failure 404 {object} response.EvaluationIndexResp "Not Found"
// @Failure 500 {object} response.EvaluationIndexResp "Internal Server Error"
// @Failure 601 {object} response.EvaluationIndexResp "mysql conn error"
// @Router /web/evaluation [get]
// @Router /app/evaluation [get]
// @Tags Evaluation
func (rec *Evaluation) Index(c *gin.Context) {
	var resp response.EvaluationIndexResp
	requestId := common.GetRequestId(c)

	officeId, err := repository.GetOfficeId(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category": enum.LogCategoryAuth,
			"message":  "evaluation index from session get office id err",
			"module":   "EvaluationIndex",
			"request":  requestId,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "evaluation index access log",
		"office_id": officeId,
		"module":    "EvaluationIndex",
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "get",
				"path":   c.FullPath(),
			},
		},
	})

	dbClient, err := client.GetDbClient()
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "evaluation index get rdb connect error",
			"office_id": officeId,
			"module":    "EvaluationIndex",
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = enum.DBConnErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	handle := repository.EvaluationRepos{ConnMySQL: dbClient}
	data, err := handle.FindAllEvaluation(officeId)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   "Evaluation index query exec error",
			"office_id": officeId,
			"module":    "EvaluationIndex",
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		if err == gorm.ErrRecordNotFound {
			resp.Code = http.StatusNoContent
		} else {
			resp.Code = http.StatusInternalServerError
		}
		c.JSON(resp.Code, resp)
		return
	}

	if len(data) < 1 {
		resp.Msg = "no data"
		resp.Code = http.StatusNoContent
		resp.Transfer(data)

		c.JSON(http.StatusNoContent, resp)
		return
	}

	resp.Msg = "ok"
	resp.Code = 200
	resp.Transfer(data)

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "evaluation index response log",
		"office_id": officeId,
		"module":    "EvaluationIndex",
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "get",
				"path":   c.FullPath(),
			},
		},
		"response": resp,
	})

	c.JSON(200, resp)
}

func (rec *Evaluation) Show(c *gin.Context) {
	var resp response.EvaluationShowResp
	Id, err2 := strconv.Atoi(c.Query("id"))
	if err2 != nil {
		resp.Msg = err2.Error()
		resp.Code = http.StatusInternalServerError
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	requestId := common.GetRequestId(c)

	officeId, err := repository.GetOfficeId(c)
	if err != nil {
		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	dbClient, err := client.GetDbClient()
	if err != nil {
		log.WithFields(log.Fields{"requestId": requestId, "error": err}).Error()

		resp.Msg = err.Error()
		resp.Code = enum.DBConnErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	handle := repository.EvaluationRepos{ConnMySQL: dbClient}
	data, err := handle.FindEvaluation(officeId, Id)
	if err != nil {
		resp.Msg = err.Error()
		if err == gorm.ErrRecordNotFound {
			resp.Code = http.StatusNotFound
			c.JSON(http.StatusNotFound, resp)
		} else {
			resp.Code = http.StatusInternalServerError
			c.JSON(http.StatusInternalServerError, resp)
		}
		return
	}

	resp.Transfer(*data)
	if data.ID == 0 {
		resp.Data = nil
	}
	resp.Msg = "ok"
	resp.Code = 200
	c.JSON(200, resp)
}

func (rec *Evaluation) Store(c *gin.Context) {
	var resp response.EvaluationCreateResp
	var req request.EvaluationCreateReq
	if err := c.ShouldBindBodyWithJSON(&req); err != nil {
		resp.Msg = err.Error()
		resp.Code = http.StatusInternalServerError
		c.JSON(resp.Code, resp)
		return
	}

	dbClient, err := client.GetDbClient()
	if err != nil {
		resp.Msg = err.Error()
		resp.Code = http.StatusInternalServerError
		c.JSON(resp.Code, resp)
		return
	}

	officeId, err := repository.GetOfficeId(c)
	if err != nil {
		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	var data model.Evaluation
	data.Score = req.Score
	data.CreatedAt = time.Now()
	data.OfficeID = officeId
	data.Content = req.Content
	err = dbClient.Model(model.Evaluation{}).
		Create(&data).Error
	if err != nil {
		resp.Msg = err.Error()
		resp.Code = http.StatusInternalServerError
		c.JSON(resp.Code, resp)
		return
	}

	resp.Msg = "ok"
	resp.Code = 200
	resp.Id = data.ID
	c.JSON(resp.Code, resp)

}

// @Summary 評価内容またスコアを更新
// @Description update comment
// @ID put-comment-update
// @Accept  json
// @Produce  json
// @Param data body []request.EvaluationPutReq true "Request payload"
// @Success 200 {object} response.EvaluationUpdateResp "not config response"
// @Failure 400 {object} response.EvaluationUpdateResp "Bad Request ,param error"
// @Failure 401 {object} response.EvaluationUpdateResp "Unauthorized"
// @Failure 404 {object} response.EvaluationUpdateResp "Not Found"
// @Failure 500 {object} response.EvaluationUpdateResp "Internal Server Error"
// @Failure 601 {object} response.EvaluationUpdateResp "mysql conn error"
// @Failure 602 {object} response.EvaluationUpdateResp "mysql sql error"
// @Router /web/evaluation [put]
// @Tags Evaluation
func (rec *Evaluation) Update(c *gin.Context) {
	requestId := common.GetRequestId(c)
	var resp response.EvaluationUpdateResp
	officeId, err := repository.GetOfficeId(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryAuth,
			"message":   "evaluation update token parse error",
			"office_id": officeId,
			"module":    "EvaluationUpdate",
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "put",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	var reqNoOrder []request.EvaluationPutReq
	if err := c.ShouldBindBodyWithJSON(&reqNoOrder); err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   "evaluation update params parse error",
			"module":    "EvaluationUpdate",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "put",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = http.StatusBadRequest
		c.JSON(resp.Code, resp)
		return
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "evaluation update access log",
		"module":    "EvaluationUpdate",
		"office_id": officeId,
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "put",
				"path":   c.FullPath(),
				"data":   reqNoOrder,
			},
		},
	})

	if len(reqNoOrder) != 5 {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   "evaluation update request params length must be five items ",
			"module":    "EvaluationUpdate",
			"office_id": officeId,
			"request":   requestId,
			"param": map[string]any{
				"req": map[string]any{
					"method": "put",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = "need five items"
		resp.Code = http.StatusBadRequest
		c.JSON(resp.Code, resp)
		return
	}

	var req []request.EvaluationPutReq
	stages := [5]int{1, 2, 3, 4, 5}
	for _, stage := range stages {
		for _, putReq := range reqNoOrder {
			if putReq.Stage == stage {
				req = append(req, putReq)
			}
		}
	}
	if req == nil || len(req) > 5 || len(req) < 5 {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   "evaluation update request params error items stage number 1~5",
			"module":    "EvaluationUpdate",
			"office_id": officeId,
			"request":   requestId,
			"param": map[string]any{
				"req": map[string]any{
					"method": "put",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = "param error items stage number 1~5"
		resp.Code = http.StatusBadRequest
		c.JSON(resp.Code, resp)
		return
	}

	if audit := repository.NewOperateAudit(); audit.AuditOn == enum.OperateAuditON {
		logging.LogFormat(enum.LogInfo, map[string]any{
			"category":  enum.LogCategoryApp,
			"message":   "evaluation update audit check open",
			"module":    "EvaluationUpdate",
			"office_id": officeId,
			"request":   requestId,
			"sync":      audit.AuditSyncOn,
			"param": map[string]any{
				"req": map[string]any{
					"method": "put",
					"path":   c.FullPath(),
				},
			},
		})

		if audit.AuditSyncOn == enum.OperateAuditSyncOn {
			go audit.SaveOperate(model.Evaluation{}.TableName(), officeId, req, "")
		} else {
			audit.SaveOperate(model.Evaluation{}.TableName(), officeId, req, "")
		}
	}

	dbClient, err := client.GetDbClient()
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "evaluation update get rdb connect error",
			"office_id": officeId,
			"module":    "EvaluationUpdate",
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "put",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		resp.Code = enum.DBConnErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}
	handle := repository.EvaluationRepos{ConnMySQL: dbClient}

	// create and update
	data, err := handle.UpdateEvaluation(req, officeId)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "evaluation update db data error",
			"office_id": officeId,
			"module":    "EvaluationUpdate",
			"request":   requestId,
			"err":       err,
			"data":      req,
			"param": map[string]any{
				"req": map[string]any{
					"method": "put",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Msg = err.Error()
		if resp.Msg == "data not change" {
			resp.Code = http.StatusBadRequest
		} else {
			resp.Code = exception.StatusCode(err)
		}
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	resp.Msg = "ok"
	resp.Code = 200
	resp.Transfer(data)

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "evaluation update response log",
		"module":    "EvaluationUpdate",
		"office_id": officeId,
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "put",
				"path":   c.FullPath(),
				"data":   reqNoOrder,
			},
		},
		"response": resp,
	})

	c.JSON(resp.Code, resp)
}

func (rec *Evaluation) Delete(c *gin.Context) {
	var resp response.EvaluationDelResp
	var req request.EvaluationDelReq
	if err := c.ShouldBind(&req); err != nil {
		resp.Msg = err.Error()
		resp.Code = http.StatusInternalServerError
		c.JSON(resp.Code, resp)
		return
	}

	officeId, err := repository.GetOfficeId(c)
	if err != nil {
		resp.Msg = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	dbClient, err := client.GetDbClient()
	if err != nil {
		resp.Msg = err.Error()
		resp.Code = http.StatusInternalServerError
		c.JSON(resp.Code, resp)
		return
	}
	err = dbClient.Where("office_id = ? AND id = ?", officeId, req.ID).Delete(&model.Evaluation{}).Error
	if err != nil {
		resp.Msg = err.Error()
		resp.Code = http.StatusInternalServerError
		c.JSON(resp.Code, resp)
		return
	}

	resp.Msg = "ok"
	resp.Code = 200
	c.JSON(resp.Code, resp)
}
