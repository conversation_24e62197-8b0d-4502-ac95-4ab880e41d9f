package v1

import (
	"mi-restful-api/router"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestPing(t *testing.T) {
	// 设置路由
	r := gin.Default()
	router.Api(r)
	// 创建模拟请求
	req := httptest.NewRequest("GET", "/ping", nil)
	w := httptest.NewRecorder()

	// call server
	r.ServeHTTP(w, req)
	//fmt.Println(w.Body)
	// 验证响应
	assert.Equal(t, http.StatusOK, w.Code)
}
