package v1

import (
	"bytes"
	"mi-restful-api/enum"
	"mi-restful-api/router"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestGetQuestionnaire(t *testing.T) {
	// 设置路由
	r := gin.Default()
	router.Api(r)
	// 创建模拟请求
	req := httptest.NewRequest("GET", "/web/questionnaire?caddy_id=0&start_date=2024-08-01&end_date=2024-09-26&weekday=3&limit=10", nil)
	req.Header.Set("Authorization", enum.WebMockAuthToken)
	w := httptest.NewRecorder()

	// call server
	r.ServeHTTP(w, req)

	// 测试到数据库链接错误
	assert.Equal(t, http.StatusInternalServerError, w.Code)
	assert.Equal(t, `{"message":"invalid db","code":601,"data":null,"current_page":0,"from":0,"to":0,"last_page":0,"per_page":0,"total":0}`, w.Body.String())
}

func TestCartInfo(t *testing.T) {
	// 设置路由
	r := gin.Default()
	router.Api(r)
	// 创建模拟请求
	req := httptest.NewRequest("GET", "/app/cartinfo?id=120", nil)
	req.Header.Set("token", enum.MockAuthToken)
	req.Header.Set("device-id", enum.MockAuthToken)
	w := httptest.NewRecorder()

	// call server
	r.ServeHTTP(w, req)

	// 测试到数据库链接错误
	assert.Equal(t, http.StatusInternalServerError, w.Code)
	assert.Contains(t, `{"message":"operation error DynamoDB: Query`, w.Body.String())
}

func TestStore(t *testing.T) {
	// 设置路由
	r := gin.Default()
	router.Api(r)
	// 创建模拟请求
	data := `{
		"cart_no":"24",
		"caddy_id":"2222",
		"caddy_name":"sikamalu",
		"played_date":"2024-07-21",
		"start_time":"07:22",
		"start_course":"east 02",
		"player_name":"four player",
		"player_id":"player-id",
		"sort_key":"serial",
		"survey":[
			{
				"id":1,
				"answer":3
			},
			{
				"id":2,
				"answer":2
			},
			{
				"id":3,
				"answer":4
			},
			{
				"id":4,
				"answer":3
			},
			{
				"id":5,
				"answer":5
			}
		]
	}`
	buf := bytes.NewBuffer(nil)
	buf.WriteString(data)

	req := httptest.NewRequest("POST", "/app/answer", buf)
	req.Header.Set("token", enum.MockAuthToken)
	req.Header.Set("device-id", enum.MockAuthToken)
	w := httptest.NewRecorder()

	// call server
	r.ServeHTTP(w, req)

	// 测试到数据库链接错误
	assert.Equal(t, http.StatusInternalServerError, w.Code)
	assert.Equal(t, `{"message":"invalid db","code":601,"id":0}`, w.Body.String())
}
