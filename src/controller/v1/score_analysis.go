package v1

import (
	"fmt"
	"log/slog"
	"mi-restful-api/request"
	"mi-restful-api/utils/dynamo"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"github.com/gin-gonic/gin"
)

type ScoreAnalysis struct {
	TableName string
	Pk        string
}

func (rec *ScoreAnalysis) Index(c *gin.Context) {

	var lastEvaluatedKey map[string]types.AttributeValue
	sortKey := strings.Trim(c.Query("sort_key"), " ")
	officeId := strings.Trim(c.Query("office_id"), " ")
	if sortKey != "" {
		lastEvaluatedKey = map[string]types.AttributeValue{}
		lastEvaluatedKey["office_id"] = &types.AttributeValueMemberS{Value: officeId}
		lastEvaluatedKey["sort_key"] = &types.AttributeValueMemberS{Value: sortKey}
	}

	var pageSize int32 = 1

	input := &dynamodb.QueryInput{
		TableName:              aws.String("score"),
		KeyConditionExpression: aws.String("office_id = :office_id and begins_with(sort_key, :prefix)"),
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":office_id": &types.AttributeValueMemberS{Value: officeId},
			":prefix":    &types.AttributeValueMemberS{Value: "analysis_2024"},
		},
		ScanIndexForward:  aws.Bool(true),      // 昇順
		Limit:             aws.Int32(pageSize), // 1ページあたりのレコード数を設定
		ExclusiveStartKey: lastEvaluatedKey,    // ページング開始点
	}
	slog.Info("office_id:", "", c.Query("office_id"))
	result, err := dynamo.Query(input)
	if err != nil {
		slog.Info(err.Error())
		return
	}

	c.JSON(200, gin.H{
		"data": result,
	})
}

func (rec *ScoreAnalysis) Store(c *gin.Context) {
	var req request.Score
	if err := c.ShouldBindBodyWithJSON(&req); err != nil {
		slog.Info(err.Error())
		return
	}
	// 追加データを作成する
	item := map[string]types.AttributeValue{
		"office_id":   &types.AttributeValueMemberS{Value: req.OfficeID},
		"sort_key":    &types.AttributeValueMemberS{Value: rec.getSortKeyMonthPrefix()},
		"created_at":  &types.AttributeValueMemberS{Value: time.Now().Format(time.RFC3339)},
		"id":          &types.AttributeValueMemberS{Value: "uuid"},
		"serial":      &types.AttributeValueMemberS{Value: req.Serial},
		"caddie_id":   &types.AttributeValueMemberS{Value: req.CaddyId},
		"caddie_name": &types.AttributeValueMemberS{Value: req.CaddyName},
		"detail":      &types.AttributeValueMemberS{Value: ""},
	}
	execute, err := dynamo.PutItem("score", item)
	if err != nil {
		slog.Info(err.Error())
		return
	}

	c.JSON(200, gin.H{
		"question": "create",
		"operate":  execute,
	})
}

func (rec *ScoreAnalysis) getSortKeyMonthPrefix() string {
	// 月別で統計 sort_key analysis_{yyyyMM}
	now := time.Now()
	//return "analysis_202406"
	return fmt.Sprintf("analysis_%s", now.Format("200601"))
}

// GetDataByCondition query question analysis
func (rec *ScoreAnalysis) GetDataByCondition(c *gin.Context) {
	var req request.MultiQueryParam
	if err := c.ShouldBind(&req); err != nil {
		slog.Info(err.Error())
		return
	}
	officeId := req.OfficeID

	var lastEvaluatedKey map[string]types.AttributeValue
	if req.SortKey != "" {
		lastEvaluatedKey = map[string]types.AttributeValue{}
		lastEvaluatedKey["office_id"] = &types.AttributeValueMemberS{Value: officeId}
		lastEvaluatedKey["sort_key"] = &types.AttributeValueMemberS{Value: getAnalysisSortKeyPrefix(&req)}
	}

	slog.Info("GetDataByCondition:", "", req)
	var pageSize int32 = 20 // max question number is 20 , chart ui not pages

	input := &dynamodb.QueryInput{
		TableName:              aws.String("score"),
		KeyConditionExpression: aws.String("office_id = :office_id and begins_with(sort_key, :prefix)"),
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":office_id": &types.AttributeValueMemberS{Value: officeId},
			":prefix":    &types.AttributeValueMemberS{Value: "analysis_2024"},
		},
		ScanIndexForward:  aws.Bool(true),      // 昇順
		Limit:             aws.Int32(pageSize), // 1ページあたりのレコード数を設定
		ExclusiveStartKey: lastEvaluatedKey,    // ページング開始点
	}
	slog.Info("office_id:", "", c.Query("office_id"))
	result, err := dynamo.Query(input)
	if err != nil {
		slog.Info(err.Error())
		return
	}

	c.JSON(200, gin.H{
		"data": result,
	})
}
func getAnalysisSortKeyPrefix(req *request.MultiQueryParam) string {
	queryPrefix := "analysis_"
	if req.CaddyId != "" { // プレイヤーによる検索
		queryPrefix += req.CaddyId
	}
	if req.DayType != "" {
		queryPrefix = fmt.Sprintf("%s_%s", queryPrefix, req.DayType)
	}
	queryPrefix = fmt.Sprintf("%s_%s%s", queryPrefix, req.Year, req.Month)

	return queryPrefix
}
