package v1

import (
	"fmt"
	"log"
	"log/slog"
	"mi-restful-api/request"
	"mi-restful-api/utils/dynamo"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/expression"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

type ScoreAnalysisList struct {
	TableName string
	Pk        string
}

func (rec *ScoreAnalysisList) Index(c *gin.Context) {
	var page = 0
	var evaluated map[string]types.AttributeValue
	var count int32 = 0
	for {
		result, err := indexListPos(c.Query("office_id"), c.Que<PERSON>("serial"), evaluated)
		if err != nil {
			slog.Info(err.Error())
			return
		}

		count = count + result.Count

		if result.LastEvaluatedKey == nil {
			c.JSON(200, gin.H{
				"data":  result,
				"count": count,
			})
			return
		}
		page++
		evaluated = result.LastEvaluatedKey
		if page == 10 {
			fmt.Printf("%#v", evaluated)
			break
		}
	}

	result, err := indexListPosData(c.Query("office_id"), c.Query("serial"), evaluated)
	if err != nil {
		slog.Info(err.Error())
		return
	}
	c.JSON(200, gin.H{
		"data":   result,
		"count":  count,
		"result": result.Items,
	})
	return

	tableName := "score"
	startDate := "list_20240601"
	endDate := "list_20240711"

	// Construct the key condition expression
	keyCondition := expression.Key("office_id").Equal(expression.Value(c.Query("office_id"))).
		And(expression.Key("sort_key").Between(expression.Value(startDate+"_"),
			expression.Value(endDate+"_~")))

	// Build the filter expression
	var filterCond expression.ConditionBuilder
	if c.Query("serial") != "" {
		filterCond = expression.Name("serial").Equal(expression.Value(c.Query("serial")))
	}

	builder := expression.NewBuilder().
		WithKeyCondition(keyCondition)
	if c.Query("serial") != "" {
		builder.WithFilter(filterCond)
	}
	expr, err := builder.Build()
	if err != nil {
		log.Fatalf("failed to build expression, %v", err)
	}

	queryInput := &dynamodb.QueryInput{
		TableName:                 aws.String(tableName),
		ExpressionAttributeNames:  expr.Names(),
		ExpressionAttributeValues: expr.Values(),
		KeyConditionExpression:    expr.KeyCondition(),
		Select:                    types.SelectCount,
	}

	if c.Query("serial") != "" {
		queryInput.FilterExpression = expr.Filter()
	}

	slog.Info("office_id:", "", c.Query("office_id"))
	result, err = dynamo.Query(queryInput)
	if err != nil {
		slog.Info(err.Error())
		return
	}

	c.JSON(200, gin.H{
		"data": result,
	})
}

func indexListPos(officeId, serial string, evaluated map[string]types.AttributeValue) (*dynamodb.QueryOutput, error) {
	tableName := "score"
	startDate := "score_20240713"
	endDate := "score_20240713"

	// Construct the key condition expression
	keyCondition := expression.Key("office_id").Equal(expression.Value(officeId)).
		And(expression.Key("sort_key").Between(expression.Value(startDate+"_"),
			expression.Value(endDate+"_~")))

	// Build the filter expression
	var filterCond expression.ConditionBuilder
	if serial != "" {
		filterCond = expression.Name("serial").Equal(expression.Value(serial))
	}

	builder := expression.NewBuilder().
		WithKeyCondition(keyCondition)
	if serial != "" {
		builder.WithFilter(filterCond)
	}
	expr, err := builder.Build()
	if err != nil {
		log.Fatalf("failed to build expression, %v", err)
	}

	queryInput := &dynamodb.QueryInput{
		TableName:                 aws.String(tableName),
		ExpressionAttributeNames:  expr.Names(),
		ExpressionAttributeValues: expr.Values(),
		KeyConditionExpression:    expr.KeyCondition(),
		Select:                    types.SelectCount,
		ExclusiveStartKey:         evaluated,
		Limit:                     aws.Int32(100),
	}

	if serial != "" {
		queryInput.FilterExpression = expr.Filter()
	}

	slog.Info("office_id:", "", officeId)
	return dynamo.Query(queryInput)
}

func indexListPosData(officeId, serial string, evaluated map[string]types.AttributeValue) (*dynamodb.QueryOutput, error) {
	tableName := "score"
	startDate := "list_20240601"
	endDate := "list_20240711"

	// Construct the key condition expression
	keyCondition := expression.Key("office_id").Equal(expression.Value(officeId)).
		And(expression.Key("sort_key").Between(expression.Value(startDate+"_"),
			expression.Value(endDate+"_~")))

	// Build the filter expression
	var filterCond expression.ConditionBuilder
	if serial != "" {
		filterCond = expression.Name("serial").Equal(expression.Value(serial))
	}

	builder := expression.NewBuilder().
		WithKeyCondition(keyCondition)
	if serial != "" {
		builder.WithFilter(filterCond)
	}
	expr, err := builder.Build()
	if err != nil {
		log.Fatalf("failed to build expression, %v", err)
	}

	queryInput := &dynamodb.QueryInput{
		TableName:                 aws.String(tableName),
		ExpressionAttributeNames:  expr.Names(),
		ExpressionAttributeValues: expr.Values(),
		KeyConditionExpression:    expr.KeyCondition(),
		ExclusiveStartKey:         evaluated,
		Limit:                     aws.Int32(20),
	}

	if serial != "" {
		queryInput.FilterExpression = expr.Filter()
	}

	slog.Info("office_id:", "serial", serial)
	return dynamo.Query(queryInput)
}

func (rec *ScoreAnalysisList) Store(c *gin.Context) {
	var req request.ScoreList
	if err := c.ShouldBindBodyWithJSON(&req); err != nil {
		slog.Info(err.Error())
		return
	}
	// 追加データを作成
	item := map[string]types.AttributeValue{
		"office_id":   &types.AttributeValueMemberS{Value: req.OfficeID},
		"sort_key":    &types.AttributeValueMemberS{Value: rec.getSortKeyMonthPrefix(req.CaddyId, req.Serial)},
		"created_at":  &types.AttributeValueMemberS{Value: time.Now().Format(time.RFC3339)},
		"id":          &types.AttributeValueMemberS{Value: uuid.NewString()},
		"serial":      &types.AttributeValueMemberS{Value: req.Serial},
		"cart_no":     &types.AttributeValueMemberS{Value: req.CartNo},
		"caddie_id":   &types.AttributeValueMemberS{Value: req.CaddyId},
		"caddie_name": &types.AttributeValueMemberS{Value: req.CaddyName},
		"player_id":   &types.AttributeValueMemberS{Value: req.PlayerId},
		"player_name": &types.AttributeValueMemberS{Value: req.PlayerName},
		"detail":      &types.AttributeValueMemberS{Value: req.Detail},
	}
	execute, err := dynamo.PutItem("score", item)
	if err != nil {
		slog.Info(err.Error())
		return
	}

	c.JSON(200, gin.H{
		"question": "create",
		"operate":  execute,
	})
}

func (rec *ScoreAnalysisList) getSortKeyMonthPrefix(caddyId, serial string) string {
	// 月別で統計 sort_key analysis_{yyyyMM}
	now := time.Now()
	//return "analysis_202406"
	return fmt.Sprintf("list_%s_%s_%s_%s", now.Format("20060102"), caddyId, serial, uuid.NewString())
}

// GetDataByCondition query question analysis
func (rec *ScoreAnalysisList) GetDataByCondition(c *gin.Context) {
	var req request.MultiQueryParam
	if err := c.ShouldBind(&req); err != nil {
		slog.Info(err.Error())
		return
	}
	officeId := req.OfficeID

	var lastEvaluatedKey map[string]types.AttributeValue
	if req.SortKey != "" {
		lastEvaluatedKey = map[string]types.AttributeValue{}
		lastEvaluatedKey["office_id"] = &types.AttributeValueMemberS{Value: officeId}
		lastEvaluatedKey["sort_key"] = &types.AttributeValueMemberS{Value: getAnalysisSortKeyPrefix(&req)}
	}

	slog.Info("GetDataByCondition", "req", req)
	var pageSize int32 = 20 // max question number is 20 , chart ui not pages

	input := &dynamodb.QueryInput{
		TableName:              aws.String("score"),
		KeyConditionExpression: aws.String("office_id = :office_id and begins_with(sort_key, :prefix)"),
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":office_id": &types.AttributeValueMemberS{Value: officeId},
			":prefix":    &types.AttributeValueMemberS{Value: "analysis_2024"},
		},
		ScanIndexForward:  aws.Bool(true),      // 昇順
		Limit:             aws.Int32(pageSize), // 1ページあたりのレコード数を設定
		ExclusiveStartKey: lastEvaluatedKey,    // ページング開始点
	}
	slog.Info("office_id:", "", c.Query("office_id"))
	result, err := dynamo.Query(input)
	if err != nil {
		slog.Info(err.Error())
		return
	}

	c.JSON(200, gin.H{
		"data": result,
	})
}
