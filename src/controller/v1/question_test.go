package v1

import (
	"bytes"
	"mi-restful-api/enum"
	"mi-restful-api/router"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestAppGetQuestion(t *testing.T) {
	// 设置路由
	r := gin.Default()
	router.Api(r)
	// 创建模拟请求
	req := httptest.NewRequest("GET", "/app/question", nil)
	req.Header.Set("token", enum.MockAuthToken)
	req.Header.Set("device-id", enum.MockAuthToken)
	w := httptest.NewRecorder()

	// call server
	r.ServeHTTP(w, req)

	// 测试到数据库链接错误
	assert.Equal(t, http.StatusInternalServerError, w.Code)
	assert.Equal(t, `{"message":"mysql conn error","code":601,"data":null}`, w.Body.String())
}

func TestWebGetQuestion(t *testing.T) {
	// 设置路由
	r := gin.Default()
	router.Api(r)
	// 创建模拟请求
	req := httptest.NewRequest("GET", "/web/question", nil)
	req.Header.Set("Authorization", enum.WebMockAuthToken)
	w := httptest.NewRecorder()

	// call server
	r.ServeHTTP(w, req)

	// 测试到数据库链接错误
	assert.Equal(t, http.StatusInternalServerError, w.Code)
	assert.Equal(t, `{"message":"mysql conn error","code":601,"data":null}`, w.Body.String())
}

func TestWebStoreQuestion(t *testing.T) {
	// 设置路由
	r := gin.Default()
	router.Api(r)
	// 创建模拟请求
	data := `{
	  "content": "create question 5 0059213222",
	  "require": 1,
	  "type": 1
	}`
	buf := bytes.NewBuffer(nil)
	buf.WriteString(data)
	req := httptest.NewRequest("POST", "/web/question", buf)
	req.Header.Set("Authorization", enum.WebMockAuthToken)
	w := httptest.NewRecorder()

	// call server
	r.ServeHTTP(w, req)

	// 测试到数据库链接错误
	assert.Equal(t, http.StatusInternalServerError, w.Code)
	assert.Equal(t, `{"message":"invalid db","code":601,"id":0}`, w.Body.String())
}

func TestWebPutQuestion(t *testing.T) {
	// 设置路由
	r := gin.Default()
	router.Api(r)
	// 创建模拟请求
	data := `{
		"content":"this is content update id 2",
		"require": 2
	}`
	buf := bytes.NewBuffer(nil)
	buf.WriteString(data)
	req := httptest.NewRequest("PUT", "/web/question/1", buf)
	req.Header.Set("Authorization", enum.WebMockAuthToken)
	w := httptest.NewRecorder()

	// call server
	r.ServeHTTP(w, req)

	// 测试到数据库链接错误
	assert.Equal(t, http.StatusInternalServerError, w.Code)
	assert.Equal(t, `{"message":"invalid db","code":601,"data":null}`, w.Body.String())
}

func TestWebPutQuestionIndex(t *testing.T) {
	// 设置路由
	r := gin.Default()
	router.Api(r)
	// 创建模拟请求
	data := `{
		"id":5,
		"index":4
	}`
	buf := bytes.NewBuffer(nil)
	buf.WriteString(data)
	req := httptest.NewRequest("PUT", "/web/questionindex", buf)
	req.Header.Set("Authorization", enum.WebMockAuthToken)
	w := httptest.NewRecorder()

	// call server
	r.ServeHTTP(w, req)

	// 测试到数据库链接错误
	assert.Equal(t, http.StatusInternalServerError, w.Code)
	assert.Equal(t, `{"message":"invalid db","code":601,"data":null}`, w.Body.String())
}

func TestWebDeleteQuestion(t *testing.T) {
	// 设置路由
	r := gin.Default()
	router.Api(r)
	// 创建模拟请求
	req := httptest.NewRequest("DELETE", "/web/question/1", nil)
	req.Header.Set("Authorization", enum.WebMockAuthToken)
	w := httptest.NewRecorder()

	// call server
	r.ServeHTTP(w, req)

	// 测试到数据库链接错误
	assert.Equal(t, http.StatusInternalServerError, w.Code)
	assert.Equal(t, `{"message":"invalid db","code":601}`, w.Body.String())
}
