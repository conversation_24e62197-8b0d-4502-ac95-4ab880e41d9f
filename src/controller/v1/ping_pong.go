package v1

import (
	"mi-restful-api/response"
	"os"

	"github.com/gin-gonic/gin"
)

// @Summary server ping
// @Description get ping
// @ID get-string
// @Produce  json
// @Success 200 {object} response.Ping "pong"
// @Router /ping [get]
// @Tags Ping
func Ping(c *gin.Context) {
	var resp response.Ping
	resp.Message = "pong"
	resp.Version = os.Getenv("GIT_COMMIT_ID")
	resp.AWSConfig = len(os.Getenv("AWS_ACCESS_KEY_ID")) > 0
	c.JSON(200, resp)
}
