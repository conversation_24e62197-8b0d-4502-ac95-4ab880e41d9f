package v1

import (
	"mi-restful-api/client"
	"mi-restful-api/enum"
	"mi-restful-api/exception"
	"mi-restful-api/logging"
	"mi-restful-api/repository"
	"mi-restful-api/response"
	"mi-restful-api/utils/common"
	"net/http"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Caddy struct {
}

// @Summary キャディ一覧
// @Description キャディ一覧
// @ID get-caddy-index
// @Produce  json
// @Success 200 {object} response.CaddyIndexResp "response"
// @Failure 400 {object} response.CaddyIndexResp "Bad Request ,param error"
// @Failure 401 {object} response.CaddyIndexResp "Unauthorized"
// @Failure 404 {object} response.CaddyIndexResp "Not Found"
// @Failure 500 {object} response.CaddyIndexResp "Internal Server Error"
// @Failure 601 {object} response.CaddyIndexResp "mysql conn error"
// @Failure 602 {object} response.CaddyIndexResp "mysql sql error"
// @Router /web/caddylist [get]
// @Tags Caddy
func (svc *Caddy) Index(c *gin.Context) {
	var resp response.CaddyIndexResp
	requestId := common.GetRequestId(c)

	officeId, err := repository.GetOfficeId(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryAuth,
			"message":   "caddy list token parse msg error",
			"office_id": officeId,
			"module":    "CaddyIndex",
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Message = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "caddy list access log",
		"module":    "CaddyIndex",
		"office_id": officeId,
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "get",
				"path":   c.FullPath(),
			},
		},
	})

	db, err := client.GetDbClient()
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "caddy list get db client error",
			"module":    "CaddyIndex",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Code = enum.DBConnErrorCode
		resp.Message = err.Error()
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	handle := repository.CaddyRepos{ConnMySQL: db}
	data, err := handle.CaddyFind(officeId)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "caddy list get answer info from db error",
			"module":    "CaddyIndex",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Message = err.Error()
		if err == gorm.ErrRecordNotFound {
			resp.Code = http.StatusNoContent
			c.JSON(resp.Code, resp)
			return
		} else {
			resp.Code = enum.DBSQLEXECErrorCode
			c.JSON(http.StatusInternalServerError, resp)
			return
		}
	}

	if len(data) < 1 {
		resp.Transfer(data)
		resp.Message = "no data"
		resp.Code = http.StatusNoContent
		c.JSON(http.StatusNoContent, resp)
		return
	}

	resp.Transfer(data)
	resp.Message = "ok"
	resp.Code = 200

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "caddy list response log",
		"module":    "CaddyIndex",
		"office_id": officeId,
		"request":   requestId,
		"param": map[string]any{
			"req": map[string]any{
				"method": "get",
				"path":   c.FullPath(),
			},
		},
		"response": resp,
	})

	c.JSON(http.StatusOK, resp)
}

// @Summary キャディ評価機能有効
// @Description キャディ評価機能有効
// @ID get-caddy_on-index
// @Produce  json
// @Success 200 {object} response.CaddyOnResp "response"
// @Failure 400 {object} response.CaddyOnResp "Bad Request ,param error"
// @Failure 401 {object} response.CaddyOnResp "Unauthorized"
// @Failure 404 {object} response.CaddyOnResp "Not Found"
// @Failure 500 {object} response.CaddyOnResp "Internal Server Error"
// @Failure 601 {object} response.CaddyOnResp "mysql conn error"
// @Failure 602 {object} response.CaddyOnResp "mysql sql error"
// @Router /app/caddyon [get]
// @Tags Caddy
func (svc *Caddy) CaddyOn(c *gin.Context) {
	var resp response.CaddyOnResp
	requestId := common.GetRequestId(c)
	officeId, err := repository.GetOfficeId(c)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryAuth,
			"message":   "caddy on token parse msg error",
			"office_id": officeId,
			"module":    "CaddyOn",
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Message = err.Error()
		resp.Code = exception.StatusCode(err)
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "caddy on access log",
		"module":    "CaddyOn",
		"office_id": officeId,
		"request":   requestId,
		"device-id": c.GetHeader("device-id"),
		"param": map[string]any{
			"req": map[string]any{
				"method": "get",
				"path":   c.FullPath(),
			},
		},
	})

	db, err := client.GetMncDBClient()
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "caddy on get db client error",
			"module":    "CaddyOn",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Message = err.Error()
		resp.Code = enum.DBConnErrorCode
		c.JSON(http.StatusInternalServerError, resp)
		return
	}
	handle := repository.CaddyRepos{ConnMySQLMncdb: db}
	switchStatusTags, err := handle.CaddyOn(officeId, requestId)
	if err != nil {
		logging.LogFormat(enum.LogError, map[string]any{
			"category":  enum.LogCategoryRDS,
			"message":   "caddy on get restriction_tags info from db error",
			"module":    "CaddyOn",
			"office_id": officeId,
			"request":   requestId,
			"err":       err,
			"param": map[string]any{
				"req": map[string]any{
					"method": "get",
					"path":   c.FullPath(),
				},
			},
		})

		resp.Message = err.Error()
		if exception.StatusCode(err) == http.StatusNoContent {
			resp.Code = http.StatusNoContent
			c.JSON(http.StatusNoContent, resp)
			return
		} else {
			resp.Code = enum.DBSQLEXECErrorCode
			c.JSON(http.StatusInternalServerError, resp)
			return
		}
	}

	resp.Transfer(switchStatusTags)
	resp.Message = "ok"
	resp.Code = 200

	logging.LogFormat(enum.LogInfo, map[string]any{
		"category":  enum.LogCategoryApp,
		"message":   "caddy on response log",
		"module":    "CaddyOn",
		"office_id": officeId,
		"request":   requestId,
		"device-id": c.GetHeader("device-id"),
		"param": map[string]any{
			"req": map[string]any{
				"method": "get",
				"path":   c.FullPath(),
			},
		},
		"response": resp,
	})

	c.JSON(http.StatusOK, resp)
}
