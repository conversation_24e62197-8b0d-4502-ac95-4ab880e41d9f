{"containerDefinitions": [{"name": "mi-restful-api-service", "image": "750201609216.dkr.ecr.ap-northeast-1.amazonaws.com/mi-restful-api:a21d39bb898e5e71cd7085ee92a278568a4ad28c", "cpu": 0, "portMappings": [{"name": "mi-restful-api-service-9004-tcp", "containerPort": 9004, "hostPort": 9004, "protocol": "tcp"}], "essential": true, "environment": [], "secrets": [{"name": "REDIS_DB", "valueFrom": "/mi-restful-api/dev/env/REDIS_DB"}, {"name": "GET_GOLFER_DATA_TOKEN", "valueFrom": "/mi-restful-api/dev/env/GET_GOLFER_DATA_TOKEN"}, {"name": "PHP_SESSION_REDIS_HOST", "valueFrom": "/mi-restful-api/dev/env/PHP_SESSION_REDIS_HOST"}, {"name": "PHP_SESSION_REDIS_DB", "valueFrom": "/mi-restful-api/dev/env/PHP_SESSION_REDIS_DB"}, {"name": "COURSE_RATE_SEARCH_TOKEN", "valueFrom": "/mi-restful-api/dev/env/COURSE_RATE_SEARCH_TOKEN"}, {"name": "PHP_SESSION_REDIS_POOL_SIZE", "valueFrom": "/mi-restful-api/dev/env/PHP_SESSION_REDIS_POOL_SIZE"}, {"name": "REDIS_POOL_SIZE", "valueFrom": "/mi-restful-api/dev/env/REDIS_POOL_SIZE"}, {"name": "SECRET_KEY", "valueFrom": "/mi-restful-api/dev/env/SECRET_KEY"}, {"name": "MYSQL_MNCDB_CONN", "valueFrom": "/mi-restful-api/dev/env/MYSQL_MNCDB_CONN"}, {"name": "GET_GOLFER_DATA", "valueFrom": "/mi-restful-api/dev/env/GET_GOLFER_DATA"}, {"name": "MYSQL_CONN", "valueFrom": "/mi-restful-api/dev/env/MYSQL_CONN"}, {"name": "APP_REDIS_MODE", "valueFrom": "/mi-restful-api/dev/env/APP_REDIS_MODE"}, {"name": "REDIS_PORT", "valueFrom": "/mi-restful-api/dev/env/REDIS_PORT"}, {"name": "PHP_SESSION_REDIS_PASSWORD", "valueFrom": "/mi-restful-api/dev/env/PHP_SESSION_REDIS_PASSWORD"}, {"name": "SHARED_URL", "valueFrom": "/mi-restful-api/dev/env/SHARED_URL"}, {"name": "APP_REDIS_HOST", "valueFrom": "/mi-restful-api/dev/env/APP_REDIS_HOST"}, {"name": "APP_REDIS_POOL_SIZE", "valueFrom": "/mi-restful-api/dev/env/APP_REDIS_POOL_SIZE"}, {"name": "REDIS_PASSWORD", "valueFrom": "/mi-restful-api/dev/env/REDIS_PASSWORD"}, {"name": "MOCK_OFFICE_KEY", "valueFrom": "/mi-restful-api/dev/env/MOCK_OFFICE_KEY"}, {"name": "APP_LOG_LEVEL", "valueFrom": "/mi-restful-api/dev/env/APP_LOG_LEVEL"}, {"name": "REDIS_HOST", "valueFrom": "/mi-restful-api/dev/env/REDIS_HOST"}, {"name": "APP_S3_BUCKET", "valueFrom": "/mi-restful-api/dev/env/APP_S3_BUCKET"}, {"name": "API_PREFIX", "valueFrom": "/mi-restful-api/dev/env/API_PREFIX"}, {"name": "MOCK_OFFICE_ID", "valueFrom": "/mi-restful-api/dev/env/MOCK_OFFICE_ID"}, {"name": "ACCESS_TOKEN_TTL", "valueFrom": "/mi-restful-api/dev/env/ACCESS_TOKEN_TTL"}, {"name": "PHP_SESSION_REDIS_PORT", "valueFrom": "/mi-restful-api/dev/env/PHP_SESSION_REDIS_PORT"}, {"name": "DYNAMODB_MASHIAI_TABLE_NAME", "valueFrom": "/mi-restful-api/dev/env/DYNAMODB_MASHIAI_TABLE_NAME"}, {"name": "DEPLOY_MODE", "valueFrom": "/mi-restful-api/dev/env/DEPLOY_MODE"}, {"name": "APP_S3_PROMOTIONAL_PREFIX", "valueFrom": "/mi-restful-api/dev/env/APP_S3_PROMOTIONAL_PREFIX"}, {"name": "GOLFER_SEARCH", "valueFrom": "/mi-restful-api/dev/env/GOLFER_SEARCH"}, {"name": "DYNAMODB_COMPE_TABLE_NAME", "valueFrom": "/mi-restful-api/dev/env/DYNAMODB_COMPE_TABLE_NAME"}, {"name": "APP_REDIS_DB", "valueFrom": "/mi-restful-api/dev/env/APP_REDIS_DB"}, {"name": "ACCESS_CONTROL_ALLOW_DOMAIN", "valueFrom": "/mi-restful-api/dev/env/ACCESS_CONTROL_ALLOW_DOMAIN"}, {"name": "GOLFER_SEARCH_TOKEN", "valueFrom": "/mi-restful-api/dev/env/GOLFER_SEARCH_TOKEN"}, {"name": "APP_REDIS_PASSWORD", "valueFrom": "/mi-restful-api/dev/env/APP_REDIS_PASSWORD"}, {"name": "EMAIL_AWS_REGION", "valueFrom": "/mi-restful-api/dev/env/EMAIL_AWS_REGION"}, {"name": "APP_REDIS_PORT", "valueFrom": "/mi-restful-api/dev/env/APP_REDIS_PORT"}, {"name": "REFRESH_TOKEN_TTL", "valueFrom": "/mi-restful-api/dev/env/REFRESH_TOKEN_TTL"}, {"name": "COURSE_RATE_SEARCH", "valueFrom": "/mi-restful-api/dev/env/COURSE_RATE_SEARCH"}, {"name": "APP_PORT", "valueFrom": "/mi-restful-api/dev/env/APP_PORT"}, {"name": "EMAIL_SOURCE", "valueFrom": "/mi-restful-api/dev/env/EMAIL_SOURCE"}], "mountPoints": [], "volumesFrom": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/mi-restful-api-service", "awslogs-create-group": "true", "awslogs-region": "ap-northeast-1", "awslogs-stream-prefix": "ecs"}}, "systemControls": []}], "family": "mi-restful-api-service", "taskRoleArn": "arn:aws:iam::750201609216:role/mi-dev-task-api-role", "executionRoleArn": "arn:aws:iam::750201609216:role/mi-dev-task-api-role", "networkMode": "awsvpc", "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "cpu": "256", "memory": "512", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}, "tags": [{"key": "IaC_Service", "value": "mi_restful_api"}]}