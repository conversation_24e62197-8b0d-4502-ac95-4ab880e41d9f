.PHONY: build

VERSION=v0.0.23
REGION=ap-northeast-1
HOST=750201609216.dkr.ecr.ap-northeast-1.amazonaws.com
PROJECTNAME=mi-restful-api

build:
	docker build --platform linux/amd64 -t $(PROJECTNAME):$(VERSION) .

clean:
	docker rmi $(PROJECTNAME):$(VERSION)

run:
	docker run -ti --rm $(PROJECTNAME):$(VERSION) /bin/sh

tag:
	docker tag $(PROJECTNAME):$(VERSION) $(HOST)/$(PROJECTNAME):$(VERSION)

deploy:
	docker push $(HOST)/$(PROJECTNAME):$(VERSION)

deploy_pub:
	docker tag $(PROJECTNAME):$(VERSION) $(HOST)/$(PROJECTNAME):$(VERSION)
	docker push $(HOST)/$(PROJECTNAME):$(VERSION)

login:
	aws ecr get-login-password --region $(REGION) | docker login --username AWS --password-stdin $(HOST)

