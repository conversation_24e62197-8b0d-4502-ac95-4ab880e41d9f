DELIMITER //

CREATE PROCEDURE GenerateTestData(
    IN p_officeId VARCHAR(64),
    IN p_numRecords INT,
    IN p_startDate DATETIME,
    IN p_endDate DATETIME
)
BEGIN
    DECLARE v_diffDays INT;
    DECLARE v_incrementDays INT;
    DECLARE v_counter INT DEFAULT 0;
    DECLARE v_randomEvalId BIGINT;
    DECLARE v_questionnaireId BIGINT;
    DECLARE v_playerId BIGINT;
    DECLARE v_cartNo INT;
    DECLARE v_caddyId VARCHAR(64);
    DECLARE v_insertDate DATETIME;
    DECLARE v_playerCount INT DEFAULT 60;
    DECLARE v_playerIndex INT;
    DECLARE v_playerCountInArray INT;
    DECLARE v_selectedPlayerId VARCHAR(64);
    DECLARE v_selectedPlayerName VARCHAR(64);
    DECLARE v_serial  VARCHAR(64);
    DECLARE v_questionId BIGINT;

    -- 游标声明必须在所有变量声明之后
    DECLARE v_questionCursor CURSOR FOR 
        SELECT id FROM question WHERE office_id = p_officeId;

    -- 声明退出游标的处理程序
    DECLARE CONTINUE HANDLER FOR NOT FOUND 
        SET v_questionId = NULL;

    -- 创建一个临时表用于存储球员信息
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_players (
        player_id VARCHAR(64),
        player_name VARCHAR(64)
    );

    -- 初始化球员表
    WHILE v_counter < v_playerCount DO
        INSERT INTO temp_players (player_id, player_name) 
        VALUES (UUID(), CONCAT('Player ', v_counter + 1));
        SET v_counter = v_counter + 1;
    END WHILE;
    SET v_counter = 0;

    -- 计算日期区间的天数
    SET v_diffDays = DATEDIFF(p_endDate, p_startDate) + 1;
    SET v_incrementDays = v_diffDays / p_numRecords;

    -- 1. 检查并插入 question 表的数据
    IF (SELECT COUNT(*) FROM question WHERE office_id = p_officeId) = 0 THEN
        WHILE v_counter < 10 DO
            INSERT INTO question (`index`, `type`, `content`, `require`, `created_at`, `office_id`)
            VALUES (v_counter + 1, 1, CONCAT('Test Question ', v_counter + 1), 0, NOW(), p_officeId);
            SET v_counter = v_counter + 1;
        END WHILE;
        SET v_counter = 0;
    END IF;

    -- 2. 检查并插入 evaluation 表的数据
    IF (SELECT COUNT(*) FROM evaluation WHERE office_id = p_officeId) = 0 THEN
        WHILE v_counter < 5 DO
            INSERT INTO evaluation (`stage`, `score`, `content`, `created_at`, `office_id`)
            VALUES (v_counter + 1, 100, CONCAT('Test Evaluation ', v_counter + 1), NOW(), p_officeId);
            SET v_counter = v_counter + 1;
        END WHILE;
        SET v_counter = 0;
    END IF;

    -- 3. 向 caddy 表插入 30 条数据
    WHILE v_counter < 30 DO
        INSERT INTO caddy (`caddy_id`, `caddy_name`, `created_at`, `office_id`)
        VALUES (UUID(), CONCAT('Caddy ', v_counter + 1), NOW(), p_officeId);
        SET v_counter = v_counter + 1;
    END WHILE;
    SET v_counter = 0;

    -- 4. 向 questionnaire 和 player 表插入数据，并关联到 answer 表
    WHILE v_counter < p_numRecords DO
        -- 计算当前插入数据的日期时间
        SET v_insertDate = DATE_ADD(p_startDate, INTERVAL v_incrementDays * v_counter DAY);

        -- 生成随机 cart_no
        SET v_cartNo = FLOOR(100 + RAND() * 100);

        -- 从 caddy 表中随机选择一个 caddy_id
        SELECT caddy_id INTO v_caddyId FROM caddy WHERE office_id = p_officeId ORDER BY RAND() LIMIT 1;

         -- 生成带有日期和UUID的 serial 字段
        SET v_serial = CONCAT('score_', DATE_FORMAT(v_insertDate, '%Y%m%d'), '_', UUID());

        -- 插入 questionnaire 表
        INSERT INTO questionnaire (`serial`, `cart_no`, `caddy_id`, `created_at`, `weekday`, `office_id`, `played_date`, `start_time`, `start_course`)
        VALUES (v_serial, v_cartNo, v_caddyId, v_insertDate, WEEKDAY(v_insertDate), p_officeId, DATE(v_insertDate), '08:00:00', 'Course1');
        
        SET v_questionnaireId = LAST_INSERT_ID();

        -- 每次从球员表中随机选取 3 到 4 个球员
        SET v_playerCountInArray = FLOOR(3 + RAND() * 2);
        WHILE v_playerCountInArray > 0 DO
            SELECT player_id, player_name INTO v_selectedPlayerId, v_selectedPlayerName 
            FROM temp_players 
            ORDER BY RAND() 
            LIMIT 1;

            -- 插入 player 表
            INSERT INTO player (`player_id`, `player_name`, `questionnaire_id`, `office_id`, `created_at`)
            VALUES (v_selectedPlayerId, v_selectedPlayerName, v_questionnaireId, p_officeId, v_insertDate);
            
            SET v_playerId = LAST_INSERT_ID();

            -- 通过游标遍历 question 表中的每个问题，并插入 answer 表
            OPEN v_questionCursor;
            FETCH v_questionCursor INTO v_questionId;

            WHILE v_questionId IS NOT NULL DO
                -- 选择一个随机的 evaluation id
                SELECT id INTO v_randomEvalId FROM evaluation WHERE office_id = p_officeId ORDER BY RAND() LIMIT 1;

                -- 插入 answer 表
                INSERT INTO answer (`player_id`, `question_id`, `comment_id`, `caddy_id`, `weekday`, `created_at`, `office_id`)
                VALUES (v_playerId, v_questionId, v_randomEvalId, v_caddyId, WEEKDAY(v_insertDate), v_insertDate, p_officeId);

                FETCH v_questionCursor INTO v_questionId;
            END WHILE;

            CLOSE v_questionCursor;

            SET v_playerCountInArray = v_playerCountInArray - 1;
        END WHILE;

        SET v_counter = v_counter + 1;
    END WHILE;

    -- 删除临时表
    DROP TEMPORARY TABLE IF EXISTS temp_players;

END //

DELIMITER ;
