version: 2.1

orbs:
  aws-ecr: circleci/aws-ecr@9.2.0
  aws-ecs: circleci/aws-ecs@4.0.0
  aws-cli: circleci/aws-cli@4.1.3

jobs:
  update-swagger-dev:
    docker:
      - image: cimg/go:1.24
    steps:
      - checkout
      - run:
          name: Update Swagger
          command: |
            go install github.com/swaggo/swag/cmd/swag@latest
            cd src
            swag init
            cd docs
            sed -i "s|^host:.*$|host: $API_BASE_URL_DEV|g" swagger.yaml
            attachmentID=`
            curl -u ${ATLASSIAN_USER_EMAIL_DEV}:${ATLASSIAN_API_TOKEN_DEV} \
            -X GET \
            -H "Accept: application/json" \
            "https://${ATLASSIAN_BASE_URL_DEV}/wiki/rest/api/content/${ATLASSIAN_PAGE_ID_DEV}/child/attachment" | jq -r  '.results[] | .id' | tail -1`
            if [ -z "$attachmentID" ]; then
              echo "No attachment found, uploading new attachment"
              result=`curl -X POST \
                -H "Authorization: Basic ${ATLASSIAN_API_TOKEN_BASE64_ENCODED_DEV}" \
                -H "X-Atlassian-Token: no-check" \
                -H "Content-Type: multipart/form-data" \
                -F "file=@swagger.yaml" \
                "https://${ATLASSIAN_BASE_URL_DEV}/wiki/rest/api/content/${ATLASSIAN_PAGE_ID_DEV}/child/attachment"`
              title=`echo $result | jq -r '.title'`
              if [ -z "$title" ]; then
                echo $result
                echo "Failed to upload attachment"
                exit 1
              fi
              echo "Successfully uploaded attachment!"
            else
              echo "Updating attachment swagger.yaml..."
              result=`curl -X POST \
                -H "Authorization: Basic ${ATLASSIAN_API_TOKEN_BASE64_ENCODED_DEV}" \
                -H "X-Atlassian-Token: no-check" \
                -H "Content-Type: multipart/form-data" \
                -F "file=@swagger.yaml" \
                "https://${ATLASSIAN_BASE_URL_DEV}/wiki/rest/api/content/${ATLASSIAN_PAGE_ID_DEV}/child/attachment/${attachmentID}/data"`
              title=`echo $result | jq -r '.title'`
              if [ -z "$title" ]; then
                echo $result
                echo "Failed to update attachment"
                exit 1
              fi
              echo "Successfully updated attachment!"
            fi

workflows:
  dev_build_and_push_image:
    jobs:
      - update-swagger-dev:
          filters:
            branches:
              only:
                - develop
      - aws-ecr/build_and_push_image:
          account_id: ${DEV_AWS_ACCOUNT_ID}
          auth:
            - aws-cli/setup:
                role_arn: ${DEV_AWS_ECR_OIDC_ROLE_ARN}
          create_repo: true
          dockerfile: Dockerfile
          region: "${DEV_AWS_DEFAULT_REGION}"
          repo: "${DEV_APP_PREFIX}"
          extra_build_args: "--provenance=false"
          tag: "$CIRCLE_SHA1"
          filters:
            branches:
              only:
                - develop

      - aws-ecs/deploy_service_update:
          requires:
            - aws-ecr/build_and_push_image
          auth:
            - aws-cli/setup:
                role_arn: ${DEV_AWS_ECR_OIDC_ROLE_ARN}
          region: "${DEV_AWS_DEFAULT_REGION}"
          family: "${DEV_APP_PREFIX}-service"
          cluster: "${DEV_APP_CLUSTER}"
          container_image_name_updates: "container=${DEV_APP_PREFIX}-service,tag=${CIRCLE_SHA1}"
          container_env_var_updates: "container=${DEV_APP_PREFIX}-service,name=GIT_COMMIT_ID,value=${CIRCLE_SHA1}"
          filters:
            branches:
              only:
                - develop

  golf_build_and_push_image:
    jobs:
      - aws-ecr/build_and_push_image:
          account_id: ${GOLF_AWS_ACCOUNT_ID}
          auth:
            - aws-cli/setup:
                role_arn: ${GOLF_AWS_ECR_OIDC_ROLE_ARN}
          create_repo: true
          dockerfile: Dockerfile
          region: "${GOLF_AWS_DEFAULT_REGION}"
          repo: "${GOLF_APP_PREFIX}"
          extra_build_args: "--provenance=false"
          tag: "$CIRCLE_SHA1"
          filters:
            branches:
              only:
                - release/v2/golf
      - aws-ecs/deploy_service_update:
          requires:
            - aws-ecr/build_and_push_image
          auth:
            - aws-cli/setup:
                role_arn: ${GOLF_AWS_ECR_OIDC_ROLE_ARN}
          region: "${GOLF_AWS_DEFAULT_REGION}"
          family: "${GOLF_APP_PREFIX}-service"
          cluster: "${GOLF_APP_CLUSTER}"
          container_image_name_updates: "container=${GOLF_APP_PREFIX}-service,tag=${CIRCLE_SHA1}"
          container_env_var_updates: "container=${GOLF_APP_PREFIX}-service,name=GIT_COMMIT_ID,value=${CIRCLE_SHA1}"
          filters:
            branches:
              only:
                - release/v2/golf

  accordia_build_and_push_image:
    jobs:
      - aws-ecr/build_and_push_image:
          account_id: ${ACCORDIA_AWS_ACCOUNT_ID}
          auth:
            - aws-cli/setup:
                role_arn: ${ACCORDIA_AWS_ECR_OIDC_ROLE_ARN}
          create_repo: true
          dockerfile: Dockerfile
          region: "${ACCORDIA_AWS_DEFAULT_REGION}"
          repo: "${ACCORDIA_APP_PREFIX}"
          extra_build_args: "--provenance=false"
          tag: "$CIRCLE_SHA1"
          filters:
            branches:
              only:
                - release/v2/accordia
      - aws-ecs/deploy_service_update:
          requires:
            - aws-ecr/build_and_push_image
          auth:
            - aws-cli/setup:
                role_arn: ${ACCORDIA_AWS_ECR_OIDC_ROLE_ARN}
          region: "${ACCORDIA_AWS_DEFAULT_REGION}"
          family: "${ACCORDIA_APP_PREFIX}-service"
          cluster: "${ACCORDIA_APP_CLUSTER}"
          container_image_name_updates: "container=${ACCORDIA_APP_PREFIX}-service,tag=${CIRCLE_SHA1}"
          container_env_var_updates: "container=${ACCORDIA_APP_PREFIX}-service,name=GIT_COMMIT_ID,value=${CIRCLE_SHA1}"
          filters:
            branches:
              only:
                - release/v2/accordia

  pgm_build_and_push_image:
    jobs:
      - aws-ecr/build_and_push_image:
          account_id: ${PGM_AWS_ACCOUNT_ID}
          auth:
            - aws-cli/setup:
                role_arn: ${PGM_AWS_ECR_OIDC_ROLE_ARN}
          create_repo: true
          dockerfile: Dockerfile
          region: "${PGM_AWS_DEFAULT_REGION}"
          repo: "${PGM_APP_PREFIX}"
          extra_build_args: "--provenance=false"
          tag: "$CIRCLE_SHA1"
          filters:
            branches:
              only:
                - release/v2/pgm
                - /release/v2/pgm.*/

      - aws-ecs/deploy_service_update:
          requires:
            - aws-ecr/build_and_push_image
          auth:
            - aws-cli/setup:
                role_arn: ${PGM_AWS_ECR_OIDC_ROLE_ARN}
          region: "${PGM_AWS_DEFAULT_REGION}"
          family: "${PGM_APP_PREFIX}-service"
          cluster: "${PGM_APP_CLUSTER}"
          container_image_name_updates: "container=${PGM_APP_PREFIX}-service,tag=${CIRCLE_SHA1}"
          container_env_var_updates: "container=${PGM_APP_PREFIX}-service,name=GIT_COMMIT_ID,value=${CIRCLE_SHA1}"
          filters:
            branches:
              only:
                - release/v2/pgm
                - /release/v2/pgm.*/
