default:
  image: golang:1.24

stages:
  - install
  - test
  - build
  - deploy
  - canary

variables:
  VERSION: "latest"
  PROJECTNAME: "mi-restful-api"
  CI_AWS_ECS_CLUSTER: "my-cluster"
  CI_AWS_ECS_SERVICE: "my-service"
  CI_AWS_ECS_TASK_DEFINITION_FILE: "mi-restful-api-service-revision.json"

install:
  stage: install
  script:
    - echo "setup app variables ${IMAGE_VERSION}"
    - cd src
    - go get

run_test:
  stage: test
  needs:
    - install
  script:
    - echo "test"
    - cd src
    - go test

build_image:
  stage: build
  when: manual
  image: docker:24.0.5
  needs:
    - run_test
  script:
    - echo "build version ${IMAGE_VERSION}"
    - apk add --update py-pip
    - pip install awscli
    - aws sts get-caller-identity
    - aws ecr get-login-password --region ${AWS_DEFAULT_REGION} | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID}.dkr.ecr.ap-northeast-1.amazonaws.com
    - docker build --platform linux/amd64 -t $(PROJECTNAME):$(VERSION) .
    - docker tag $(PROJECTNAME):$(VERSION) $(HOST)/$(PROJECTNAME):$(VERSION)
    - docker push $(ECR_HOST)/$(PROJECTNAME):$(VERSION)

set_ecs:
  stage: deploy
  when: manual
  image: registry.gitlab.com/gitlab-org/cloud-deploy/aws-ecs:latest
  needs:
    - build_image
  script:
    - echo "register-task-definition"
    - aws ecs register-task-definition --cli-input-json file://${CI_AWS_ECS_TASK_DEFINITION_FILE}
    - echo "update-service"
    - aws ecs update-service --cluster ${CI_AWS_ECS_CLUSTER} --service ${CI_AWS_ECS_SERVICE} --task-definition mi-restful-api-service --desired-count 1

set_canary:
  stage: canary
  when: manual
  needs:
    - set_ecs
  script:
    - echo "get current router"
    - echo "set canary"
